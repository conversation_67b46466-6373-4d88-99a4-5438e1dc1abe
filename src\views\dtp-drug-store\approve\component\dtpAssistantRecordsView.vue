<script setup>
import { ref, onMounted, computed, watch, defineEmits } from 'vue';
import { ElMessage } from 'element-plus';
import { getDicts } from '@/api/system/dict/data';
import { ZoomIn } from '@element-plus/icons-vue';
import { getFileInfo } from '@/bpm/src/api/flow';

const props = defineProps({
  // 从全部DTP专员汇总的数据
  recordData: {
    type: Object,
    default: () => ({
      tagList: [],
      assistantWriteList: [],
      scoreAvg: 0
    })
  },
  // 是否可编辑标签 (零售渠道负责人审核时可以继续修改)
  editable: {
    type: Boolean,
    default: false
  },
  // 初始选中的标签
  modelValue: {
    type: Array,
    default: () => []
  },
  disabled: {
    type: Boolean,
    default: false
  },
    // 是否可以编辑标签
    canEditTag: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue', 'change']);

// 标签相关状态
const tagList = ref([]);
const selectedTags = ref(props.modelValue || []);
// 弹窗相关状态
const detailDialogVisible = ref(false);
const currentSpecialist = ref(null);
// 加载状态
const loading = ref(false);

// 计算属性：所有专员的评分平均值
const averageScore = computed(() => {
  return props.recordData.scoreAvg || 0;
});

// 获取标签字典数据
const fetchTagDictData = async () => {
  try {
    loading.value = true;
    const response = await getDicts('dtp_ds_tag');
    if (response && response.data) {
      tagList.value = response.data.map(item => ({
        label: item.dictLabel,
        value: item.dictValue,
        elTagType: item.listClass,
        elTagClass: item.cssClass
      }));
    }
  } catch (error) {
    console.error('获取标签字典失败:', error);
    ElMessage.error('获取标签字典失败');
  } finally {
    loading.value = false;
  }
};

// 获取文件详细信息
const getFileDetails = async (ossIds) => {
  if (!ossIds || !Array.isArray(ossIds)) return [];

  try {
    const filePromises = ossIds.map(async (ossId) => {
      if (typeof ossId === 'object' && ossId.url) {
        // 如果已经是带有URL的对象，直接返回
        return ossId;
      }

      try {
        const response = await getFileInfo(ossId);
        if (response?.data?.rows && response.data.rows[0]) {
          return response.data.rows[0];
        }
        return { ossId, url: '', name: '文件' };
      } catch (error) {
        console.error('获取文件详情失败:', error);
        return { ossId, url: '', name: '文件' };
      }
    });

    return await Promise.all(filePromises);
  } catch (error) {
    console.error('获取文件信息失败:', error);
    return ossIds.map(ossId => ({ ossId, url: '', name: '文件' }));
  }
};

// 处理专员数据，获取每个专员的文件详情
const processSpecialistData = async () => {
  if (!props.recordData) return;

  loading.value = true;
  try {
    // 处理当前专员列表
    if (props.recordData.assistantWriteList) {
      for (const specialist of props.recordData.assistantWriteList) {
        // 处理专员级别的附件
        if (specialist.ossIdList && Array.isArray(specialist.ossIdList)) {
          specialist.ossFileList = await getFileDetails(specialist.ossIdList);
        }

        // 处理药店评分中的附件
        if (specialist.drugScore && Array.isArray(specialist.drugScore)) {
          for (const category of specialist.drugScore) {
            if (category.ossIdList && Array.isArray(category.ossIdList)) {
              category.ossFileList = await getFileDetails(category.ossIdList);
            }
          }
        }
      }
    }

    // 处理历史记录中的专员数据
    if (props.recordData.historyRecords) {
      for (const record of props.recordData.historyRecords) {
        if (record.assistantWriteList) {
          for (const specialist of record.assistantWriteList) {
            // 处理专员级别的附件
            if (specialist.ossIdList && Array.isArray(specialist.ossIdList)) {
              specialist.ossFileList = await getFileDetails(specialist.ossIdList);
            }

            // 处理药店评分中的附件
            if (specialist.drugScore && Array.isArray(specialist.drugScore)) {
              for (const category of specialist.drugScore) {
                if (category.ossIdList && Array.isArray(category.ossIdList)) {
                  category.ossFileList = await getFileDetails(category.ossIdList);
                }
              }
            }
          }
        }
      }
    }
  } catch (error) {
    console.error('处理专员数据失败:', error);
    ElMessage.error('获取文件详情失败');
  } finally {
    loading.value = false;
  }
};

// 处理标签选择变化
const handleTagChange = () => {
  emit('update:modelValue', selectedTags.value);
  emit('change', selectedTags.value);
};

// 显示专员详情
const showSpecialistDetail = (specialist) => {
  currentSpecialist.value = specialist;
  detailDialogVisible.value = true;
};

// 处理文件预览
const previewFile = (url) => {
  if (url) {
    // window.open(url, '_blank');
  }
};

// 获取标签名称
const getTagLabel = (tagCode) => {
  const tag = tagList.value.find(t => t.value === tagCode);
  return tag ? tag.label : tagCode;
};

// 当属性变更时更新内部状态
watch(() => props.modelValue, (newValue) => {
  selectedTags.value = [...newValue];
}, { deep: true });

watch(() => props.recordData, async (newValue) => {
  // 当外部数据变化时，处理文件详情
  if (newValue) {
    // await processSpecialistData();
  }
}, { deep: true, immediate: true });

onMounted(async () => {
  await fetchTagDictData();
  await processSpecialistData();
});
</script>

<template>
  <div class="dtp-assistant-records" v-loading="loading">
    <h2 class="text-xl font-bold">DTP专员资料汇总</h2>
    <!-- 标签&附件上传&药店评分 标题栏 -->
    <div class="section-header">
      <div class="section-title">标签&附件上传&药店评分</div>
      <div class="average-score">平均评分: {{ averageScore }}</div>
    </div>

    <!-- 标签区域 -->
    <div class="tags-section">
      <div class="subsection-title">标签</div>
      <div class="tags-container">
        <!-- 显示标签汇总 -->
        <div v-if="editable" class="tag-edit-mode">
          <el-checkbox-group v-model="selectedTags" @change="handleTagChange" :disabled="props.canEditTag ? false : props.disabled">
            <el-checkbox v-for="tag in tagList" :key="tag.value" :label="tag.value" class="tag-item">
              {{ tag.label }}
            </el-checkbox>
          </el-checkbox-group>
        </div>
        <div v-else class="tag-view-mode">
          <el-tag
            v-for="tagCode in props.recordData.tagList"
            :key="tagCode.tagCode || tagCode"
            class="mx-1 mb-2"
            :type="tagList.find(t => t.value === (tagCode.tagCode || tagCode))?.elTagType || ''"
          >
            {{ getTagLabel(tagCode.tagCode || tagCode) }}
          </el-tag>
          <div v-if="!props.recordData.tagList || props.recordData.tagList.length === 0" class="empty-info">暂无标签</div>
        </div>
      </div>
    </div>

    <!-- DTP专员列表 -->
    <div v-for="(specialist, index) in props.recordData.assistantWriteList" :key="index" class="specialist-section">
      <!-- 专员信息 -->
      <div class="specialist-header">
        <div class="specialist-name">
          {{ specialist.empName }}
          <span class="specialist-status" v-if="specialist.status === '0'">待操作</span>
          <span class="specialist-status" v-else-if="specialist.status === '1'">接受</span>
          <span class="specialist-status" v-else-if="specialist.status === '2'">拒绝</span>
          <span class="specialist-status" v-else-if="specialist.status === '4'">接受</span>
          <span v-if="specialist.status !== '2'" class="specialist-score">评分 {{ specialist.score }}</span>
        </div>
        <div class="view-details">
          <a href="javascript:;" @click="showSpecialistDetail(specialist)">》查看详情</a>
        </div>
      </div>

      <!-- 附件信息 -->
      <div class="attachments-section">
        <div class="subsection-title">附件 ({{ specialist.ossFileList?.length || 0 }})</div>
        <div class="attachments-container">
          <div v-if="!specialist.ossIdList || specialist.ossFileList?.length === 0" class="empty-info">未上传附件</div>
          <div v-else class="attachments-grid">
            <div v-for="(fileInfo, fileIndex) in specialist.ossFileList" :key="fileIndex" class="attachment-item">
              <div class="file-thumbnail">
                <el-image :src="fileInfo.url || ''" fit="cover" :preview-src-list="[fileInfo.url]" class="thumbnail-image">
                  <template #error>
                    <div class="image-error">
                      <el-icon><ZoomIn /></el-icon>
                    </div>
                  </template>
                </el-image>
              </div>
              <div class="file-name text-xs mt-1 truncate">{{ fileInfo.name || '文件' + (fileIndex + 1) }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 历史记录 -->
    <div class="history-section" v-if="props.recordData.historyRecords && props.recordData.historyRecords.length > 0">
      <div class="section-header">
        <div class="section-title">历史记录</div>
      </div>

      <div v-for="(record, recordIndex) in props.recordData.historyRecords" :key="recordIndex" class="history-record">
        <div class="record-header">
          <div class="record-title">标签&附件上传&药店评分</div>
          <div class="average-score">平均评分: {{ record.scoreAvg || 89.00 }}</div>
        </div>

        <div class="tags-section">
          <div class="subsection-title">标签</div>
          <div class="tags-container">
            <div class="tag-chips">
              <el-tag v-for="tagCode in record.tagList" :key="tagCode.tagCode || tagCode" class="mx-1 mb-2" effect="plain">
                {{ getTagLabel(tagCode.tagCode || tagCode) }}
              </el-tag>
            </div>
          </div>
        </div>

        <div v-for="(specialist, specIndex) in record.assistantWriteList" :key="specIndex" class="specialist-section">
          <div class="specialist-header">
            <div class="specialist-name">
              {{ specialist.empName }}
              <span class="specialist-status">{{ specialist.status === '2' ? '拒绝' : '接受' }}</span>
              <span v-if="specialist.status !== '2'" class="specialist-score">评分 {{ specialist.score || 80.00 }}</span>
            </div>
            <div class="view-details">
              <a href="javascript:;" @click="showSpecialistDetail(specialist)">》查看详情</a>
            </div>
          </div>

          <div v-if="specialist.status !== '2'" class="attachments-section">
            <div class="subsection-title">附件 ({{ specialist.ossFileList?.length || 0 }})</div>
            <div class="attachments-container">
              <div v-if="!specialist.ossIdList || specialist.ossFileList?.length === 0" class="empty-info">未上传附件</div>
              <div v-else class="attachments-grid">
                <div v-for="(fileInfo, fileIndex) in specialist.ossFileList" :key="fileIndex" class="attachment-item">
                  <div class="file-thumbnail">
                    <el-image :src="fileInfo.url || ''" fit="cover" class="thumbnail-image" :preview-src-list="[fileInfo.url]">
                      <template #error>
                        <div class="image-error">
                          <el-icon><ZoomIn /></el-icon>
                        </div>
                      </template>
                    </el-image>
                  </div>
                  <div class="file-name text-xs mt-1 truncate">{{ fileInfo.name || '文件' + (fileIndex + 1) }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- DTP专员详情弹窗 -->
    <el-dialog v-model="detailDialogVisible" title="药店评分详情" width="800px" destroy-on-close>
      <div v-if="currentSpecialist" class="specialist-detail">
        <!-- 评分详情 -->
        <div class="score-detail">
          <div class="score-categories">
            <el-collapse accordion>
              <el-collapse-item v-for="(category, catIndex) in currentSpecialist.drugScore" :key="catIndex">
                <template #title>
                  <div class="flex justify-between w-full">
                    <span>{{ category.scoreItemName }}</span>
                    <span v-if="category.ossFileList && category.ossFileList.length > 0" class="text-sm text-gray-500">
                      ({{ category.ossFileList.length }}张图片)
                    </span>
                  </div>
                </template>
                <div class="category-content">
                  <!-- 评分项详情 -->
                  <div class="score-points">
                    <div v-for="(item, itemIndex) in category.scoreItem" :key="itemIndex" class="score-point">
                      <div class="flex justify-between py-2 border-b">
                        <span class="score-point-title">{{ item.scorePointsTitle }}</span>
                        <span class="score-point-value inline-block min-w-[60px] text-right">
                          {{ item.inputScore || '--' }} / {{ item.score || item.inputScore || 0 }}
                        </span>
                      </div>
                    </div>
                  </div>

                  <!-- 分类级别的附件展示 -->
                  <div v-if="category.ossFileList && category.ossFileList.length > 0" class="category-attachments mt-4">
                    <div class="subsection-title">{{ category.scoreItemName }}相关图片</div>
                    <div class="attachments-grid">
                      <div v-for="(fileInfo, fileIndex) in category.ossFileList" :key="fileIndex" class="attachment-item">
                        <div class="file-thumbnail">
                          <el-image
                            :src="fileInfo.url || ''"
                            fit="cover"
                            class="detail-image"
                            :preview-src-list="[fileInfo.url]"
                            @click="previewFile(fileInfo.url)"
                          >
                            <template #error>
                              <div class="image-error">
                                <el-icon><ZoomIn /></el-icon>
                              </div>
                            </template>
                          </el-image>
                        </div>
                        <div class="file-name text-xs mt-1 truncate">{{ fileInfo.name || '图片' + (fileIndex + 1) }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.dtp-assistant-records {
  padding: 16px 0;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #ebeef5;
    margin-bottom: 16px;

    .section-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }

    .average-score {
      font-size: 18px;
      color: #F56C6C;
      font-weight: 600;
    }
  }

  .subsection-title {
    font-size: 16px;
    font-weight: 600;
    margin: 16px 0;
    color: #333;
  }

  .tags-section {
    margin-bottom: 24px;

    .tags-container {
      .tag-edit-mode {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;

        .tag-item {
          margin-bottom: 8px;
        }
      }

      .tag-view-mode {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
      }

      .tag-chips {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
      }
    }
  }

  .specialist-section {
    margin-bottom: 24px;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 16px;

    .specialist-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;

      .specialist-name {
        font-size: 16px;
        font-weight: 600;

        .specialist-status {
          margin-left: 10px;
          font-weight: normal;

          &.accept {
            color: #67C23A;
          }

          &.refuse {
            color: #F56C6C;
          }
        }

        .specialist-score {
          margin-left: 10px;
          color: #67C23A;
        }
      }

      .view-details {
        a {
          color: #409EFF;
          text-decoration: none;
        }
      }
    }

    .attachments-section {
      margin-top: 16px;

      .attachments-grid {
        display: grid;
        grid-template-columns: repeat(5, 1fr);
        gap: 12px;

        .attachment-item {
          width: 100%;

          .file-thumbnail {
            width: 100%;
            height: 100px;
            border-radius: 4px;
            overflow: hidden;
            border: 1px solid #ebeef5;

            .thumbnail-image {
              width: 100%;
              height: 100%;
              display: block;
            }

            .image-error {
              display: flex;
              justify-content: center;
              align-items: center;
              height: 100%;
              background-color: #f5f7fa;
              color: #909399;
            }
          }

          .file-name {
            margin-top: 4px;
            text-align: center;
            color: #606266;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-size: 12px;
          }
        }
      }
    }
  }

  .history-section {
    margin-top: 32px;

    .history-record {
      margin-bottom: 24px;
    }

    .attachments-grid {
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      gap: 12px;

      .attachment-item {
        .file-thumbnail {
          width: 100%;
          height: 100px;
          border-radius: 4px;
          overflow: hidden;
          border: 1px solid #ebeef5;

          .el-image {
            width: 100%;
            height: 100%;
            display: block;
          }
        }
      }
    }
  }

  .empty-info {
    color: #909399;
    font-size: 14px;
    text-align: center;
    padding: 16px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }

  .specialist-status {
    &.accept {
      color: #67C23A;
    }

    &.refuse {
      color: #F56C6C;
    }
  }

  .specialist-detail {
    .category-content {
      .category-attachments {
        .subsection-title {
          font-size: 14px;
          font-weight: 600;
          margin-bottom: 12px;
          color: #333;
        }

        .attachments-grid {
          display: grid;
          grid-template-columns: repeat(4, 1fr);
          gap: 12px;

          .attachment-item {
            .file-thumbnail {
              width: 100%;
              height: 80px;
              border-radius: 4px;
              overflow: hidden;
              border: 1px solid #ebeef5;
              cursor: pointer;

              .detail-image {
                width: 100%;
                height: 100%;
                display: block;
              }

              .image-error {
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100%;
                background-color: #f5f7fa;
                color: #909399;
              }
            }

            .file-name {
              margin-top: 4px;
              text-align: center;
              color: #606266;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              font-size: 11px;
            }
          }
        }
      }
    }

    .file-detail {
      .file-item {
        .detail-image {
          width: 100%;
          height: 120px;
          border-radius: 4px;
          border: 1px solid #ebeef5;
          display: block;
        }
      }
    }
  }
}
</style>
