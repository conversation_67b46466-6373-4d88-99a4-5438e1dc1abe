<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { updateJurisdiction, getDepartmentList } from '../api'
import { getDepListApi, getDeptListApi } from '@/api/structure/organization/index'
import { listData } from '@/api/system/dict/data/index'
import type { DtpJurBo, SysDeptUserBo } from '../api'
import { Warning } from '@element-plus/icons-vue'

interface PostOption {
  postId: number
  postName: string
  postCode: string
}

interface ProductOption {
  productCode: string
  productName: string
}

interface JurTypeOption {
  value: string
  label: string
}

interface ApiResponse<T> {
  code: number
  msg?: string
  data: T
}

const props = defineProps<{
  modelValue: boolean
  editData?: DtpJurBo
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}>()

const appCode = import.meta.env.VITE_APP_CODE
const tenantId = import.meta.env.VITE_TENANT_ID

const addFormRef = ref()
const submitLoading = ref(false)
const num = ref(0)
const butType = ref<string>('')
const defaultExpandedArr = ref<string[]>([])
const treeData = ref<SysDeptUserBo[]>([])

// 数据源
const deptTreeData = ref<SysDeptUserBo[]>([])
const postOptions = ref<PostOption[]>([])
const productOptions = ref<ProductOption[]>([])
const jurTypeOptions = ref<JurTypeOption[]>([])
const resetTree = ref(true)
const lazy = ref(true)

const listDeptForm = reactive({
  status: '0',
  deptName: '',
  appCode,
  tenantId,
  parentId: undefined
})

// 表单数据
const formData = reactive<DtpJurBo>({
  deptId: undefined,
  postId: undefined,
  userName: '',
  jurType: '',
  productCodeList: [],
  operateType: 'insert',
  deptCode: '',
  postCode: ''
})

// 表单验证规则
const rules = {
  deptId: [{ required: true, message: '请选择部门', trigger: 'change' }],
  postCode: [{ required: true, message: '请选择岗位', trigger: 'change' }],
  // userName: [{ required: true, message: '请输入辖区负责人', trigger: 'blur' }],
  jurType: [{ required: true, message: '请选择辖区类型', trigger: 'change' }],
  productCodeList: [{ required: true, message: '请选择产品', trigger: 'change' }]
}

// Watch for editData changes
watch(() => props.editData, (newVal) => {
  if (newVal) {
    Object.assign(formData, {
      ...newVal,
      operateType: 'update'
    })
    // If we have deptId, fetch the post list
    if (newVal.deptId) {
      getPostList(newVal.deptId)
    }
  }
}, { immediate: true })

// 获取部门树形数据
const getDeptTreeData = async () => {
  try {
    const res = await getDepartmentList({ appCode: 'mdm' })
    if (res.code === 200) {
      deptTreeData.value = res.data
    } else {
      ElMessage.error(res.msg || '获取部门数据失败')
    }
  } catch (error) {
    console.error('Failed to fetch department data:', error)
    ElMessage.error('获取部门数据失败')
  }
}

// 获取岗位列表
const getPostList = async (deptId: number | undefined, deptName = '') => {
  if (!deptId) return
  try {
    const params: any = {
      deptId,
      pageSize: 100,
      pageNum: 1,
      deptName
    }
    const res = await getDepListApi(params, '')
    if (res.code === 200) {
      postOptions.value = res.data.rows?.map((el: any) => ({
        postName: el.postName,
        postCode: el.postCode,
        postId: el.postId
      })) || []
    } else {
      ElMessage.error(res.msg || '获取岗位数据失败')
    }
  } catch (error) {
    console.error('Failed to fetch post data:', error)
    ElMessage.error('获取岗位数据失败')
  }
}

// 获取产品列表
const getProductList = async () => {
  try {
    const res = await listData({
      pageNum: 1,
      pageSize: 100,
      dictType: 'dtp_jur_product',
      dictName: '',
      dictLabel: ''
    })
    if (res.code === 200) {
      productOptions.value = res.rows?.map(item => ({
        productCode: item.dictValue,
        productName: item.dictLabel
      })) || []
    } else {
      ElMessage.error(res.msg || '获取产品数据失败')
    }
  } catch (error) {
    console.error('Failed to fetch product data:', error)
    ElMessage.error('获取产品数据失败')
  }
}

// 获取辖区类型列表
const getJurTypeList = async () => {
  try {
    const res = await listData({
      pageNum: 1,
      pageSize: 100,
      dictType: 'dtp_jur_type',
      dictName: '',
      dictLabel: ''
    })
    if (res.code === 200) {
      jurTypeOptions.value = res.rows?.map(item => ({
        value: item.dictValue,
        label: item.dictLabel
      })) || []
    } else {
      ElMessage.error(res.msg || '获取辖区类型数据失败')
    }
  } catch (error) {
    console.error('Failed to fetch jurisdiction type data:', error)
    ElMessage.error('获取辖区类型数据失败')
  }
}

// 部门树懒加载
const load = async (node: any, resolve: any) => {
  if (node.level === 0) {
    const res = await getDeptListApi({ status: 0 }, '')
    resolve(res.data)
  } else {
    const { data } = node
    if (data.children && data.children.length !== 0) {
      resolve(data.children)
    } else {
      const res = await getDeptListApi({ status: 0, parentId: data.deptId }, '')
      res.data = res.data.map((el: any) => ({
        ...el,
        isLeaf: !el.isChildren
      }))
      resolve(res.data)
    }
  }
}

// 搜索部门
let timerF: ReturnType<typeof setTimeout> | null = null
const filterNode = async (deptName: string) => {
  if (deptName && deptName.length !== 0) {
    formData.deptId = undefined
    if (timerF) {
      clearTimeout(timerF)
      timerF = null
    }
    timerF = setTimeout(async () => {
      const res = await getDeptListApi({ deptName, status: 0 }, '')
      lazy.value = false
      defaultExpandedArr.value = []
      deptTreeData.value = res.data || []
      deptTreeData.value = res.data?.map((ele: any) => ({
        ...ele,
        isLeaf: !ele.isChildren
      }))
      getDefaultExpanded(deptTreeData.value)
    }, 1000)
  }
}

// 获取默认展开的节点
const getDefaultExpanded = (item: any[]) => {
  item.forEach((i) => {
    if (i.children?.length) {
      defaultExpandedArr.value.push(i.deptId)
      getDefaultExpanded(i.children)
    } else {
      if (!i.isChildren) {
        i.isLeaf = true
      }
      return
    }
  })
}

// 部门节点点击事件
const handleNodeClick = async (row: SysDeptUserBo) => {
  formData.deptId = row.deptId
  formData.deptCode = row.deptCode
  formData.postCode = '' // 清空岗位选择
  await getPostList(row.deptId)
}

// 岗位选择变化事件
const handlePostChange = (value: string) => {
  const selectedPost = postOptions.value.find(item => item.postCode === value)
  if (selectedPost) {
    formData.postId = selectedPost.postId
  }
}

const handleClose = () => {
  emit('update:modelValue', false)
  // 重置表单
  if (addFormRef.value) {
    addFormRef.value.resetFields()
  }
}

const handleSubmit = async () => {
  if (!addFormRef.value) return

  await addFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      submitLoading.value = true
      try {
        const res = await updateJurisdiction({
          ...formData,
          operateType: props.editData ? 'update' : 'insert'
        })
        if (res.code === 200) {
          ElMessage.success(props.editData ? '编辑成功' : '新增成功')
          handleClose()
          emit('success')
        } else {
          ElMessage.error(res.msg || (props.editData ? '编辑失败' : '新增失败'))
        }
      } catch (error) {
        console.error('Failed to submit jurisdiction:', error)
        ElMessage.error(props.editData ? '编辑失败' : '新增失败')
      } finally {
        submitLoading.value = false
      }
    }
  })
}

// 初始化数据
onMounted(() => {
  getDeptTreeData()
  getProductList()
  getJurTypeList()
})
</script>

<template>
  <el-drawer
    :model-value="modelValue"
    @update:model-value="(val: boolean) => emit('update:modelValue', val)"
    :title="props.editData ? '编辑辖区' : '新增辖区'"
    size="500px"
    :destroy-on-close="true"
    @close="handleClose"
  >
    <el-form ref="addFormRef" :model="formData" :rules="rules" label-width="100px" label-position="top">
      <el-form-item label="部门" prop="deptName" v-if="props.editData">
        <el-input v-model="formData.deptName" disabled placeholder="-" style="width: 240px" />
      </el-form-item>
      <el-form-item label="部门" prop="deptId" v-else>
        <el-tree-select
          v-if="resetTree"
          v-model="formData.deptId"
          :data="deptTreeData"
          style="width:100%;"
          placement="bottom-end"
          :default-expanded-keys="defaultExpandedArr"
          :lazy="lazy"
          node-key="deptId"
          render-after-expand
          :filter-method="filterNode"
          :load="load"
          filterable
          :props="{
            label: 'deptName',
            value: 'deptId',
            children: 'childNodes',
            isLeaf: 'isLeaf'
          }"
          clearable
          check-strictly
          :disabled="butType === 'edit'"
          accordion
          placeholder="请选择部门"
          @node-click="handleNodeClick"
        />
      </el-form-item>
      <el-form-item label="岗位" prop="postCode">
        <template v-if="props.editData">
          <el-input v-model="formData.postName" disabled placeholder="-" style="width: 240px" />
        </template>
        <template v-else>
          <el-select
            v-model="formData.postCode"
            filterable
            :reserve-keyword="false"
            placeholder="请选择岗位"
            clearable
            style="width:100%;"
            :disabled="!formData.deptId"
            @change="handlePostChange"
          >
            <el-option v-for="dict in postOptions" :key="dict.postCode" :label="dict.postName" :value="dict.postCode" />
          </el-select>
        </template>
      </el-form-item>
      <!-- <el-form-item label="辖区负责人" prop="userName">
        <template v-if="props.editData">
          <el-input v-model="formData.userName" disabled placeholder="-" style="width: 240px" />
        </template>
        <template v-else>
          <el-input v-model="formData.userName" placeholder="请输入辖区负责人" style="width: 240px" />
        </template>
      </el-form-item> -->
      <el-form-item label="辖区类型" prop="jurType">
        <template v-if="props.editData">
          <el-input :value="jurTypeOptions.find(item => item.value === formData.jurType)?.label || '-'" disabled style="width: 240px" />
        </template>
        <template v-else>
          <el-select v-model="formData.jurType" placeholder="请选择辖区类型" style="width: 100%">
            <el-option v-for="item in jurTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </template>
      </el-form-item>
      <el-form-item label="产品" prop="productCodeList">
        <el-select v-model="formData.productCodeList" multiple placeholder="请选择产品" style="width: 100%">
          <el-option v-for="item in productOptions" :key="item.productCode" :label="item.productName" :value="item.productCode" />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="flex justify-end">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确定</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<style scoped>
.el-drawer__body {
  padding: 0;
}
.pop-item {
  position: absolute;
  left: 68px;
  top: 73px;
}
</style>
