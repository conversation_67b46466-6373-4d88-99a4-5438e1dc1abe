import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { StatisticsQuery } from './types';

// 当前覆盖统计
export const getCurrentCoverApi = (query: StatisticsQuery): AxiosPromise<any> => {
  return request({
    url: `/hcd/statistics/hcd`,
    method: 'get',
    params: query
  });
};

// 新增统计
export const getAddApi = (query: StatisticsQuery): AxiosPromise<any> => {
  return request({
    url: `/hcd/statistics/add`,
    method: 'get',
    params: query
  });
};

// 审核状态统计
export const getApproveApi = (query: StatisticsQuery): AxiosPromise<any> => {
  return request({
    url: `/hcd/statistics/task`,
    method: 'get',
    params: query
  });
};
