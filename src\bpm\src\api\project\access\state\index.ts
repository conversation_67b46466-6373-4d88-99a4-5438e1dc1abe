import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { AccessStateQuery, MembersVO } from './types';

// 准入状态统计
export const getCountApi = (query: any): AxiosPromise<any> => {
  return request({
    // url: `/hcd/project/statistics/${id}`,
    url: '/hcd/project/statisticsQuery',
    method: 'get',
    params: query
  });
};

// 准入状态筛选条件
export const getAccessfilterApi = () => {
  return request({
    url: '/hcd/institution/access/get/filter',
    method: 'post',
    data: {}
  });
};

// 机构准入状态列表
export const getAccessManageListApi = (query: any): AxiosPromise<any> => {
  return request({
    url: '/hcd/project/list',
    method: 'get',
    params: query
  });
};

// 详情页回显信息

export const getInsinfoApi = (query: any): AxiosPromise<any> => {
  return request({
    url: '/hcd/institution/insinfo',
    method: 'get',
    params: query
  });
};
// 项目成员查询
export const getMembersApi = (parentId: any): AxiosPromise<MembersVO> => {
  return request({
    url: `/hcd/project/members/${parentId}`,
    method: 'get',
    params: {}
  });
};

// 审批日志
export const getTrendsApi = (query: any): AxiosPromise<any> => {
  return request({
    url: '/hcd/project/log',
    method: 'get',
    params: query
  });
};

// 提交申请表单
export const getApplicationSubmitApi = (data: any) => {
  return request({
    url: '/hcd/task/submit',
    method: 'post',
    data: data
  });
};
// 撤回申请
export const getApplicationCancelApi = (data: any) => {
  return request({
    url: '/hcd/task/cancel',
    method: 'put',
    data: data
  });
};

// 申请单回显
export const getApplicationShowApi = (query: any): AxiosPromise<any> => {
  return request({
    url: '/hcd/task/detail',
    method: 'get',
    params: query
  });
};
//  业务流程日志展示
export const getQueryLogApi = (query: any): AxiosPromise<MembersVO> => {
  return request({
    url: '/hcd/bpm/log',
    method: 'get',
    params: query
  });
};

// 业务流程展示
export const getQueryNodeApi = (query: any): AxiosPromise<MembersVO> => {
  return request({
    url: '/hcd/bpm/node',
    method: 'get',
    params: query
  });
};

// 文件下载 download("/hcd/institution/access/excel/export"
export const downloadApi = (data: any) => {
  return request({
    url: '/hcd/project/export',
    method: 'post',
    data: data
  });
};

// 负责人岗
export const getQuerySubordinateApi = (query: any): AxiosPromise<MembersVO> => {
  return request({
    url: '/plt/post/subordinate',
    method: 'get',
    params: query
  });
};
