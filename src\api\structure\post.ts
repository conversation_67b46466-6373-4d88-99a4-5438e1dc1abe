import request from '@/utils/request';
import { AxiosPromise } from 'axios';

export const postPageApi = (query: any, version: any) => {
  return request({
    url: `/hr/post${version ? '-version' : ''}/page`,
    method: 'get',
    params: query
  });
};

export const getDepListApi = (query: any, version: any): AxiosPromise<any> => {
  return request({
    url: `/hr/user-dept${version ? '-version' : ''}/list-by-dept`,
    method: 'get',
    params: query
  });
};

export const postInsertApi = (data: any, version: any) => {
  return request({
    url: `/hr/post${version ? '-version' : ''}/insert`,
    method: 'post',
    data
  });
};
export const postUpdateApi = (data: any, version: any) => {
  return request({
    url: `/hr/post${version ? '-version' : ''}/update`,
    method: 'post',
    data
  });
};

export const postLevel = () => {
  return request({
    url: '/plt/dict/data/list?dictType=post_level',
    method: 'get'
  });
};
