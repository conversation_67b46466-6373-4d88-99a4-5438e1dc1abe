<template>
  <div class="leftTree">
    <el-input v-model="queryParams.deptName" placeholder="请搜索部门" @keyup.enter="inputChange">
      <template #suffix>
        <el-icon @click="inputChange"><Search /></el-icon>
      </template>
    </el-input>
    <el-tree
      v-if="isShow"
      ref="treeRef"
      class="filter-tree"
      :data="Depdata"
      :props="defaultProps"
      :load="loadNode"
      :lazy="lazy"
      :default-expanded-keys="defaultExpandedArr"
      node-key="deptId"
      render-after-expand
      highlight-current
      :filter-node-method="filterNode"
      :current-node-key="currentNodeKey"
      @node-click="handleNodeClick"
      @node-expand="handleNodeExpand"
    />
    <div v-else v-loading="true" style="width:100%;height:500px;"></div>
  </div>
</template>
<script lang="ts" setup>
import { Search } from '@element-plus/icons-vue'
import { ElTree } from 'element-plus'
import { getDepartmentList  } from './../api';
import dayjs from 'dayjs';

const appCode = import.meta.env.VITE_APP_CODE;
const tenantId = import.meta.env.VITE_TENANT_ID;
const defaultProps = {
  label: 'deptName',
  isLeaf: 'isLeaf',
}
const props = defineProps({
  version: {
    type: String,
    default: ''
  },

})
interface Tree {
  [key: string]: any
}
const treeRef = ref<InstanceType<typeof ElTree>>()

  const currentNodeKey = ref();  // 新增: 控制当前选中节点

// 新增: 重置选中状态方法
const resetSelection = () => {
  if (treeRef.value) {
    currentNodeKey.value = null;
    treeRef.value.setCurrentKey(null,false);
  }
  // TODO:收起所有节点
};

const  Depdata= ref([
])
const queryParams = ref ({
  status:'0',
  deptName: '',
  appCode: 'athena_hcd',
  tenantId: '000020',
  // parentId:'0' // 过滤的时候不
  sysEnableFlag: '0',
  version: dayjs(new Date()).format('YYYYMM')
})
const num = ref(0)
const lazy = ref(true)
const isShow = ref(true)
const isClearVal = ref(false)
const defaultExpandedArr = ref([])
const emits = defineEmits(['handleNodeClick','getList','allList'])
const inputChange = () => {
  isShow.value = false
  if (queryParams.value.deptName) {
    delete queryParams.value.parentId
    lazy.value = false
  } else {
    isClearVal.value = true
    // queryParams.value.parentId = '0'
    lazy.value = true
    emits('getList',)
  }
  setTimeout(async () => {
    const versionFlag = true
    //掉接口
    const res = await getDepartmentList(queryParams.value)
      Depdata.value = res.data || []
      emits("allList",res.data)
      defaultExpandedArr.value = []
      if (queryParams.value.deptName) {
        getDefaultExpanded(Depdata.value)
      }
      isShow.value = true

  }, 0)
}
const filterNode = (value: string, data: Tree) => {
  if (!value) return true
  return data.label.includes(value)
}

//懒加载
const loadNode = async (node: any, resolve: any) => {
  if (node.isLeaf) return resolve([])
  num.value++
  const { level,data } = node
   if(num.value > 1) {
    queryParams.value.parentId = data?.deptId
  }
  if (isClearVal.value ) {
    // queryParams.value.parentId = '0'
    Depdata.value=[]
   }
    const res = await getDepartmentList(queryParams.value)
    res.data.forEach((i: any) => {
      console.log(44);

      i.isLeaf = !i.isChildren
    });
   await resolve(res.data || [])
   emits("allList",res.data)
    isClearVal.value = false
}

const handleNodeClick = (TreeNode:any,node:any ) => {
  emits('handleNodeClick',TreeNode)
}

const getDefaultExpanded = (item:any) => {

    item.forEach( async (i) => {
      if (i.children?.length) {
        defaultExpandedArr.value.push(i.deptId)
        getDefaultExpanded(i.children)
      } else {
        if (i.isChildren) {
          i.children = [{}]
       }
        return
     }
    })
}
const handleNodeExpand = async (data: any) => {
  if (!queryParams.value.deptName) {
    return
  }
  if (defaultExpandedArr.value.includes(data.deptId)) {
    return
  }
  const versionFlag = true

  const res = versionFlag ? await deptListApi({parentId:data.deptId,status:'0',appCode,tenantId,version:props.version}):  await deptListApi({parentId:data.deptId,status:'0',appCode,tenantId,  version:props.version})

  res.data.forEach((i: any) => {
    i.isLeaf = !i.isChildren
    if (i.isChildren) {
      i.children = [{}]
    }
  });
  data.children =  res.data
  emits("allList",res.data)
  console.log( res.data);
}

defineExpose({
  handleNodeClick,
  resetSelection
})
</script>

<style lang="scss" scoped>
.leftTree {
  .filter-tree {
    margin-top: 16px;
    overflow: auto;
    min-width:252px;
    width:252px;
  }
  ::v-deep .el-tree>.el-tree-node {
    min-width: 100%;
    display:inline-block;
  }
  ::v-deep .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
    color: #2551F2;
  }
}
</style>
