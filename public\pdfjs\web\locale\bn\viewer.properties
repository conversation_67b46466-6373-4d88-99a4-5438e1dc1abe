# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=পূর্ববর্তী পাতা
previous_label=পূর্ববর্তী
next.title=পরবর্তী পাতা
next_label=পরবর্তী

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=পাতা
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages={{pagesCount}} এর
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pagesCount}} এর {{pageNumber}})

zoom_out.title=ছোট আকারে প্রদর্শন
zoom_out_label=ছোট আকারে প্রদর্শন
zoom_in.title=বড় আকারে প্রদর্শন
zoom_in_label=বড় আকারে প্রদর্শন
zoom.title=বড় আকারে প্রদর্শন
presentation_mode.title=উপস্থাপনা মোডে স্যুইচ করুন
presentation_mode_label=উপস্থাপনা মোড
open_file.title=ফাইল খুলুন
open_file_label=খুলুন
print.title=মুদ্রণ
print_label=মুদ্রণ
download.title=ডাউনলোড
download_label=ডাউনলোড
bookmark.title=বর্তমান অবস্থা (অনুলিপি অথবা নতুন উইন্ডো তে খুলুন)
bookmark_label=বর্তমান অবস্থা

# Secondary toolbar and context menu
tools.title=টুল
tools_label=টুল
first_page.title=প্রথম পাতায় যাও
first_page.label=প্রথম পাতায় যাও
first_page_label=প্রথম পাতায় যাও
last_page.title=শেষ পাতায় যাও
last_page.label=শেষ পাতায় যাও
last_page_label=শেষ পাতায় যাও
page_rotate_cw.title=ঘড়ির কাঁটার দিকে ঘোরাও
page_rotate_cw.label=ঘড়ির কাঁটার দিকে ঘোরাও
page_rotate_cw_label=ঘড়ির কাঁটার দিকে ঘোরাও
page_rotate_ccw.title=ঘড়ির কাঁটার বিপরীতে ঘোরাও
page_rotate_ccw.label=ঘড়ির কাঁটার বিপরীতে ঘোরাও
page_rotate_ccw_label=ঘড়ির কাঁটার বিপরীতে ঘোরাও

cursor_text_select_tool.title=লেখা নির্বাচক টুল সক্রিয় করুন
cursor_text_select_tool_label=লেখা নির্বাচক টুল
cursor_hand_tool.title=হ্যান্ড টুল সক্রিয় করুন
cursor_hand_tool_label=হ্যান্ড টুল

scroll_vertical.title=উলম্ব স্ক্রলিং ব্যবহার করুন
scroll_vertical_label=উলম্ব স্ক্রলিং
scroll_horizontal.title=অনুভূমিক স্ক্রলিং ব্যবহার করুন
scroll_horizontal_label=অনুভূমিক স্ক্রলিং
scroll_wrapped.title=Wrapped স্ক্রোলিং ব্যবহার করুন
scroll_wrapped_label=Wrapped স্ক্রোলিং

spread_none.title=পেজ স্প্রেডগুলোতে যোগদান করবেন না
spread_none_label=Spreads নেই
spread_odd_label=বিজোড় Spreads
spread_even_label=জোড় Spreads

# Document properties dialog box
document_properties.title=নথি বৈশিষ্ট্য…
document_properties_label=নথি বৈশিষ্ট্য…
document_properties_file_name=ফাইলের নাম:
document_properties_file_size=ফাইলের আকার:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} কেবি ({{size_b}} বাইট)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} এমবি ({{size_b}} বাইট)
document_properties_title=শিরোনাম:
document_properties_author=লেখক:
document_properties_subject=বিষয়:
document_properties_keywords=কীওয়ার্ড:
document_properties_creation_date=তৈরির তারিখ:
document_properties_modification_date=পরিবর্তনের তারিখ:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=প্রস্তুতকারক:
document_properties_producer=পিডিএফ প্রস্তুতকারক:
document_properties_version=পিডিএফ সংষ্করণ:
document_properties_page_count=মোট পাতা:
document_properties_page_size=পাতার সাইজ:
document_properties_page_size_unit_inches=এর মধ্যে
document_properties_page_size_unit_millimeters=mm
document_properties_page_size_orientation_portrait=উলম্ব
document_properties_page_size_orientation_landscape=অনুভূমিক
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=লেটার
document_properties_page_size_name_legal=লীগাল
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} × {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} × {{height}} {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=Fast Web View:
document_properties_linearized_yes=হ্যাঁ
document_properties_linearized_no=না
document_properties_close=বন্ধ

print_progress_message=মুদ্রণের জন্য নথি প্রস্তুত করা হচ্ছে…
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=বাতিল

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=সাইডবার টগল করুন
toggle_sidebar_notification.title=সাইডবার টগল (নথিতে আউটলাইন/এটাচমেন্ট রয়েছে)
toggle_sidebar_label=সাইডবার টগল করুন
document_outline.title=নথির আউটলাইন দেখাও (সব আইটেম প্রসারিত/সঙ্কুচিত করতে ডবল ক্লিক করুন)
document_outline_label=নথির রূপরেখা
attachments.title=সংযুক্তি দেখাও
attachments_label=সংযুক্তি
thumbs.title=থাম্বনেইল সমূহ প্রদর্শন করুন
thumbs_label=থাম্বনেইল সমূহ
findbar.title=নথির মধ্যে খুঁজুন
findbar_label=খুঁজুন

# LOCALIZATION NOTE (page_canvas): "{{page}}" will be replaced by the page number.
page_canvas=পাতা {{page}}

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=পাতা {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas={{page}} পাতার থাম্বনেইল

# Find panel button title and messages
find_input.title=খুঁজুন
find_input.placeholder=নথির মধ্যে খুঁজুন…
find_previous.title=বাক্যাংশের পূর্ববর্তী উপস্থিতি অনুসন্ধান
find_previous_label=পূর্ববর্তী
find_next.title=বাক্যাংশের পরবর্তী উপস্থিতি অনুসন্ধান
find_next_label=পরবর্তী
find_highlight=সব হাইলাইট করা হবে
find_match_case_label=অক্ষরের ছাঁদ মেলানো
find_entire_word_label=সম্পূর্ণ শব্দ
find_reached_top=পাতার শুরুতে পৌছে গেছে, নীচ থেকে আরম্ভ করা হয়েছে
find_reached_bottom=পাতার শেষে পৌছে গেছে, উপর থেকে আরম্ভ করা হয়েছে
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ plural(total) ]}
find_match_count[one]={{total}} এর {{current}} মিল
find_match_count[two]={{total}} এর {{current}} মিল
find_match_count[few]={{total}} এর {{current}} মিল
find_match_count[many]={{total}} এর {{current}} মিল
find_match_count[other]={{total}} এর {{current}} মিল
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ plural(limit) ]}
find_match_count_limit[zero]={{limit}} এর বেশি মিল
find_match_count_limit[one]={{limit}} এর বেশি মিল
find_match_count_limit[two]={{limit}} এর বেশি মিল
find_match_count_limit[few]={{limit}} এর বেশি মিল
find_match_count_limit[many]={{limit}} এর বেশি মিল
find_match_count_limit[other]={{limit}} এর বেশি মিল
find_not_found=বাক্যাংশ পাওয়া যায়নি

# Error panel labels
error_more_info=আরও তথ্য
error_less_info=কম তথ্য
error_close=বন্ধ
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (build: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=বার্তা: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=Stack: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=নথি: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=লাইন: {{line}}
rendering_error=পাতা উপস্থাপনার সময় ত্রুটি দেখা দিয়েছে।

# Predefined zoom values
page_scale_width=পাতার প্রস্থ
page_scale_fit=পাতা ফিট করুন
page_scale_auto=স্বয়ংক্রিয় জুম
page_scale_actual=প্রকৃত আকার
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error_indicator=ত্রুটি

loading_error=পিডিএফ লোড করার সময় ত্রুটি দেখা দিয়েছে।
invalid_file_error=অকার্যকর অথবা ক্ষতিগ্রস্ত পিডিএফ ফাইল।
missing_file_error=নিখোঁজ PDF ফাইল।
unexpected_response_error=অপ্রত্যাশীত সার্ভার প্রতিক্রিয়া।

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.
annotation_date_string={{date}}, {{time}}

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 – Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} টীকা]
password_label=পিডিএফ ফাইলটি ওপেন করতে পাসওয়ার্ড দিন।
password_invalid=ভুল পাসওয়ার্ড। অনুগ্রহ করে আবার চেষ্টা করুন।
password_ok=ঠিক আছে
password_cancel=বাতিল

printing_not_supported=সতর্কতা: এই ব্রাউজারে মুদ্রণ সম্পূর্ণভাবে সমর্থিত নয়।
printing_not_ready=সতর্কীকরণ: পিডিএফটি মুদ্রণের জন্য সম্পূর্ণ লোড হয়নি।
web_fonts_disabled=ওয়েব ফন্ট নিষ্ক্রিয়: সংযুক্ত পিডিএফ ফন্ট ব্যবহার করা যাচ্ছে না।
