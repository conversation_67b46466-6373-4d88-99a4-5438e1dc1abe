<template>
  <div class="business-record">
    <h3 class="section-title">产品备案清单</h3>

    <!-- 筛选区域 -->
    <div class="filter-container">
      <el-form :model="queryParams" :inline="true">
        <el-form-item label="备案状态">
          <el-select v-model="queryParams.status" placeholder="请选择备案状态" clearable>
            <el-option v-for="dict in statusOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="品牌">
          <el-select v-model="queryParams.brand" placeholder="请选择品牌" clearable>
            <el-option v-for="item in brandOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table :data="filteredTableData" border :header-cell-style="{background:'#f5f7fa'}" size="small" style="min-width: 100%; max-width: 1000px;">
        <el-table-column prop="businessUnit" label="事业部" min-width="150" fixed="left" />
        <el-table-column prop="recordId" label="备案ID" min-width="150" fixed="left" />
        <el-table-column prop="productName" label="品规名称" min-width="250" fixed="left" />
        <el-table-column prop="productCode" label="品规编码" min-width="150" />
        <el-table-column prop="beiTongCode" label="品规倍通编码" min-width="180" />
        <el-table-column prop="category" label="产品分类" min-width="150" />
        <el-table-column prop="brand" label="品牌" min-width="120" />
        <el-table-column prop="deliveryMode" label="终端配送方式" min-width="160" />
        <el-table-column prop="recordMonth" label="备案月份" min-width="120" />
        <el-table-column prop="recordStatus" label="备案状态" min-width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.recordStatus)">
              {{ statusMap[row.recordStatus] }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="versionStatus" label="定版状态" min-width="100">
          <template #default="{ row }">
            <el-tag :type="getVersionStatusType(row.versionStatus)">
              {{ versionStatusMap[row.versionStatus] }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="subCategory" label="二级分类" min-width="120" />
        <el-table-column prop="thirdCategory" label="三级分类" min-width="120" />
        <el-table-column prop="submitterName" label="上报人姓名" min-width="100" />
        <el-table-column prop="submitterId" label="上报人工号" min-width="100" />
        <el-table-column prop="buManager" label="BU负责人" min-width="100" />
        <el-table-column prop="buManagerId" label="BU负责人工号" min-width="120" />
        <el-table-column prop="createTime" label="创建时间" min-width="160" />
        <el-table-column prop="updateTime" label="更新时间" min-width="160" />
      </el-table>
    </div>


    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 30, 50]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue';

// 查询参数
const queryParams = reactive({
  status: '',
  brand: ''
});

// 分页参数
const currentPage = ref(1);
const pageSize = ref(10);

// 备案状态选项
const statusOptions = [
  { value: '0', label: '草稿' },
  { value: '1', label: '待审核' },
  { value: '2', label: '已通过' },
  { value: '3', label: '已驳回' }
];

// 品牌选项
const brandOptions = [
  { value: 'brand1', label: '品牌A' },
  { value: 'brand2', label: '品牌B' },
  { value: 'brand3', label: '品牌C' }
];

// 状态映射
const statusMap = {
  '0': '草稿',
  '1': '待审核',
  '2': '已通过',
  '3': '已驳回'
};

const versionStatusMap = {
  '0': '未定版',
  '1': '已定版',
  '2': '已驳回'
};

// 模拟表格数据
const mockTableData = [
  {
    businessUnit: '事业部A',
    recordId: 'BA2023071401',
    productName: '测试产品名称1',
    productCode: 'P001',
    beiTongCode: 'BT001',
    category: '分类A',
    brand: '品牌A',
    deliveryMode: '配送方式1',
    recordMonth: '2023-07',
    recordStatus: '1',
    versionStatus: '0',
    subCategory: '二级分类A',
    thirdCategory: '三级分类A',
    submitterName: '张三',
    submitterId: 'EMP001',
    buManager: '李四',
    buManagerId: 'MGR001',
    createTime: '2023-07-14 10:00:00',
    updateTime: '2023-07-14 10:00:00'
  },
  {
    businessUnit: '事业部B',
    recordId: 'BA2023071402',
    productName: '测试产品名称2',
    productCode: 'P002',
    beiTongCode: 'BT002',
    category: '分类B',
    brand: '品牌B',
    deliveryMode: '配送方式2',
    recordMonth: '2023-07',
    recordStatus: '2',
    versionStatus: '1',
    subCategory: '二级分类B',
    thirdCategory: '三级分类B',
    submitterName: '王五',
    submitterId: 'EMP002',
    buManager: '赵六',
    buManagerId: 'MGR002',
    createTime: '2023-07-14 11:00:00',
    updateTime: '2023-07-14 11:00:00'
  }
];

// 过滤后的表格数据
const filteredTableData = computed(() => {
  let result = [];

  if (queryParams.status) {
    result = result.filter(item => item.recordStatus === queryParams.status);
  }

  if (queryParams.brand) {
    result = result.filter(item => item.brand === brandOptions.find(b => b.value === queryParams.brand)?.label);
  }

  // 分页处理
  const start = (currentPage.value - 1) * pageSize.value;
  return result.slice(start, start + pageSize.value);
});

// 总数
const total = computed(() => {
  let result = []

  if (queryParams.status) {
    result = result.filter(item => item.recordStatus === queryParams.status);
  }

  if (queryParams.brand) {
    result = result.filter(item => item.brand === brandOptions.find(b => b.value === queryParams.brand)?.label);
  }

  return result.length;
});

// 获取状态标签类型
const getStatusType = (status) => {
  const map = {
    '0': 'info',    // 草稿
    '1': 'warning', // 待审核
    '2': 'success', // 已通过
    '3': 'danger'   // 已驳回
  };
  return map[status] || 'info';
};

// 获取定版状态标签类型
const getVersionStatusType = (status) => {
  const map = {
    '0': 'info',    // 未定版
    '1': 'success', // 已定版
    '2': 'danger'   // 已驳回
  };
  return map[status] || 'info';
};

// 查询按钮
const handleQuery = () => {
  currentPage.value = 1;
};

// 重置按钮
const resetQuery = () => {
  queryParams.status = '';
  queryParams.brand = '';
  currentPage.value = 1;
};

// 分页大小改变
const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
};

// 当前页改变
const handleCurrentChange = (val) => {
  currentPage.value = val;
};
</script>

<style lang="scss" scoped>
.business-record {
  // 确保父容器不限制子元素的滚动

  .section-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 20px;
    color: #333;
  }

  .filter-container {
    background-color: #fff;
    border-radius: 4px;
  }

  .table-container {
    width: 100%;
    overflow-x: auto;
    overflow-y: hidden;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    // 确保容器有明确的显示方式
    display: block;

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      height: 8px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 4px;

      &:hover {
        background: #a8a8a8;
      }
    }

    // 确保表格样式正确
    .el-table {
      border: none;
    }
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}

:deep(.el-table) {
  .el-table__header th {
    font-weight: 600;
    color: #1f2d3d;
    background-color: #f5f7fa;
  }
}

.el-tag {
  border-radius: 2px;
}
</style>
