import request from '@/utils/request';

// Types
export interface ProductVo {
  mdmCode: string | null;
  productCode: string | null;
  productName: string | null;
  specCode: string | null;
  specName: string | null;
}

export interface DtpJurVo {
  jurCode: string;
  userName: string;
  userCode: string;
  postCode: string;
  postName: string;
  deptCode: string;
  deptName: string;
  dtpDrugCount: number;
  jurType: string;
  productName: string;
  status: string;
  leaderName: string;
  leaderCode: string;
  productVoList: ProductVo[];
  drugName: string;
  drugCode: string;
  province: string;
  city: string;
  district: [];
  specCode: string;
  specName: string;
}

export interface DtpJurBo {
  pageSize?: number;
  pageNum?: number;
  orderByColumn?: string;
  isAsc?: string;
  jurCode?: string;
  userName?: string;
  userCode?: string;
  postCode?: string;
  postName?: string;
  deptCode?: string;
  deptName?: string;
  jurType?: string;
  productName?: string;
  status?: string;
  leaderName?: string;
  leaderCode?: string;
  productCodeList?: string[];
  operateType?: 'insert' | 'update' | 'delete';
  deptId?: number;
  postId?: number;
  dtpDrugCount?: number;
}

export interface DtpJurDrugBo extends DtpJurBo {
  drugName?: string;
  drugCode?: string;
  province?: string;
  city?: string;
  district?: string;
  specCode?: string;
  specName?: string;
}

export interface TableDataInfo<T> {
  total: number;
  rows: T[];
  code: number;
  msg: string;
}

export interface SysDeptUserBo {
  deptId: number;
  companyCode: string;
  parentId: number;
  parentDeptName: string;
  ancestors: string;
  deptName: string;
  deptType: string;
  orderNum: number;
  status: string;
  createTime: string;
  parentDeptCode: string;
  deptCode: string;
  deptLevel: string;
  mgrName: string;
  isChildren: boolean;
  isJob: boolean;
}

// API Functions
export function updateJurisdiction(data: DtpJurBo) {
  return request({
    url: '/mdm/dtp/jur/update',
    method: 'post',
    data
  });
}

export function getJurisdictionList(data: DtpJurBo) {
  return request({
    url: '/mdm/dtp/jur/page',
    method: 'post',
    data
  });
}

export function getJurisdictionOperateLog(data: DtpJurBo) {
  return request({
    url: '/mdm/dtp/jur/operate/log',
    method: 'post',
    data
  });
}

export function getJurisdictionDrugList(data: DtpJurDrugBo) {
  return request({
    url: '/mdm/dtp/jur/drug/page',
    method: 'post',
    data
  });
}

export function getJurisdictionDetail(jurCode: string) {
  return request({
    url: '/mdm/dtp/jur/detail',
    method: 'get',
    params: { jurCode }
  });
}

export function getDepartmentList(params: {
  deptId?: number;
  deptCodeList?: string[];
  parentId?: number;
  deptName?: string;
  deptType?: string;
  status?: string;
  parentDeptCode?: string;
  deptCode?: string;
  deptLevel?: string;
  ancestors?: string;
  version?: string;
  mgr?: boolean;
  ancestorsList?: number[];
  appCode: string;
}) {
  return request({
    url: '/mdm/dtp/jur/dept-list',
    method: 'get',
    params
  });
}
