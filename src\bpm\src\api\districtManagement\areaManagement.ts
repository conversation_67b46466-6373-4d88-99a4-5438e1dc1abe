import request from '@/utils/request';
import { AxiosPromise } from 'axios';
// 查询辖区列表
export const getJurListApi = (query: any): AxiosPromise<any> => {
  return request({
    url: '/hr/jur/list',
    method: 'get',
    params: query
  });
};
// 获取辖区信息详情
export const getJurDetailApi = (jurCode: any): AxiosPromise<any> => {
  return request({
    url: `/hr/jur/detail/${jurCode}`,
    method: 'get'
    // params: query
  });
};
// 辖区操作记录-分页
export const getLogListApi = (query: any): AxiosPromise<any> => {
  return request({
    url: '/hr/jur/log-list',
    method: 'get',
    params: query
  });
};

// 辖区机构列表
export const getJurInstitutionListApi = (query: any): AxiosPromise<any> => {
  return request({
    url: '/hr/jur/institution/list',
    method: 'get',
    params: query
  });
};

// 辖区客户列表
export const getJurCustomerListApi = (query: any): AxiosPromise<any> => {
  return request({
    url: '/hr/jur/customer/list',
    method: 'get',
    params: query
  });
};

//新增项
export const addApi = (data: any, url: string) => {
  return request({
    url,
    method: 'post',
    data
  });
};
// 更新项
export const editApi = (data: any, url: string) => {
  return request({
    url,
    method: 'post',
    data: data
  });
};
/**
 * 删除测试单
 * @param id  id: string | number | Array<string | number>
 */
export const delApi = (data: any, url: string) => {
  return request({
    url,
    method: 'post',
    data: data
  });
};

// 启用
export const useApi = (data: any, url: string) => {
  return request({
    url,
    method: 'post',
    data: data
  });
};
// 停用
export const stopApi = (data: any, url: string) => {
  return request({
    url,
    method: 'post',
    data: data
  });
};

// 查询拜访产品信息
export const getJurCproductstApi = (query: any): AxiosPromise<any> => {
  return request({
    url: '/mdm/jur/products',
    method: 'get',
    params: query
  });
};
// 查询机构
export const getQueryJurInstitutionApi = (query: any): AxiosPromise<any> => {
  return request({
    url: '/mdm/jur/institution',
    method: 'get',
    params: query
  });
};

// 根据机构查询客户
export const getQueryJurInstitutionByinsApi = (query: any): AxiosPromise<any> => {
  return request({
    url: '/mdm/jur/cust-by-ins',
    method: 'get',
    params: query
  });
};

//excel导入辖区
export const jurUploadApi = (data: any) => {
  return request({
    url: '/hr/jur/upload',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
};

// 辖区新增按钮控制

export const getInsertControlApi = (query: any): AxiosPromise<any> => {
  return request({
    url: '/hr/jur/insert-control',
    method: 'get',
    params: query
  });
};
