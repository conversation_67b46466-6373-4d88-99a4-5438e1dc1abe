<template>
  <div class="effective-tags">
    <h3 class="title">生效标签</h3>
    <div v-for="(group, groupIdx) in tagsData" :key="groupIdx" class="tag-group">
      <div class="group-title">{{ group.groupName }}</div>
      <div v-for="(subGroup, subIdx) in group.subGroups" :key="subIdx" class="sub-group">
        <div class="sub-title">{{ subGroup.subGroupName }}</div>
        <div class="tag-list">
          <span
            v-for="(tag, tagIdx) in subGroup.tags"
            :key="tagIdx"
            class="tag-card"
          >
            {{ tag }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// props: [{ groupName: '', subGroups: [{ subGroupName: '', tags: [''] }] }]
const props = defineProps({
  tagsData: {
    type: Array,
    required: true,
    default: () => []
  }
});
</script>

<style scoped lang="scss">
.effective-tags {
  .title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 16px;
  }
  .tag-group {
    margin-bottom: 20px;
    .group-title {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 8px;
    }
    .sub-group {
      display: flex;
      align-items: flex-start;
      margin-bottom: 8px;
      .sub-title {
        min-width: 80px;
        color: #666;
        font-size: 14px;
        margin-right: 12px;
        flex-shrink: 0;
      }
      .tag-list {
        display: flex;
        flex-wrap: wrap;
        gap: 8px 8px;
        .tag-card {
          display: inline-block;
          background: #f3f6fa;
          color: #333;
          border-radius: 6px;
          padding: 4px 14px;
          font-size: 14px;
          margin-bottom: 4px;
          min-width: 40px;
          text-align: center;
          box-sizing: border-box;
          transition: background 0.2s;
        }
      }
    }
  }
}
</style>
