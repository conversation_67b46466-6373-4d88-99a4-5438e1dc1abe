import request from '@/utils/request';
import { AxiosPromise } from 'axios';

export const insDsApplyListApi = (data: any) => {
  return request({
    url: `/mdm/ins-ds/apply/list`,
    method: 'post',
    data
  });
};

export const insDsApplyDetailApi = (query: any) => {
  return request({
    url: `/mdm/ins-ds/detail`,
    method: 'get',
    params: query
  });
};

export const insDsBuConfigApi = (): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=ins_ds_bu_config`,
    method: 'get'
  });
};
// 【H5】关联药店列表-药店品规列表
export const insDsSpecListApi = (params: any) => {
  return request({
    url: `/mdm/ins-ds/ds/list?insCode=${params.insCode}`,
    method: 'post'
  });
};

export const getInsDsChannelsSalesStatusApi = () => {
  return request({
    url: `/plt/dict/data/list?dictType=sales_status`,
    method: 'get'
  });
};

export const affiliatedPharmacylist = (query: any): AxiosPromise<any> => {
  return request({
    url: '/msr/rel-drugstore/list',
    method: 'get',
    params: query
  });
};

export const mdmAffiliatedPharmacylist = (data: any) => {
  return request({
    url: '/mdm/ins-ds/list',
    method: 'post',
    data
  });
};
export const relationDrugstorelist = (query: any): AxiosPromise<any> => {
  return request({
    url: '/msr/jur-rel-drugstore/list',
    method: 'get',
    params: query
  });
};
export const versionRelationDrugstorelist = (query: any): AxiosPromise<any> => {
  return request({
    url: '/msr/rel-drugstore/version-list',
    method: 'get',
    params: query
  });
};
export const relationDrugstorelistByIns = (query: any): AxiosPromise<any> => {
  return request({
    url: '/msr/rel-drugstore/list-by-ins',
    method: 'get',
    params: query
  });
};
export const jurRelationDrugstoreDelete = (id: any, jurCode: any): AxiosPromise<any> => {
  return request({
    url: `/msr/jur-rel-drugstore/${id}?jurCode=${jurCode}`,
    method: 'delete'
  });
};

export const relationDrugstoreDelete = (id: any): AxiosPromise<any> => {
  return request({
    url: `/msr/rel-drugstore/${id}`,
    method: 'delete'
  });
};

export const institutionImport = (data: any) => {
  return request({
    url: '/msr/jur/drugstore/relationDrugstore-template',
    method: 'post',
    data: data
  });
};

export const institutionExport = (data: any) => {
  return request({
    url: '/msr/institution/export',
    method: 'post',
    data: data
  });
};

export const cusExport = (data: any) => {
  return request({
    url: '/msr/customer/import',
    method: 'post',
    data: data
  });
};

export const relationDrugstoreDetail = (id: any): AxiosPromise<any> => {
  return request({
    url: `/msr/rel-drugstore/detail/${id}`,
    method: 'get'
  });
};
export const tarRelationDrugstoreDetail = (id: any): AxiosPromise<any> => {
  return request({
    url: `/msr/jur-rel-drugstore/detail/${id}`,
    method: 'get'
  });
};
export const institutionUpdate = (data: any) => {
  return request({
    url: '/msr/institution',
    method: 'put',
    data: data
  });
};
export const add = (data: any) => {
  return request({
    url: '/msr/institution/add',
    method: 'post',
    data: data
  });
};

export const dictTypeList = (): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=ins_level`,
    method: 'get'
  });
};

export const insProfessionTypeList = (): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=ins_profession_type`,
    method: 'get'
  });
};

export const insTypeList = (): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=ins_type`,
    method: 'get'
  });
};

export const economicTypeList = (): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=economic_type`,
    method: 'get'
  });
};

export const getInsListRes = (): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=department_label`,
    method: 'get'
  });
};
export const deptNameList = (): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=dept_name`,
    method: 'get'
  });
};

export const customerList = (query: any): AxiosPromise<any> => {
  return request({
    url: `/msr/customer/list`,
    method: 'get',
    params: query
  });
};

export const deleteCustomer = (ids: any): AxiosPromise<any> => {
  return request({
    url: `/msr/customer/${ids}`,
    method: 'delete'
  });
};

export const customerDetail = (id: any): AxiosPromise<any> => {
  return request({
    url: `/msr/customer/${id}`,
    method: 'get'
  });
};
export const createCustomer = (data): AxiosPromise<any> => {
  return request({
    url: `/msr/customer/add`,
    method: 'post',
    data
  });
};
export const customerUpdate = (data): AxiosPromise<any> => {
  return request({
    url: `/msr/customer`,
    method: 'put',
    data
  });
};
export const professionList = (): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=profession_tech_title`,
    method: 'get'
  });
};

export const administrationList = (): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=administration_lvl`,
    method: 'get'
  });
};

export const lectureLvlList = (): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=lecture_lvl`,
    method: 'get'
  });
};
export const district = (type: string): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=${type}`,
    method: 'get'
  });
};
export const jobList = (query: any): AxiosPromise<any> => {
  return request({
    url: `/msr/customer/detail`,
    method: 'get',
    params: query
  });
};
export const addCustomerType = (data: any): AxiosPromise<any> => {
  return request({
    url: `/msr/customer`,
    method: 'post',
    data
  });
};
export const customerTypeUpdate = (data: any): AxiosPromise<any> => {
  return request({
    url: `/msr/customer/update`,
    method: 'put',
    data
  });
};

export const deleteCustomerType = (ids: any): AxiosPromise<any> => {
  return request({
    url: `/msr/customer/${ids}`,
    method: 'delete'
  });
};

export const cusLevelList = (): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=cus_idea_level`,
    method: 'get'
  });
};
export const cusPotenLevel = (): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=cus_poten_level`,
    method: 'get'
  });
};
export const getProductList = (params: boolean): AxiosPromise<any> => {
  return request({
    url: `/msr/product/crm-products`,
    method: 'get',
    params
  });
};

export const insDsDeteleApi = (ids: any): AxiosPromise<any> => {
  return request({
    url: `/mdm/ins-ds/${ids}`,
    method: 'delete'
  });
};

export const getProcessList = (data: any): AxiosPromise<any> => {
  return request({
    url: '/bpm/workflow/process/page',
    method: 'get',
    params: data
  });
};
export const getAuditList = (logSchema: string, pageNum: number, pageSize: number, data: any): AxiosPromise<any> => {
  return request({
    url: `/mdm/log/audit/query?logSchema=${logSchema}&pageNum=${pageNum}&pageSize=${pageSize}`,
    method: 'post',
    data
  });
};

export const getRecordTypeListApi = (): AxiosPromise<any> => {
  return request({
    url: '/plt/dict/data/list?dictType=record_type',
    method: 'get'
  });
};
