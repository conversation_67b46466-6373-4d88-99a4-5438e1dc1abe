# HCO 查询条件持久化功能使用指南

## 功能概述

HCO 列表页面现在支持查询条件持久化功能，当用户从列表页面跳转到详情页面，再返回列表页面时，会自动恢复之前的所有查询条件，包括：

- 基础查询条件（BU 归属、机构类别、机构名称）
- 高级筛选条件
- 分页信息（当前页码、每页显示数量）

## 功能特性

### 1. 自动保存查询条件

- 每次执行查询时，自动保存当前的查询条件
- 分页变化时，自动保存当前的分页状态
- 跳转到详情页面前，保存当前查询状态
- 数据保存在 localStorage 中，支持浏览器刷新后恢复

### 2. 自动恢复查询条件

- 页面加载时，自动检查是否有保存的查询条件
- 如果有保存的条件，自动恢复并执行查询
- 如果没有保存的条件，执行默认查询

### 3. 数据过期机制

- 保存的查询条件有效期为 24 小时
- 超过有效期的数据会自动清除
- 确保数据不会无限期占用存储空间

### 4. 重置功能

- 点击"重置"按钮时，清除所有保存的查询条件
- 恢复到默认查询状态

### 5. 页面卸载清除

- 当用户点击左侧菜单跳转到其他页面时，自动清除保存的查询条件
- 确保下次访问 HCO 页面时是全新的状态
- 避免不同页面之间的查询条件相互干扰

## 技术实现

### 1. 持久化管理器 (`useQueryPersistence`)

```typescript
// 保存查询条件
const saveQueryConditions = (basicQuery, advancedFilters, queryParams) => {
  // 保存到 localStorage
}

// 恢复查询条件
const restoreQueryConditions = () => {
  // 从 localStorage 恢复
}

// 清除查询条件
const clearQueryConditions = () => {
  // 清除 localStorage 数据
}
```

### 2. HCO 列表管理器增强 (`useHcoList`)

新增方法：

- `saveCurrentQueryState()` - 保存当前查询状态
- `restoreQueryState()` - 恢复查询状态
- `clearQueryState()` - 清除查询状态

### 3. 高级筛选组件增强

新增方法：

- `restoreFilters(filters)` - 恢复筛选条件

## 使用方法

### 1. 在列表页面中使用

```vue
<script setup>
import { useHcoList } from './composables/useHco'

const hcoList = useHcoList()

// 跳转到详情页面前保存查询条件
function handleDetail(row) {
  hcoList.saveCurrentQueryState(queryParams.value, advancedFilters.value)
  router.push({ path: '/detail', query: { id: row.id } })
}

// 页面加载时恢复查询条件
onMounted(() => {
  const hasRestoredConditions = restoreQueryConditions()
  if (hasRestoredConditions) {
    handleQuery() // 执行查询
  } else {
    // 执行默认查询
    handleQuery()
  }
})
</script>
```

### 2. 恢复查询条件函数

```javascript
function restoreQueryConditions() {
  const savedState = hcoList.restoreQueryState()
  if (savedState) {
    // 恢复基础查询条件
    Object.assign(queryParams.value, savedState.basicQuery)

    // 恢复高级筛选条件
    advancedFilters.value = savedState.advancedFilters
    advancedFilterRef.value?.restoreFilters(savedState.advancedFilters)

    // 恢复查询参数
    Object.assign(hcoList.queryParams, savedState.queryParams)

    return true
  }
  return false
}
```

## 存储数据结构

```typescript
interface QueryState {
  basicQuery: {
    bu: string | string[]
    type: string | string[]
    orgName: string
  }
  advancedFilters: Array<{
    id: number
    left: string
    op: string
    right: any
  }>
  queryParams: {
    conditions: QueryCondition[]
    logic: string
    pageSize: number
    pageNum: number
  }
  timestamp: number // 保存时间戳
}
```

## 注意事项

1. **数据安全性**：查询条件保存在客户端 localStorage 中，不包含敏感信息
2. **存储限制**：localStorage 有大小限制，建议定期清理过期数据
3. **浏览器兼容性**：需要浏览器支持 localStorage API
4. **数据同步**：多个标签页之间的查询条件不会自动同步

## 故障排除

### 1. 查询条件没有恢复

- 检查浏览器是否支持 localStorage
- 检查是否有 JavaScript 错误
- 确认数据没有过期（24 小时内）

### 2. 高级筛选条件没有恢复

- 确认高级筛选组件已正确实现 `restoreFilters` 方法
- 检查筛选条件数据格式是否正确

### 3. 分页信息没有恢复

- 确认查询参数中包含分页信息
- 检查分页组件是否正确绑定了恢复的参数

## 更新日志

- **v1.0.0** - 初始版本，支持基础查询条件和高级筛选条件的持久化
- 支持数据过期机制
- 支持重置功能
- 支持高级筛选组件的条件恢复
