import request from '@/utils/request';
import { AxiosPromise } from 'axios';
// 获取部门下权限范围
export const getDepListApi = (query: any): AxiosPromise<any> => {
  return request({
    url: '/tum/dept-user/list-by-dept',
    method: 'get',
    params: query
  });
};

// 获取岗位下权限范围
export const getPostListApi = (query: any): AxiosPromise<any> => {
  return request({
    url: '/tum/dept-user/list-by-post',
    method: 'get',
    params: query
  });
};

// 新增岗位下权限范围
export const addUserDeptApi = (data: any) => {
  return request({
    url: '/tum/dept-user/insert',
    method: 'post',
    data: data
  });
};

// 转岗
export const updateUserDeptApi = (data: any) => {
  return request({
    url: '/hr/user-dept/update',
    method: 'post',
    data: data
  });
};

// 删除权限范围
export const delUserDeptApi = (id: any) => {
  return request({
    url: `/tum/dept-user/${id}`,
    method: 'delete'
  });
};

// 获取部门列表 /tum/dept/list
export const getDeptListApi = (query: any) => {
  return request({
    url: '/tum/dept/list',
    method: 'get',
    params: query
  });
};

// 枚举
export const getEnumerateApi = (query: any, url: string) => {
  return request({
    url,
    method: 'get',
    params: query
  });
};

// 获取直属领导
export const getLeaderApi = (id: any) => {
  return request({
    url: `/hr/user/get-leader/${id}`,
    method: 'get'
    // params: query
  });
};
// 获取操作记录
export const logListPage = (query: any) => {
  return request({
    url: '/tum/log/list-page',
    method: 'get',
    params: query
  });
};
export const setManager = (data: any) => {
  return request({
    url: '/hr/user-dept/set/manager',
    method: 'post',
    data: data
  });
};

// 根据姓名搜索姓名工号
export const getNameApi = (name: any) => {
  return request({
    url: `/tum/user/get-name/${name}`,
    method: 'get'
    // data: data
  });
};

// 获取是否展示空岗添加按钮
export const getIsaddDeptUserApi = (query: any) => {
  return request({
    url: `/hr/dept/is_add_dept_user`,
    method: 'get',
    params: query
  });
};
// 获取是否展示空岗添加按钮
export const deptManager = (id: any) => {
  return request({
    url: `/hr/dept/manager/${id}`,
    method: 'get'
  });
};

// 获取部门详情
export const getDeptDetailApi = (deptId: any) => {
  return request({
    url: `/tum/dept/detail/${deptId}`,
    method: 'get'
    // data: data
  });
};

// 获取岗位标签
export const getPsotTagsApi = (postId: any) => {
  return request({
    url: `/tum/post/tag/${postId}`,
    method: 'get'
  });
};

// 修改岗位科室标签
export const updateTagApi = (data: any) => {
  return request({
    url: '/hr/post/update-tag',
    method: 'post',
    data: data
  });
};

//导入接口
export const importApi = (data: any) => {
  return request({
    url: `/tum/post/upload`,
    method: 'post',
    data: data
  });
};

// 新建部门
export const addDeptInsertApi = (data: any) => {
  return request({
    url: `/tum/dept/insert`,
    method: 'post',
    data: data
  });
};
// 编辑部门
export const updateDeptApi = (data: any) => {
  return request({
    url: `/tum/dept/update`,
    method: 'post',
    data: data
  });
};

// 删除部门
export const deleteDeptApi = (data: any, deptId) => {
  return request({
    url: `/tum/dept/delete?deptId=${deptId}`,
    method: 'post'
    // data: data
  });
};
// 新增岗位
export const addPostInsertApi = (data: any) => {
  return request({
    url: `/tum/post/insert`,
    method: 'post',
    data: data
  });
};
// 编辑岗位
export const updatePostApi = (data: any) => {
  return request({
    url: `/tum/post/update`,
    method: 'post',
    data: data
  });
};
// 删除岗位
export const deletePostApi = (postId) => {
  return request({
    url: `/tum/post/delete?postId=${postId}`,
    method: 'post'
    // data: data
  });
};

// 根据岗位获取上级部门
export const deptGetByPostApi = (query: any) => {
  return request({
    url: `/tum/dept/get-by-post`,
    method: 'get',
    params: query
  });
};
export const postLevel = () => {
  return request({
    url: '/plt/dict/data/list?dictType=post_level',
    method: 'get'
  });
};
export const detailByPost = (query: any) => {
  return request({
    url: '/tum/jur/detail/by-post',
    method: 'get',
    params: query
  });
};
//
