import request from '@/utils/request';
import { OssQuery, OssVO } from './types';
import { AxiosPromise } from 'axios';

// 查询OSS对象存储列表
export function listOss(query: OssQuery): AxiosPromise<OssVO[]> {
  return request({
    url: '/fle/oss/list',
    method: 'get',
    params: query
  });
}

// 查询OSS对象基于id串
export function listByIds(ossId: string | number): AxiosPromise<OssVO[]> {
  return request({
    url: '/fle/oss/listByIds/' + ossId,
    method: 'get'
  });
}

// 删除OSS对象存储
export function delOss(ossId: string | number | Array<string | number>) {
  return request({
    url: '/fle/oss-file/' + ossId,
    method: 'delete'
  });
}

export function dowTemplte(ossId: string | number | Array<string | number>) {
  return request({
    url: '/fle/oss-file/tenant/' + ossId,
    method: 'get'
  });
}

export const exportURL = (data: object): AxiosPromise<any> => {
  return request({
    url: `/mdm/imp-task/download`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
};

export const upload = (data: any, funcCode: string, query: any) => {
  return request({
    url: `/msr/imp-task/importByOss?funcCode=${funcCode}&queryParams=${query || ''}`,
    method: 'post',
    data: data
  });
};
