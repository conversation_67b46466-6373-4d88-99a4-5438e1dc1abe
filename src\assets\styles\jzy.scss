// 状态公共样式
.jzy-status{
    display: flex;
    align-items: center;
    justify-content: center;
    &::before {
    display: block;
    content: '';
     width: 6px;
     height: 6px;
     border-radius: 6px;
     margin-right: 8px;
     background: black;
   }
  }
  .jzy-info {
    color: var(--unnamed, #606266);
    &::before{
     background: var(--unnamed, #C6CAD1);
    }
  }
.jzy-primary {
    color: var(--unnamed, #2551F2);
    &::before{
     background: var(--unnamed, #2551F2);
    }
  }
.jzy-success {
    color: var(--6, #34C724);
    &::before{
        background: var(--6, #34C724);
    }
}

.jzy-warning {
  color: var(--6, #F7BA1E);
  &::before{
    background:  var(--6, #F7BA1E);
  }
}
.jzy-danger {
  color: var(--6, #F54A45);
  &::before{
    background:  var(--6, #F54A45);
  }
}

.jzy-tag{
  ::v-deep {
    .el-tag {
      padding: 0 8px;
      margin: 0;
      border: none !important;
      border-radius: 2px;
      width: 72px;
      font-size: 14px !important;
    }
  }
 }

 .jzy-status-first{
  color: var(--6, #F54A45);
 }
 .jzy-status-nature{
  color: var(--6, #F80);
 }
 .jzy-status-buy{
  color: var(--6, #F7BA1E);

 }
 .jzy-status-formal{
  color: var(--6, #34C724);
 }


.status-code {
  .jzy-status-first{
    color: var(--6, #F54A45);
    .el-input{
      --el-input-text-color: var(--6, #F54A45);
     }
   }
   .jzy-status-nature{
    color: var(--6, #F80);
    .el-input {
      --el-input-text-color:  var(--6, #F80);
     }
   }
   .jzy-status-buy{
    color: var(--6, #F7BA1E);
    .el-input {
      --el-input-text-color:  var(--6, #F7BA1E);
     }
  
   }
   .jzy-status-formal{
    color: var(--6, #34C724);
    .el-input {
      --el-input-text-color: var(--6, #34C724);
     }
   }


   .jzy-status-first.el-input {
    --el-input-text-color: var(--6, #F54A45);
   }
   .jzy-status-nature.el-input {
    --el-input-text-color:  var(--6, #F80);
   }
   .jzy-status-buy.el-input {
    --el-input-text-color:  var(--6, #F7BA1E);
   }
   .jzy-status-formal.el-input {
      --el-input-text-color: var(--6, #34C724);
     }
}


