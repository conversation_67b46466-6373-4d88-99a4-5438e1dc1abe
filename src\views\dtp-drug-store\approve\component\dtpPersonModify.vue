<script setup lang="ts">
import { ref, onMounted, defineProps, defineEmits, watch, computed } from 'vue';
import { ElMessage, UploadProps, UploadFile } from 'element-plus';
import { Plus, ZoomIn, Delete } from '@element-plus/icons-vue';
import { getDicts } from '@/api/system/dict/data';
import { globalHeaders } from '@/utils/request';
import { getConfigKey } from '@/api/system/config';

// 定义上传文件接口
interface UploadResponse {
  code: number;
  msg: string;
  data: {
    ossId: string;
    url: string;
    [key: string]: any;
  };
}

interface FileItem {
  name: string;
  url: string;
  ossId: string;
  size: number;
  mime_type: string;
  [key: string]: any;
}

// 药店评分点
interface ScorePoint {
  scorePointsTitle: string;
  score: string;
  inputScore?: string;
  images?: FileItem[]; // 每个评分点的图片列表
}

// 药店评分类别
interface DrugScore {
  scoreItemName: string;
  scoreItem: ScorePoint[];
  images?: FileItem[]; // 分类级别的图片列表
}

const props = defineProps({
  // 初始选中的标签列表
  modelValue: {
    type: Array as () => string[],
    default: () => [] as string[]
  },
  // 标签字典类型
  dictType: {
    type: String,
    default: 'dtp_ds_tag'
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 初始附件列表
  fileList: {
    type: Array as () => FileItem[],
    default: () => [] as FileItem[]
  },
  // 初始评分列表
  drugScoreList: {
    type: Array as () => DrugScore[],
    default: () => [] as DrugScore[]
  },
});

const emit = defineEmits(['update:modelValue', 'change', 'update:fileList', 'file-change', 'update:drugScoreList', 'score-change']);

// 标签列表
const tagList = ref<Array<{ label: string; value: string; elTagType?: string; elTagClass?: string }>>([]);

// 选中的标签列表
const selectedTags = ref<string[]>(props.modelValue || []);

// 上传的图片列表
const imageList = ref<FileItem[]>(props.fileList || []);

// 药店评分列表
const drugScoreCategories = ref<DrugScore[]>([]);

// 用户输入的评分
const inputScores = ref<DrugScore[]>(props.drugScoreList || []);

// 加载状态
const loading = ref(false);
const ossConfigKey = import.meta.env.VITE_APP_UPLOAD_URL;
const baseUrl = import.meta.env.VITE_APP_BASE_API;
const uploadAction = ref(baseUrl + `/fle/oss-file/upload/${ossConfigKey}`); // 上传文件服务器地址
const uploadHeaders = ref(globalHeaders());

// 获取标签字典
const fetchTags = async () => {
  loading.value = true;
  try {
    const response = await getDicts(props.dictType);
    if (response && response.data) {
      tagList.value = response.data.map(item => ({
        label: item.dictLabel,
        value: item.dictValue,
        elTagType: item.listClass,
        elTagClass: item.cssClass
      }));
    }
  } catch (error) {
    console.error('获取标签字典失败:', error);
    ElMessage.error('获取标签字典失败');
  } finally {
    loading.value = false;
  }
};

// 获取药店评分系统参数
const fetchDrugScoreConfig = async () => {
  try {
    loading.value = true;
    const response = await getConfigKey('gs.dtp.drug.score');

    if (response && response.msg) {
      let scoreConfig;

      try {
        scoreConfig = JSON.parse(response.msg as string);
      } catch (err) {
        console.error('解析药店评分配置失败:', err);
        return;
      }

      if (Array.isArray(scoreConfig)) {
        drugScoreCategories.value = scoreConfig;

        // 如果已经有输入的评分，保持它们；否则，初始化评分
        if (inputScores.value.length === 0) {
          // 初始化用户输入评分
          inputScores.value = scoreConfig.map(category => ({
            scoreItemName: category.scoreItemName,
            images: [], // 初始化分类级别图片数组
            scoreItem: category.scoreItem.map((item: any) => ({
              scorePointsTitle: item.scorePointsTitle,
              score: item.score,
              inputScore: '', // 初始化为空字符串
              images: [] // 初始化评分点图片数组
            }))
          }));
        } else {
          // 如果已有数据，确保每个分类和评分点都有images属性
          inputScores.value.forEach(category => {
            if (!category.images) {
              category.images = [];
            }
            category.scoreItem.forEach(item => {
              if (!item.images) {
                item.images = [];
              }
            });
          });
        }
      }
    }
  } catch (error) {
    console.error('获取药店评分配置失败:', error);
    ElMessage.error('获取药店评分配置失败');
  } finally {
    loading.value = false;
  }
};

// 处理评分输入
const handleScoreInput = (categoryIndex: number, pointIndex: number, value: string | number) => {
  // 验证输入是否为数字且在范围内，支持小数点后一位
  const maxScore = Number(drugScoreCategories.value[categoryIndex].scoreItem[pointIndex].score);
  let inputValue = value?.toString().trim() || '';

  // 如果不是有效的数字格式（支持小数点后一位），设为空
  if (!/^\d*\.?\d{0,1}$/.test(inputValue)) {
    inputValue = '';
  } else if (inputValue !== '') {
    // 如果是数字，确保在0-maxScore范围内
    const numValue = Number(inputValue);
    if (numValue < 0) {
      inputValue = '0';
    } else if (numValue > maxScore) {
      inputValue = maxScore.toString();
    } else {
      // 保留一位小数（如果有的话）
      inputValue = numValue.toString();
    }
  }

  // 更新评分
  inputScores.value[categoryIndex].scoreItem[pointIndex].inputScore = inputValue;

  // 发出更新事件
  emit('update:drugScoreList', inputScores.value);
  emit('score-change', inputScores.value);
};

// 处理选中状态变化
const handleChange = () => {
  emit('update:modelValue', selectedTags.value);
  emit('change', selectedTags.value);
};

// 处理图片上传成功
const handleUploadSuccess = (response: UploadResponse, file: UploadFile, fileList: UploadFile[]) => {
  if (response && response.code === 200) {
    const fileInfo: FileItem = {
      name: file.name,
      url: response.data.url,
      ossId: response.data.ossId,
      size: file.size || 0,
      mime_type: file.raw?.type || 'image/jpeg'
    };

    imageList.value.push(fileInfo);
    emit('update:fileList', imageList.value);
    emit('file-change', imageList.value);
    ElMessage.success('上传成功');
  } else {
    ElMessage.error(response.msg || '上传失败');
  }
};

// 处理上传错误
const handleUploadError = (error: Error) => {
  console.error('上传失败:', error);
  ElMessage.error('图片上传失败');
};

// 处理上传前的检查
const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  // 检查文件类型
  const isImage = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'].includes(file.type);
  if (!isImage) {
    ElMessage.error('只能上传图片格式!');
    return false;
  }

  // 检查文件大小，限制为5MB
  const isLt5M = file.size / 1024 / 1024 < 5;
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过5MB!');
    return false;
  }

  return true;
};

// 删除图片
const handleRemove = (file: FileItem) => {
  imageList.value = imageList.value.filter(item =>
    item.ossId !== file.ossId && item.url !== file.url
  );
  emit('update:fileList', imageList.value);
  emit('file-change', imageList.value);
};

// 预览图片
const handlePreview = (file: FileItem) => {
  window.open(file.url, '_blank');
};

// 处理评分项图片上传成功
const handleScoreImageUploadSuccess = (response: UploadResponse, file: UploadFile, categoryIndex: number, pointIndex: number) => {
  if (response && response.code === 200) {
    const fileInfo: FileItem = {
      name: file.name,
      url: response.data.url,
      ossId: response.data.ossId,
      size: file.size || 0,
      mime_type: file.raw?.type || 'image/jpeg'
    };

    // 确保images数组存在
    if (!inputScores.value[categoryIndex].scoreItem[pointIndex].images) {
      inputScores.value[categoryIndex].scoreItem[pointIndex].images = [];
    }

    inputScores.value[categoryIndex].scoreItem[pointIndex].images!.push(fileInfo);

    // 发出更新事件
    emit('update:drugScoreList', inputScores.value);
    emit('score-change', inputScores.value);
    ElMessage.success('图片上传成功');
  } else {
    ElMessage.error(response.msg || '图片上传失败');
  }
};

// 处理评分项图片上传错误
const handleScoreImageUploadError = (error: Error) => {
  console.error('评分项图片上传失败:', error);
  ElMessage.error('图片上传失败');
};

// 删除评分项图片
const handleScoreImageRemove = (file: FileItem, categoryIndex: number, pointIndex: number) => {
  const images = inputScores.value[categoryIndex].scoreItem[pointIndex].images || [];
  inputScores.value[categoryIndex].scoreItem[pointIndex].images = images.filter(item =>
    item.ossId !== file.ossId && item.url !== file.url
  );

  // 发出更新事件
  emit('update:drugScoreList', inputScores.value);
  emit('score-change', inputScores.value);
};

// 预览评分项图片
const handleScoreImagePreview = (file: FileItem) => {
  window.open(file.url, '_blank');
};

// 处理分类级别图片上传成功
const handleCategoryImageUploadSuccess = (response: UploadResponse, file: UploadFile, categoryIndex: number) => {
  if (response && response.code === 200) {
    const fileInfo: FileItem = {
      name: file.name,
      url: response.data.url,
      ossId: response.data.ossId,
      size: file.size || 0,
      mime_type: file.raw?.type || 'image/jpeg'
    };

    // 确保images数组存在
    if (!inputScores.value[categoryIndex].images) {
      inputScores.value[categoryIndex].images = [];
    }

    inputScores.value[categoryIndex].images!.push(fileInfo);

    // 发出更新事件
    emit('update:drugScoreList', inputScores.value);
    emit('score-change', inputScores.value);
    ElMessage.success('图片上传成功');
  } else {
    ElMessage.error(response.msg || '图片上传失败');
  }
};

// 处理分类级别图片上传错误
const handleCategoryImageUploadError = (error: Error) => {
  console.error('分类图片上传失败:', error);
  ElMessage.error('图片上传失败');
};

// 删除分类级别图片
const handleCategoryImageRemove = (file: FileItem, categoryIndex: number) => {
  const images = inputScores.value[categoryIndex].images || [];
  inputScores.value[categoryIndex].images = images.filter(item =>
    item.ossId !== file.ossId && item.url !== file.url
  );

  // 发出更新事件
  emit('update:drugScoreList', inputScores.value);
  emit('score-change', inputScores.value);
};

// 预览分类级别图片
const handleCategoryImagePreview = (file: FileItem) => {
  window.open(file.url, '_blank');
};

// 监听 modelValue 变化
watch(() => props.modelValue, (newValue) => {
  if (newValue !== selectedTags.value) {
    selectedTags.value = [...newValue];
  }
}, { deep: true });

// 监听 fileList 变化
watch(() => props.fileList, (newValue) => {
  if (JSON.stringify(newValue) !== JSON.stringify(imageList.value)) {
    imageList.value = [...newValue];
  }
}, { deep: true });

// 监听 drugScoreList 变化
watch(() => props.drugScoreList, (newValue) => {
  if (JSON.stringify(newValue) !== JSON.stringify(inputScores.value)) {
    inputScores.value = [...newValue];
  }
}, { deep: true });

// 组件挂载时加载标签和评分配置
onMounted(() => {
  fetchTags();
  fetchDrugScoreConfig();
});
</script>

<template>
  <div class="tags-container" v-loading="loading">
    <div class="tag-list">
      <h3 class="text-base font-medium mb-2">标签选择</h3>
      <el-checkbox-group v-model="selectedTags" @change="handleChange">
        <el-checkbox v-for="tag in tagList" :key="tag.value" :label="tag.value" class="tag-item">
          <span>{{ tag.label }}</span>
        </el-checkbox>
      </el-checkbox-group>
    </div>

    <!-- 图片上传部分 -->
    <div class="upload-section mt-6">
      <h3 class="text-base font-medium mb-2">图片上传</h3>
      <el-upload
        class="image-uploader"
        :action="uploadAction"
        :headers="uploadHeaders"
        list-type="picture-card"
        :file-list="imageList"
        :before-upload="beforeUpload"
        :on-success="handleUploadSuccess"
        :on-error="handleUploadError"
        :on-remove="handleRemove"
        :on-preview="handlePreview"
        multiple
        :disabled="disabled"
      >
        <el-icon v-if="!disabled"><Plus /></el-icon>
        <template #file="{ file }">
          <div>
            <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
            <span class="el-upload-list__item-actions">
              <span class="el-upload-list__item-preview" @click="handlePreview(file)">
                <el-icon><zoom-in /></el-icon>
              </span>
              <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleRemove(file)">
                <el-icon><Delete /></el-icon>
              </span>
            </span>
          </div>
        </template>
      </el-upload>
      <div class="upload-tips text-xs text-gray-500 mt-1">只能上传JPG/PNG/GIF/WebP图片，且不超过5MB</div>
    </div>

    <!-- 药店评分部分 -->
    <div class="score-section mt-6" v-if="drugScoreCategories.length > 0">
      <h3 class="text-base font-medium mb-2">药店评分</h3>

      <div v-for="(category, categoryIndex) in drugScoreCategories" :key="categoryIndex" class="score-category mb-4">
        <div class="category-header flex items-center justify-between mb-4">
          <h4 class="text-sm font-medium">{{ category.scoreItemName }}</h4>

          <!-- 分类级别的图片上传 -->
          <div class="category-image-upload">
            <div class="upload-label text-xs text-gray-600 mb-1">上传{{ category.scoreItemName }}相关图片：</div>
            <el-upload
              class="category-image-uploader"
              :action="uploadAction"
              :headers="uploadHeaders"
              list-type="picture-card"
              :file-list="inputScores[categoryIndex].images || []"
              :before-upload="beforeUpload"
              :on-success="(response: UploadResponse, file: UploadFile) => handleCategoryImageUploadSuccess(response, file, categoryIndex)"
              :on-error="handleCategoryImageUploadError"
              :on-remove="(file: any) => handleCategoryImageRemove(file, categoryIndex)"
              :on-preview="handleCategoryImagePreview"
              multiple
              :disabled="disabled"
              :limit="10"
            >
              <el-icon v-if="!disabled"><Plus /></el-icon>
              <template #file="{ file }">
                <div>
                  <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
                  <span class="el-upload-list__item-actions">
                    <span class="el-upload-list__item-preview" @click="handleCategoryImagePreview(file)">
                      <el-icon><zoom-in /></el-icon>
                    </span>
                    <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleCategoryImageRemove(file, categoryIndex)">
                      <el-icon><Delete /></el-icon>
                    </span>
                  </span>
                </div>
              </template>
            </el-upload>
            <div class="upload-tips text-xs text-gray-400 mt-1">最多上传10张图片，支持JPG/PNG/GIF/WebP格式，单张不超过5MB</div>
          </div>
        </div>

        <div class="score-items">
          <div v-for="(item, pointIndex) in category.scoreItem" :key="pointIndex" class="score-item mb-2">
            <div class="flex items-center justify-between">
              <span class="score-title"
                >{{ item.scorePointsTitle }} <span class="score-max">(总分: {{ item.score }})</span></span
              >
              <div class="score-input-container">
                <el-input-number
                  v-model="inputScores[categoryIndex].scoreItem[pointIndex].inputScore"
                  :min="0"
                  :max="Number(item.score)"
                  :disabled="disabled"
                  class="w-24"
                  size="small"
                  :step="0.1"
                  :precision="1"
                  :controls="true"
                  placeholder="0.0"
                  @change="(val: any) => handleScoreInput(categoryIndex, pointIndex, val)"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.tags-container {
  margin-bottom: 16px;

  .tag-list {
    display: flex;
    flex-direction: column;

    .el-checkbox-group {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
    }

    .tag-item {
      margin-bottom: 8px;
    }
  }

  .score-section {
    .score-category {
      background-color: #f9f9f9;
      padding: 12px;
      border-radius: 4px;

      .category-header {
        background-color: #fff;
        padding: 12px;
        border-radius: 4px;
        margin-bottom: 12px;

        .category-image-upload {
          .upload-label {
            font-weight: 500;
            color: #606266;
            margin-bottom: 8px;
          }

          .category-image-uploader {
            :deep(.el-upload--picture-card) {
              width: 80px;
              height: 80px;
              line-height: 80px;
              font-size: 24px;
            }

            :deep(.el-upload-list--picture-card .el-upload-list__item) {
              width: 80px;
              height: 80px;
            }

            :deep(.el-upload-list__item-thumbnail) {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }

          .upload-tips {
            color: #909399;
            font-size: 11px;
          }
        }
      }

      .score-items {
        .score-item {
          padding: 12px;
          border-bottom: 1px dashed #e0e0e0;
          background-color: #fff;
          border-radius: 4px;
          margin-bottom: 8px;

          &:last-child {
            border-bottom: none;
            margin-bottom: 0;
          }

          .score-title {
            font-weight: normal;
            color: #333;

            .score-max {
              font-size: 13px;
              color: #909399;
              margin-left: 4px;
            }
          }

          .score-input-container {
            display: flex;
            align-items: center;
          }
        }
      }
    }
  }

  .selected-tags {
    background-color: #f5f7fa;
    padding: 12px;
    border-radius: 4px;
  }

  .upload-section {
    .image-uploader {
      width: 100%;

      :deep(.el-upload--picture-card) {
        width: 100px;
        height: 100px;
        line-height: 100px;
      }

      :deep(.el-upload-list--picture-card .el-upload-list__item) {
        width: 100px;
        height: 100px;
      }
    }

    .upload-tips {
      color: #909399;
    }
  }
}
</style>
