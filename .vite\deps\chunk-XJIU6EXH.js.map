{"version": 3, "sources": ["../../node_modules/@form-create/utils/lib/type.js"], "sourcesContent": ["const is = {\n    type(arg, type) {\n        return Object.prototype.toString.call(arg) === '[object ' + type + ']'\n    },\n    Undef(v) {\n        return v === undefined || v === null\n    },\n    Element(arg) {\n        return typeof arg === 'object' && arg !== null && arg.nodeType === 1 && !is.Object(arg)\n    },\n    trueArray(data) {\n        return Array.isArray(data) && data.length > 0;\n    },\n    Function(v) {\n        const type = this.getType(v);\n        return type === 'Function' || type === 'AsyncFunction';\n    },\n    getType(v) {\n        const str = Object.prototype.toString.call(v)\n        return /^\\[object (.*)\\]$/.exec(str)[1]\n    },\n    empty(value) {\n        if (value === undefined || value === null) {\n            return true;\n        }\n\n        if (Array.isArray(value) && Array.isArray(value) && !value.length) {\n            return true;\n        }\n\n        return typeof value === 'string' && !value;\n    }\n};\n\n['Date', 'Object', 'String', 'Boolean', 'Array', 'Number'].forEach(t => {\n    is[t] = function (arg) {\n        return is.type(arg, t);\n    }\n})\n\nexport function hasProperty(rule, k) {\n    return ({}).hasOwnProperty.call(rule, k)\n}\n\nexport default is;\n"], "mappings": ";AAAA,IAAM,KAAK;AAAA,EACP,KAAK,KAAK,MAAM;AACZ,WAAO,OAAO,UAAU,SAAS,KAAK,GAAG,MAAM,aAAa,OAAO;AAAA,EACvE;AAAA,EACA,MAAM,GAAG;AACL,WAAO,MAAM,UAAa,MAAM;AAAA,EACpC;AAAA,EACA,QAAQ,KAAK;AACT,WAAO,OAAO,QAAQ,YAAY,QAAQ,QAAQ,IAAI,aAAa,KAAK,CAAC,GAAG,OAAO,GAAG;AAAA,EAC1F;AAAA,EACA,UAAU,MAAM;AACZ,WAAO,MAAM,QAAQ,IAAI,KAAK,KAAK,SAAS;AAAA,EAChD;AAAA,EACA,SAAS,GAAG;AACR,UAAM,OAAO,KAAK,QAAQ,CAAC;AAC3B,WAAO,SAAS,cAAc,SAAS;AAAA,EAC3C;AAAA,EACA,QAAQ,GAAG;AACP,UAAM,MAAM,OAAO,UAAU,SAAS,KAAK,CAAC;AAC5C,WAAO,oBAAoB,KAAK,GAAG,EAAE,CAAC;AAAA,EAC1C;AAAA,EACA,MAAM,OAAO;AACT,QAAI,UAAU,UAAa,UAAU,MAAM;AACvC,aAAO;AAAA,IACX;AAEA,QAAI,MAAM,QAAQ,KAAK,KAAK,MAAM,QAAQ,KAAK,KAAK,CAAC,MAAM,QAAQ;AAC/D,aAAO;AAAA,IACX;AAEA,WAAO,OAAO,UAAU,YAAY,CAAC;AAAA,EACzC;AACJ;AAEA,CAAC,QAAQ,UAAU,UAAU,WAAW,SAAS,QAAQ,EAAE,QAAQ,OAAK;AACpE,KAAG,CAAC,IAAI,SAAU,KAAK;AACnB,WAAO,GAAG,KAAK,KAAK,CAAC;AAAA,EACzB;AACJ,CAAC;AAEM,SAAS,YAAY,MAAM,GAAG;AACjC,SAAQ,CAAC,EAAG,eAAe,KAAK,MAAM,CAAC;AAC3C;AAEA,IAAO,eAAQ;", "names": []}