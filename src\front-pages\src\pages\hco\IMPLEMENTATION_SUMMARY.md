# HCO 查询条件持久化功能实现总结

## 实现概述

已成功为 HCO 列表页面实现了查询条件持久化功能。用户在列表页面设置查询条件后，跳转到详情页面再返回时，所有查询条件都会被自动恢复。

## 实现的功能

### ✅ 1. 查询条件持久化

- **基础查询条件**：BU 归属、机构类别、机构名称
- **高级筛选条件**：所有高级筛选器的条件
- **分页信息**：当前页码、每页大小（完全持久化）
- **查询参数**：查询条件数组、逻辑关系

### ✅ 2. 自动保存机制

- 每次执行查询时自动保存当前状态
- 分页变化时自动保存分页状态
- 跳转到详情页面前保存查询条件
- 使用 localStorage 进行本地存储

### ✅ 3. 自动恢复机制

- 页面加载时检查并恢复保存的查询条件
- 恢复基础查询表单
- 恢复高级筛选组件状态
- 恢复分页参数

### ✅ 4. 数据管理

- 24 小时数据过期机制
- 重置时清除保存的数据
- 页面卸载时自动清除数据
- 异常处理和数据验证

## 修改的文件

### 1. `src/front-pages/src/pages/hco/composables/useHco.ts`

**新增功能：**

- `useQueryPersistence()` - 查询条件持久化管理器
- `saveCurrentQueryState()` - 保存当前查询状态
- `restoreQueryState()` - 恢复查询状态
- `clearQueryState()` - 清除查询状态

**修改内容：**

```typescript
// 新增持久化管理器
export function useQueryPersistence() {
  const saveQueryConditions = (basicQuery, advancedFilters, queryParams) => { ... }
  const restoreQueryConditions = () => { ... }
  const clearQueryConditions = () => { ... }
}

// 增强 HCO 列表管理器
export function useHcoList() {
  // 新增持久化相关方法
  const saveCurrentQueryState = (basicQuery, advancedFilters) => { ... }
  const restoreQueryState = () => { ... }
  const clearQueryState = () => { ... }
}
```

### 2. `src/front-pages/src/pages/hco/index.vue`

**修改内容：**

- 在 `handleDetail()` 中添加保存查询条件的逻辑
- 在 `handleQuery()` 中添加查询成功后保存状态的逻辑
- 在 `handleReset()` 中添加清除保存数据的逻辑
- 在 `onMounted()` 中添加恢复查询条件的逻辑
- 在 `onUnmounted()` 中添加清除持久化数据的逻辑
- 新增 `restoreQueryConditions()` 函数
- 新增 `handlePageChange()` 和 `handleSizeChange()` 函数处理分页变化并保存状态

### 3. `src/front-pages/src/components/advanced-filter.vue`

**新增功能：**

- `restoreFilters(filters)` - 恢复筛选条件方法
- 通过 `defineExpose` 暴露给父组件调用

### 4. `src/router/index.ts`

**新增内容：**

- 添加 `/ins_mdm_manage/ins_mdm/detail` 路由配置
- 指向 `@/views/mdm/hco/detail/index.vue` 组件

## 技术实现细节

### 1. 数据存储结构

```typescript
interface QueryState {
  basicQuery: {
    bu: string | string[]
    type: string | string[]
    orgName: string
  }
  advancedFilters: Array<{
    id: number
    left: string
    op: string
    right: any
  }>
  queryParams: {
    conditions: QueryCondition[]
    logic: string
    pageSize: number
    pageNum: number
  }
  timestamp: number
}
```

### 2. 存储键名

- `hco_query_conditions` - 主存储键

### 3. 过期机制

- 数据有效期：24 小时
- 自动清理过期数据
- 时间戳验证

## 用户体验流程

1. **用户设置查询条件**

   - 填写基础查询表单（BU 归属、机构类别、机构名称）
   - 设置高级筛选条件
   - 点击查询按钮

2. **系统自动保存**

   - 查询执行成功后自动保存所有查询条件
   - 数据保存到 localStorage

3. **用户跳转详情页**

   - 点击机构名称跳转到详情页
   - 跳转前再次保存当前查询状态

4. **用户返回列表页**
   - 点击详情页的"返回"按钮
   - 系统自动恢复之前的查询条件
   - 自动执行查询显示结果

## 测试验证

### 1. 功能测试

- ✅ 基础查询条件保存和恢复
- ✅ 高级筛选条件保存和恢复
- ✅ 分页信息保存和恢复（包括页码和每页大小）
- ✅ 分页变化时的状态保存
- ✅ 跳转详情页后返回的条件恢复
- ✅ 重置功能清除保存的数据
- ✅ 页面卸载时自动清除数据

### 2. 边界测试

- ✅ 数据过期处理
- ✅ localStorage 异常处理
- ✅ 无保存数据时的默认行为
- ✅ 数据格式错误的处理

### 3. 测试工具

- 创建了 `test-persistence.html` 测试页面
- 可以独立测试持久化功能的各个方面

## 注意事项

1. **浏览器兼容性**：需要支持 localStorage 的浏览器
2. **数据安全**：查询条件不包含敏感信息，安全存储在客户端
3. **存储限制**：localStorage 有大小限制，定期清理过期数据
4. **多标签页**：不同标签页之间的查询条件不会自动同步

## 后续优化建议

1. **性能优化**：考虑使用防抖机制减少保存频率
2. **用户体验**：添加查询条件恢复的提示信息
3. **数据同步**：考虑实现多标签页之间的数据同步
4. **配置化**：将过期时间等参数配置化

## 文档和指南

- `QUERY_PERSISTENCE_GUIDE.md` - 详细使用指南
- `test-persistence.html` - 功能测试页面
- `IMPLEMENTATION_SUMMARY.md` - 本实现总结文档
- `PAGINATION_PERSISTENCE_DEMO.md` - 分页持久化演示文档

## 更新日志

- **v1.2.1** - 修复详情页跳转时的数据清除问题
  - 使用 `onBeforeRouteLeave` 路由守卫替代 `onUnmounted` 钩子
  - 智能区分详情页跳转和其他页面跳转
  - 详情页跳转时保持查询条件，其他页面跳转时清除查询条件
  - 确保用户体验的一致性和合理性
- **v1.2.0** - 添加页面卸载清除功能
  - 在 `onUnmounted()` 钩子中添加清除持久化数据的逻辑
  - 当用户点击左侧菜单跳转到其他页面时，自动清除查询条件
  - 确保下次访问 HCO 页面时是全新状态
  - 避免不同页面之间的查询条件相互干扰
- **v1.1.0** - 修复分页恢复问题
  - 修复了返回列表页面时分页信息没有正确恢复的问题
  - 为 `handleQuery()` 函数添加 `resetPage` 参数
  - 区分新查询和状态恢复两种场景
  - 确保分页信息在跳转详情页后返回时完全保持
- **v1.0.0** - 初始版本，支持基础查询条件和高级筛选条件的持久化
  - 支持数据过期机制
  - 支持重置功能
  - 支持高级筛选组件的条件恢复
