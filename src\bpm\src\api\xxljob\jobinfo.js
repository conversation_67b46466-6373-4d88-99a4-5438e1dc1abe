import request from '@/utils/request';
import { tansParams } from '@/utils/ruoyi';

export function jobinfoPage(data) {
  return request({
    url: '/sdr/job-info/pageList',
    method: 'post',
    data: tansParams(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8' }
  });
}
export function jobinfoAdd(data) {
  return request({
    url: '/sdr/job-info/add',
    method: 'post',
    data: tansParams(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8' }
  });
}

export function jobinfoUpdate(data) {
  return request({
    url: '/sdr/job-info/update',
    method: 'post',
    data: tansParams(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8' }
  });
}

export function jobcodeSave(data) {
  return request({
    url: '/sdr/job-code/save',
    method: 'post',
    data: tansParams(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8' }
  });
}

export function jobinfoRemove(data) {
  return request({
    url: '/sdr/job-info/remove',
    method: 'post',
    data: tansParams(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8' }
  });
}

export function jobinfoTrigger(data) {
  return request({
    url: '/sdr/job-info/trigger',
    method: 'post',
    data: tansParams(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8' }
  });
}

export function jobinfoNextTriggerTime(data) {
  return request({
    url: '/sdr/job-info/nextTriggerTime',
    method: 'post',
    data: tansParams(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8' }
  });
}

export function jobinfoStart(data) {
  return request({
    url: '/sdr/job-info/start',
    method: 'post',
    data: tansParams(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8' }
  });
}

export function jobinfoStop(data) {
  return request({
    url: '/sdr/job-info/stop',
    method: 'post',
    data: tansParams(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8' }
  });
}
