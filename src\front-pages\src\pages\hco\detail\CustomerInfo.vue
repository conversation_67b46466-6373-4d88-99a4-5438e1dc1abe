<template>
  <div class="customer-info">
    <div class="search-container">
      <el-form :model="filterParams" :inline="true" label-width="80px" class="filter-form">
        <el-row :gutter="16">
          <el-col :span="6">
            <el-form-item label="客户姓名">
              <el-input v-model="filterParams.name" placeholder="请输入客户姓名" clearable @keyup.enter="handleSearch" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="职务">
              <el-input v-model="filterParams.job" placeholder="请输入职务" clearable @keyup.enter="handleSearch" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="挂牌科室">
              <el-input v-model="filterParams.insDeptName" placeholder="请输入挂牌科室" clearable @keyup.enter="handleSearch" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="客户级别">
              <el-input v-model="filterParams.doctorLvl" placeholder="请输入客户级别" clearable @keyup.enter="handleSearch" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="16">
          <el-col :span="6">
            <el-form-item label="是否启用">
              <el-radio-group v-model="filterParams.status" @change="handleSearch">
                <el-radio :label="'0'">是</el-radio>
                <el-radio :label="'1'">否</el-radio>
                <el-radio :label="''">全部</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="18">
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleSearch">查询</el-button>
              <el-button icon="Refresh" @click="handleReset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <el-table :data="filteredTableData" v-loading="hcpList.loading.value" style="width: 100%;">
      <el-table-column prop="wbId" label="客户ID" fixed="left" min-width="30" />
      <el-table-column prop="wbCode" label="客户编码" fixed="left" width="100" />
      <el-table-column prop="name" label="客户姓名" fixed="left" width="100" />
      <el-table-column prop="gender" label="性别" width="80" />
      <el-table-column prop="professionTechTitle" label="专业技术职称" />
      <el-table-column prop="insDeptName" label="挂牌科室" />
      <el-table-column prop="job" label="职务" />
      <el-table-column prop="doctorLvl" label="客户级别">
        <template #default="{ row }">
          <el-tag :type="getLevelTagType(row.doctorLvl)" size="small">{{ row.doctorLvl || '-' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="isMain" label="是否主院">
        <template #default="{ row }">
          <el-tag :type="row.isMain === '1' ? 'success' : 'info'" size="small">
            {{ row.isMain === '1' ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="是否启用">
        <template #default="{ row }">
          <el-tag :type="row.status === '0' ? 'success' : 'danger'" size="small">
            {{ row.status === '0' ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[20, 30, 50]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { useHcpList } from '../composables/useHco';

const route = useRoute();
const hcpList = useHcpList();

// 定义props
const props = defineProps({
  insDsSpecCode: {
    type: String,
    required: true
  }
});

// 筛选参数
const filterParams = ref({
  name: '',
  job: '',
  insDeptName: '',
  doctorLvl: '',
  status: '0' // 默认为是（启用）
});

// 分页参数
const currentPage = ref(1);
const pageSize = ref(20);

// 获取HCP数据
const fetchHcpData = async () => {
  const insCode = props.insDsSpecCode || route.query.insCode || route.query.insDsSpecCode;
  if (insCode) {
    await hcpList.fetchHcpList({
      insCode,
      ...filterParams.value,
      pageSize: pageSize.value,
      pageNum: currentPage.value
    });
  }
};

// 过滤后的表格数据
const filteredTableData = computed(() => {
  let result = [...hcpList.tableData.value];

  // 根据筛选条件过滤数据
  // if (filterParams.value.name) {
  //   result = result.filter(item =>
  //     item.name && item.name.includes(filterParams.value.name)
  //   );
  // }

  // if (filterParams.value.job) {
  //   result = result.filter(item =>
  //     item.job && item.job.includes(filterParams.value.job)
  //   );
  // }

  // if (filterParams.value.department) {
  //   result = result.filter(item =>
  //     item.teachTitle && item.teachTitle.includes(filterParams.value.department)
  //   );
  // }

  // if (filterParams.value.customerLevel) {
  //   result = result.filter(item =>
  //     item.level && item.level.includes(filterParams.value.customerLevel)
  //   );
  // }

  // if (filterParams.value.status !== '') {
  //   result = result.filter(item =>
  //     item.status === filterParams.value.status
  //   );
  // }

  return result;
});

// 总数
const total = computed(() => {
  return hcpList.total.value;
});

// 获取客户级别标签类型
const getLevelTagType = (level) => {
  const map = {
    'A': 'success',
    'B': 'warning',
    'C': 'info',
    'D': ''
  };
  return map[level] || 'info';
};



// 搜索
const handleSearch = () => {
  currentPage.value = 1;
  fetchHcpData();
};

// 重置筛选条件
const handleReset = () => {
  filterParams.value = {
    name: '',
    job: '',
    department: '',
    customerLevel: '',
    status: '0' // 重置为默认值（启用）
  };
  currentPage.value = 1;
  fetchHcpData();
};

// 分页大小改变
const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
  fetchHcpData();
};

// 当前页改变
const handleCurrentChange = (val) => {
  currentPage.value = val;
  fetchHcpData();
};

// 页面加载时获取数据
onMounted(() => {
  fetchHcpData();
});
</script>

<style lang="scss" scoped>
.customer-info {
  .search-container {
    margin-bottom: 20px;
    // padding: 16px;
    // background-color: #f5f7fa;
    border-radius: 4px;

    .filter-form {
      .el-form-item {
        margin-bottom: 16px;
      }

      .el-input {
        width: 100%;
      }

      .el-radio-group {
        .el-radio {
          margin-right: 16px;
        }
      }
    }
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}

:deep(.el-table) {
  .el-table__header th {
    font-weight: 600;
    color: #1f2d3d;
    background-color: #f5f7fa;
  }
}

.el-tag {
  border-radius: 2px;
}
</style>
