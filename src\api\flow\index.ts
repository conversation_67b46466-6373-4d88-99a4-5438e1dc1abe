import request from '@/utils/request';

export const createProcessApi = (data: any) => {
  return request({
    url: `/bpm/workflow/process/create`,
    method: 'post',
    data
  });
};

export const updateProcessApi = (data: any) => {
  return request({
    url: `/bpm/workflow/process/update`,
    method: 'post',
    data
  });
};
export const processListApi = (query: any) => {
  return request({
    url: `/bpm/workflow/process/page`,
    method: 'get',
    params: query
  });
};
export const processCheckOutApi = (id: any) => {
  return request({
    url: `/bpm/workflow/process/checkingOut/${id}`,
    method: 'post'
  });
};
export const processDetailApi = (id: any) => {
  return request({
    url: `/bpm/workflow/process/${id}`,
    method: 'get'
  });
};
export const fromModifyApi = (data: any) => {
  return request({
    url: `/bpm/form/update`,
    method: 'put',
    data
  });
};
export const formListApi = (data: any) => {
  return request({
    url: `/bpm/form/page`,
    method: 'get',
    params: data
  });
};
export const formDetailApi = (id: any) => {
  return request({
    url: `/bpm/form/${id}`,
    method: 'get'
  });
};
export const formDeleteApi = (id: any) => {
  return request({
    url: `/bpm/form/${id}`,
    method: 'delete'
  });
};

// 发启流程
export const startProcessApi = (data: any) => {
  return request({
    url: `/bpm/workflow/process/start`,
    method: 'post',
    data
  });
};

// 我发起的流程
export const taskApplyListApi = (data: any) => {
  return request({
    url: `/bpm/workflow/task/applyList`,
    method: 'post',
    data
  });
};

// 同意
export const agreeTaskApi = (data: any) => {
  return request({
    url: `/bpm/workflow/task/agree`,
    method: 'post',
    data
  });
};

// 拒绝
export const refuseTaskApi = (data: any) => {
  return request({
    url: `/bpm/workflow/task/refuse`,
    method: 'post',
    data
  });
};

// 撤回
export const revokeTaskApi = (data: any) => {
  return request({
    url: `/bpm/workflow/task/revoke`,
    method: 'post',
    data
  });
};

// 待审批任务
export const todoTaskApi = (data: any) => {
  return request({
    url: `/bpm/workflow/task/todoTask`,
    method: 'post',
    data
  });
};

// 抄送我的
export const ccTaskListApi = (data: any) => {
  return request({
    url: `/bpm/workflow/task/ccList`,
    method: 'post',
    data
  });
};

// 已审批列表
export const doneListApi = (data: any) => {
  return request({
    url: `/bpm/workflow/task/doneList`,
    method: 'post',
    data
  });
};

// 查询实例表单数据（审批表单）
/*
* processId
integer <int64>
必需
instanceId
string
* */
export const taskInstanceFormApi = (data: any) => {
  return request({
    url: `/bpm/workflow/task/instanceForm`,
    method: 'get',
    params: data
  });
};
// 查询实例动作记录(审批记录)
/*
必需
instanceId
string
* */
export const taskInstanceActionApi = (data: any) => {
  return request({
    url: `/bpm/workflow/task/instanceAction`,
    method: 'get',
    params: data
  });
};
// 查询实例现状
/*
必需
instanceId
string
* */
export const taskInstanceDetailApi = (data: any) => {
  return request({
    url: `/bpm/workflow/task/instanceHistory`,
    method: 'get',
    params: data
  });
};
// 查询节点信息(节点信息)
/*
processId
string
必需
nodeKey
string
必需
* */
export const taskNodeInfoApi = (data: any) => {
  return request({
    url: `/bpm/workflow/task/getNodeInfo`,
    method: 'get',
    params: data
  });
};

export const dataObjectPageApi = (data: any) => {
  return request({
    url: `/bpm/data/object/page`,
    method: 'get',
    params: data
  });
};

// 新增/编辑数据对象
export const dataObjectModifyApi = (data: any) => {
  return request({
    url: `/bpm/data/object/update`,
    method: 'put',
    data
  });
};

// 删除项目数据对象
export const dataObjectDelApi = (id: any) => {
  return request({
    url: `/bpm/data/object/${id}`,
    method: 'delete'
  });
};

// 数据对象详情
export const dataObjectDetailApi = (id: any) => {
  return request({
    url: `/bpm/data/object/${id}`,
    method: 'get'
  });
};

export const getFileInfo = (id: any) => {
  return request({
    url: `fle/oss-file/tenant/${id}`,
    method: 'get'
  });
};

// 查询消息模板
export const messageTempListApi = () => {
  return request({
    url: `/bpm/message/template/list`,
    method: 'get'
  });
};
