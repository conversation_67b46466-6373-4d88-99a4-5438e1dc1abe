@import './variables.module.scss';

@mixin colorBtn($color) {
  background: $color;

  &:hover {
    color: $color;

    &:before,
    &:after {
      background: $color;
    }
  }
}

.blue-btn {
  @include colorBtn($blue);
}

.light-blue-btn {
  @include colorBtn($light-blue);
}

.red-btn {
  @include colorBtn($red);
}

.pink-btn {
  @include colorBtn($pink);
}

.green-btn {
  @include colorBtn($green);
}

.tiffany-btn {
  @include colorBtn($tiffany);
}

.yellow-btn {
  @include colorBtn($yellow);
}

.pan-btn {
  font-size: 14px;
  color: #fff;
  padding: 14px 36px;
  border-radius: 8px;
  border: none;
  outline: none;
  transition: 600ms ease all;
  position: relative;
  display: inline-block;

  &:hover {
    background: #fff;

    &:before,
    &:after {
      width: 100%;
      transition: 600ms ease all;
    }
  }

  &:before,
  &:after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    height: 2px;
    width: 0;
    transition: 400ms ease all;
  }

  &::after {
    right: inherit;
    top: inherit;
    left: 0;
    bottom: 0;
  }
}

.custom-button {
  display: inline-block;
  line-height: 1;
  white-space: nowrap;
  cursor: pointer;
  background: #fff;
  color: #fff;
  -webkit-appearance: none;
  text-align: center;
  box-sizing: border-box;
  outline: 0;
  margin: 0;
  padding: 10px 15px;
  font-size: 14px;
  border-radius: 4px;
}

.button-hover-icon {
  margin-right :5px; 
}
.el-button--info:hover {
  .button-hover-icon {
    // stroke:#FFFFFF;
  }
}
.el-button {
  border-style:none !important;
}

.el-button.el-button--primary {
  --el-button-text-color: var(--el-color-white);
    --el-button-bg-color: var(--el-color-primary);
    --el-button-border-color: var(--el-color-primary);
    --el-button-outline-color: var(--el-color-primary-light-5);
    --el-button-hover-text-color: var(--el-color-white);
    --el-button-hover-link-text-color: var(--el-color-primary-light-5);
    --el-button-hover-bg-color: var(--unnamed, #5174F5) !important;
    --el-button-hover-border-color: var(--el-color-primary-light-3);
    --el-button-active-bg-color: var(--unnamed, #1F36D8)!important;
    --el-button-active-border-color: var(--el-color-primary-dark-2);
    --el-button-disabled-text-color: var(--el-color-white);
    --el-button-disabled-bg-color: var(--unnamed, #92A8F8) !important;
    --el-button-disabled-border-color: var(--el-color-primary-light-5);

}
.el-button--info.is-plain {
     --el-button-text-color:var(--neutral-color-2, #4E595E) !important;
    --el-button-bg-color: var(--el-color-info-light-9);
    --el-button-border-color: var(--el-color-info-light-5);
    --el-button-hover-text-color:  var(--neutral-color-2, #4E595E) !important;
    --el-button-hover-bg-color: var(--neutral-color-3, #E3E4E9) !important;
    --el-button-hover-border-color: var(--el-color-info);
    --el-button-active-text-color: var(--el-color-white);
    --el-button-active-bg-color: var(--neutral-color-4, #C6CAD1) !important;
    --el-button-active-text-color: var(--neutral-color-2, #4E595E) !important;
    --el-button-disabled-bg-color:var(--neutral-color-1, #F5F6F8) !important;
      
}

.el-button--danger.is-plain {
  --el-button-text-color: #F54A45 !important;
  --el-button-bg-color: #FEEDEC !important;
  --el-button-border-color: var(--el-color-danger-light-5);
  --el-button-hover-text-color: var(--el-color-white);
  --el-button-hover-bg-color: var(--el-color-danger);
  --el-button-hover-border-color: var(--el-color-danger);
  --el-button-active-text-color: var(--el-color-white);
}

.el-button--warning.is-link{
  --el-button-text-color: var(--unnamed, #FFB637) !important;
}

.el-button.el-button--danger.el-button--default {
  // background-color: #F54A45 !important;
}