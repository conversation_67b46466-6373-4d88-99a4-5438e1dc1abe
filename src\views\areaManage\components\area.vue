<template>
  <el-card shadow="hover" v-loading="state.globalLoading">
    <div class="deparment">
      <!-- 树结构 -->
      <div class="left">
        <DepTree @handleNodeClick="handleNodeClick" @getList="resetGetList" ref="depTreeRef" @allList="getAllList" />
      </div>
      <div class="w-[100%] overflow-y-auto">
        <transition>
          <el-card shadow="hover">
            <div class="institutionManagement">
              <div class="flex justify-between">
                <el-form :model="state.queryParams" ref="queryFormRef" :inline="true" label-width="76px" class="query-form">
                  <el-row>
                    <el-col :span="8">
                      <el-form-item label="辖区编码" prop="jurCode">
                        <el-input
                          v-model="state.queryParams.jurCode"
                          placeholder="请输入辖区编码"
                          @keyup.enter="handleQuery"
                          @input="handleQuery"
                          style="width: 100%;"
                        >
                          <template #suffix>
                            <el-icon>
                              <Search />
                            </el-icon>
                          </template>
                        </el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="辖区负责人" prop="userName" label-width="90">
                        <el-input
                          v-model="state.queryParams.userName"
                          placeholder="请输入负责人姓名"
                          style="width:240px"
                          @keyup.enter="handleQuery()"
                          @input="handleQuery()"
                        >
                          <template #suffix>
                            <el-icon>
                              <Search />
                            </el-icon>
                          </template>
                        </el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </div>
              <el-divider style="margin: 12px 0;" />
              <el-button type="primary" @click="handleAdd" class="mb-[12px]">新增</el-button>
              <el-card shadow="hover">
                <el-table :data="state.tableData" :loading="state.loading" :border="false" style="width: 100%;">
                  <el-table-column label="辖区编码" prop="jurCode" align="left" min-width="120">
                    <template #default="{ row }">
                      <el-button link type="primary" @click="handleDetail(row)">{{ row.jurCode }}</el-button>
                    </template>
                  </el-table-column>
                  <el-table-column label="辖区负责人" prop="userName" align="left" min-width="120" />
                  <el-table-column label="工号" prop="userCode" align="left" min-width="120" />
                  <el-table-column label="部门" prop="deptName" align="left" min-width="120" />
                  <el-table-column label="岗位" prop="postName" align="left" min-width="120" />
                  <el-table-column label="岗位编码" prop="postCode" align="left" min-width="120" />
                  <el-table-column label="辖区DTP药店数" prop="dtpDrugCount" align="left" min-width="120" />
                  <el-table-column label="辖区类型" prop="jurType" align="left" min-width="120" />
                  <el-table-column label="产品" prop="productName" align="left" min-width="120" />
                  <el-table-column label="辖区状态" prop="status" align="left" min-width="120" />
                  <el-table-column label="直属上级" prop="leaderName" align="left" min-width="120" :show-overflow-tooltip="true" />
                  <el-table-column label="直属上级工号" prop="leaderCode" align="left" min-width="120" :show-overflow-tooltip="true" />
                </el-table>
                <pagination
                  v-show="state.total > 10"
                  layout="total,prev, pager, sizes,jumper"
                  :page-sizes="[10, 20, 30, 50, 100]"
                  :total="state.total"
                  v-model:page="state.queryParams.pageNum"
                  v-model:limit="state.queryParams.pageSize"
                  @pagination="getList"
                />
              </el-card>
            </div>
          </el-card>
        </transition>
      </div>
    </div>
  </el-card>

  <!-- 新增辖区抽屉 -->
  <add-jurisdiction v-model="state.drawerVisible" @success="getList" />
</template>
<script setup>
import DepTree from './deptree.vue';
import AddJurisdiction from './addJurisdiction.vue';
import { getJurisdictionList, updateJurisdiction } from './../api'
import { getCurrentInstance } from 'vue'
import { useRouter } from 'vue-router';
import { ElMessageBox } from 'element-plus';

const router = useRouter();
const { proxy } = getCurrentInstance();

const state = reactive({
  loading: false,
  globalLoading: false,
  submitLoading: false,
  drawerVisible: false,
  queryParams: {
    jurCode: '',
    userName: '',
    pageSize: 10,
    pageNum: 1,
    deptId: undefined
  },
  addForm: {
    deptId: undefined,
    postId: undefined,
    userName: '',
    jurType: '',
    productCodeList: [],
    operateType: 'insert'
  },
  addRules: {
    deptId: [{ required: true, message: '请选择部门', trigger: 'change' }],
    postId: [{ required: true, message: '请选择岗位', trigger: 'change' }],
    userName: [{ required: true, message: '请输入辖区负责人', trigger: 'blur' }],
    jurType: [{ required: true, message: '请选择辖区类型', trigger: 'change' }],
    productCodeList: [{ required: true, message: '请选择产品', trigger: 'change' }]
  },
  deptTreeData: [],
  postOptions: [],
  productOptions: [],
  insLevelOption: [],
  distList: [],
  insGradeOption: [],

  tableData: [],
  total: 0,
  fileList: [],
  selectids: [],
  pcdvalue: ''
});


const handleQuery = () => {
  state.queryParams.pageNum = 1;
  state.queryParams.pageSize = 10;
  getList();
};

const getList = async () => {
  state.loading = true;
  state.globalLoading = true;
  try {
    const res = await getJurisdictionList(state.queryParams);
    state.tableData = res.data?.rows || [];
    state.total = res.data?.total || 0;
  } catch (error) {
    console.error('Failed to fetch jurisdiction list:', error);
    proxy?.$modal.msgError('获取数据失败');
  } finally {
    state.loading = false;
    state.globalLoading = false;
  }
};

const handleNodeClick = async (row) => {
  state.queryParams.deptId = row.deptId;
  state.deptRow = row;
  handleQuery();
  if (!row?.isChildren) {
    state.exportDeptlist = [row.deptId];
  }
};
const resetGetList = () => {
  state.queryParams.deptId = undefined;
  getList();
};
const getAllList = (list) => {
  state.exportDeptlist = list.map((item) => item.deptId);
};

const handleAdd = () => {
  state.drawerVisible = true;
  state.addForm = {
    deptId: undefined,
    postId: undefined,
    userName: '',
    jurType: '',
    productCodeList: [],
    operateType: 'insert'
  };
};

const handleSubmit = async () => {
  if (!proxy.$refs.addFormRef) return;

  await proxy.$refs.addFormRef.validate(async (valid) => {
    if (valid) {
      state.submitLoading = true;
      try {
        const res = await updateJurisdiction(state.addForm);
        if (res.code === 200) {
          proxy.$modal.msgSuccess('新增成功');
          state.drawerVisible = false;
          getList();
        } else {
          proxy.$modal.msgError(res.msg || '新增失败');
        }
      } catch (error) {
        console.error('Failed to add jurisdiction:', error);
        proxy.$modal.msgError('新增失败');
      } finally {
        state.submitLoading = false;
      }
    }
  });
};

const handleDetail = (row) => {
  router.push({
    path: '/area_management_dtp/jur_detail',
    query: {
      jurCode: row.jurCode
    }
  });
};

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确认要删除该辖区吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    state.globalLoading = true;
    const res = await updateJurisdiction({
      ...row,
      operateType: 'delete'
    });

    if (res.code === 200) {
      proxy.$modal.msgSuccess('删除成功');
      getList();
    } else {
      proxy.$modal.msgError(res.msg || '删除失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to delete jurisdiction:', error);
      proxy.$modal.msgError('删除失败');
    }
  } finally {
    state.globalLoading = false;
  }
};

onMounted(() => {
  getList();
});
</script>
<style lang="scss" scoped>
.institutionManagement {
  padding: 16px 16px 0 16px;
}

.deparment {
  display: flex;
  width: 100%;
  height: calc(100vh - 250px)
}

.left {
  // width: 253px;
  border-right: 1px solid #e5e6eb;
  padding-right: 16px;
  overflow-y: auto;
}

.right {
  width: calc(100% - 269px);
  padding-left: 16px;
}
</style>
