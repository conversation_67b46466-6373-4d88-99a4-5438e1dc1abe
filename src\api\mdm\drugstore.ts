import request from '@/utils/request';
import { AxiosPromise } from 'axios';

export const examineList = (data: any): AxiosPromise<any> => {
  return request({
    url: `/msr/bpm/page`,
    method: 'get',
    params: data
  });
};

export const examineDetail = (data: any): AxiosPromise<any> => {
  return request({
    url: `/msr/bpm/${data}`,
    method: 'get'
  });
};
export const impTasList = (data: any): AxiosPromise<any> => {
  return request({
    url: `/plt/imp-task/list`,
    method: 'get',
    params: data
  });
};
export const getLogListApi = (data: any): AxiosPromise<any> => {
  return request({
    url: `/msr/drugstore/oprLogList`,
    method: 'get',
    params: data
  });
};
export const getAffLogListApi = (data: any): AxiosPromise<any> => {
  return request({
    url: `/msr/relation/drugstore/oprLogList`,
    method: 'get',
    params: data
  });
};

export const getPharmacyLogListApi = (bpmId: any): AxiosPromise<any> => {
  return request({
    url: `/msr/bpm/log/page?bpmId=${bpmId}`,
    method: 'get'
    // data: query
  });
};
export const getListHttp = (query: any): AxiosPromise<any> => {
  return request({
    url: '/msr/drugstore/list',
    method: 'get',
    params: query
  });
};
export const getMdmListHttp = (query: any): AxiosPromise<any> => {
  return request({
    url: '/mdm/gs-institution/drugstore/list',
    method: 'get',
    params: query
  });
};
export const changeStatus = ({ ids, status }: any): AxiosPromise<any> => {
  return request({
    url: `/msr/drugstore/changeStatus/${ids.join(',')}`,
    method: 'get',
    params: {
      status
    }
  });
};
export const deleteInstitution = (id: any): AxiosPromise<any> => {
  return request({
    url: `/msr/institution/${id}`,
    method: 'delete'
  });
};

export const institutionDetail = (id: any): AxiosPromise<any> => {
  return request({
    url: `/msr/drugstore/${id}`,
    method: 'get'
  });
};

export const mdmInstitutionDetail = (params: any): AxiosPromise<any> => {
  return request({
    url: `/mdm/gs-institution/drugstore/detail`,
    method: 'get',
    params
  });
};
export const phaDetail = (id: any): AxiosPromise<any> => {
  return request({
    url: `/msr/bpm/${id}`,
    method: 'get'
  });
};

export const drugstoreBeforeMerge = (query: any): AxiosPromise<any> => {
  return request({
    url: `/msr/drugstore/before-merge`,
    method: 'get',
    params: query
  });
};
export const setDictList = (type: string): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=${type}`,
    method: 'get'
  });
};
export const getProductList = (params: boolean): AxiosPromise<any> => {
  return request({
    url: `/msr/product/crm-products`,
    method: 'get',
    params
  });
};
export const exportURL = (data: object): AxiosPromise<any> => {
  return request({
    url: `/msr/imp-task/download`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
};
export const targetAffList = (query: any): AxiosPromise<any> => {
  return request({
    url: '/msr/jur-rel-drugstore/list',
    method: 'get',
    params: query
  });
};
export const addPharmacyTableList = (query: any): AxiosPromise<any> => {
  return request({
    url: '/msr/jur-rel-drugstore/unboundRelList',
    method: 'get',
    params: query
  });
};
export const affiliatedPharmacylist = (query: any): AxiosPromise<any> => {
  return request({
    url: '/msr/rel-drugstore/list',
    method: 'get',
    params: query
  });
};

export const mdmAffiliatedPharmacylist = (query: any): AxiosPromise<any> => {
  return request({
    url: '/mdm/ins-ds/list',
    method: 'post',
    data: query,
    params: query
  });
};
export const relationDrugstorelist = (query: any): AxiosPromise<any> => {
  return request({
    url: '/msr/jur-rel-drugstore/list',
    method: 'get',
    params: query
  });
};
export const versionRelationDrugstorelist = (query: any): AxiosPromise<any> => {
  return request({
    url: '/msr/rel-drugstore/version-list',
    method: 'get',
    params: query
  });
};
export const relationDrugstorelistByIns = (query: any): AxiosPromise<any> => {
  return request({
    url: '/msr/rel-drugstore/list-by-ins',
    method: 'get',
    params: query
  });
};
export const jurRelationDrugstoreDelete = (id: any, jurCode: any): AxiosPromise<any> => {
  return request({
    url: `/msr/jur-rel-drugstore/${id}?jurCode=${jurCode}`,
    method: 'delete'
  });
};

export const relationDrugstoreDelete = (id: any): AxiosPromise<any> => {
  return request({
    url: `/msr/rel-drugstore/${id}`,
    method: 'delete'
  });
};

export const institutionImport = (data: any) => {
  return request({
    url: '/msr/jur/drugstore/relationDrugstore-template',
    method: 'post',
    data: data
  });
};

export const institutionExport = (data: any) => {
  return request({
    url: '/msr/institution/export',
    method: 'post',
    data: data
  });
};

export const cusExport = (data: any) => {
  return request({
    url: '/msr/customer/import',
    method: 'post',
    data: data
  });
};

export const relationDrugstoreDetail = (id: any): AxiosPromise<any> => {
  return request({
    url: `/msr/rel-drugstore/detail/${id}`,
    method: 'get'
  });
};
export const tarRelationDrugstoreDetail = (id: any): AxiosPromise<any> => {
  return request({
    url: `/msr/jur-rel-drugstore/detail/${id}`,
    method: 'get'
  });
};
export const institutionUpdate = (data: any) => {
  return request({
    url: '/msr/institution',
    method: 'put',
    data: data
  });
};
export const add = (data: any) => {
  return request({
    url: '/msr/institution/add',
    method: 'post',
    data: data
  });
};

export const dictTypeList = (): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=ins_level`,
    method: 'get'
  });
};

export const insProfessionTypeList = (): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=ins_profession_type`,
    method: 'get'
  });
};

export const insTypeList = (): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=ins_type`,
    method: 'get'
  });
};

export const economicTypeList = (): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=economic_type`,
    method: 'get'
  });
};

export const getInsListRes = (): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=department_label`,
    method: 'get'
  });
};
export const deptNameList = (): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=dept_name`,
    method: 'get'
  });
};

export const customerList = (query: any): AxiosPromise<any> => {
  return request({
    url: `/msr/customer/list`,
    method: 'get',
    params: query
  });
};

export const deleteCustomer = (ids: any): AxiosPromise<any> => {
  return request({
    url: `/msr/customer/${ids}`,
    method: 'delete'
  });
};

export const customerDetail = (id: any): AxiosPromise<any> => {
  return request({
    url: `/msr/customer/${id}`,
    method: 'get'
  });
};
export const createCustomer = (data: any): AxiosPromise<any> => {
  return request({
    url: `/msr/customer/add`,
    method: 'post',
    data
  });
};
export const customerUpdate = (data: any): AxiosPromise<any> => {
  return request({
    url: `/msr/customer`,
    method: 'put',
    data
  });
};
export const professionList = (): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=profession_tech_title`,
    method: 'get'
  });
};

export const administrationList = (): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=administration_lvl`,
    method: 'get'
  });
};

export const lectureLvlList = (): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=lecture_lvl`,
    method: 'get'
  });
};
export const district = (type: string): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=${type}`,
    method: 'get'
  });
};
export const jobList = (query: any): AxiosPromise<any> => {
  return request({
    url: `/msr/customer/detail`,
    method: 'get',
    params: query
  });
};
export const addCustomerType = (data: any): AxiosPromise<any> => {
  return request({
    url: `/msr/customer`,
    method: 'post',
    data
  });
};
export const customerTypeUpdate = (data: any): AxiosPromise<any> => {
  return request({
    url: `/msr/customer/update`,
    method: 'put',
    data
  });
};

export const deleteCustomerType = (ids: any): AxiosPromise<any> => {
  return request({
    url: `/msr/customer/${ids}`,
    method: 'delete'
  });
};

export const cusLevelList = (): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=cus_idea_level`,
    method: 'get'
  });
};
export const cusPotenLevel = (): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=cus_poten_level`,
    method: 'get'
  });
};
// -------------
export const drugstoreUpdateApi = (data: any): AxiosPromise<any> => {
  return request({
    url: `/mdm/gs-institution/drugstore/update`,
    method: 'post',
    data
  });
};
export const dsLabelApi = (data: any): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=ds_label`,
    method: 'get',
    data
  });
};

export const supplyModeApi = (data: any): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=supply_mode`,
    method: 'get',
    data
  });
};
