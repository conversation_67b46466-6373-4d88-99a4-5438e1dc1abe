<template>
  <div class="detailInformation">
    <div class="card-block">
      <el-card shadow="hover" class="card-block-content">
        <div class="card-block-top">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="机构ID" prop="">
                <div>{{ state.info.wbId || '-' }}</div>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="倍通编码" prop="">
                <div>{{ state.info.flowCode || '-' }}</div>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="SAP编码" prop="">
                <div>{{ state.info.sapCode || '-' }}</div>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="U8编码" prop="">
                <div>{{ state.info.u8Code || '-' }}</div>
              </el-form-item>
            </el-col>
          </el-row>
          <div class="top-title">基本信息</div>
          <el-row>
            <el-form :label-position="'left'" :model="state.queryForms" class="query-form-ref" label-width="110px" style="width: 100%">

              <el-row :gutter="20">
                <!-- 第一行：机构名称、机构类别、经度 -->
                <el-col :span="8">
                  <el-form-item label="机构名称" prop="">
                    <div>{{ state.info.insName || '-' }}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="机构类别" prop="">
                    <div>{{ state.info.insType || '-' }}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <!-- <el-form-item label="经度" prop="">
                    <div>{{ state.info.longitude || '-' }}</div>
                  </el-form-item> -->
                </el-col>

                <!-- 第二行：机构商业名称、分类编码、校验后经度 -->
                <el-col :span="8">
                  <el-form-item label="机构商业名称" prop="">
                    <div>{{ state.info.businessName || '-' }}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="分类编码" prop="">
                    <div>{{ state.info.categoryCode || '-' }}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <!-- <el-form-item label="校验后经度" prop="">
                    <div>{{ state.info.calibrateLongitude || '-' }}</div>
                  </el-form-item> -->
                </el-col>

                <!-- 第三行：机构别称、分类名称、纬度 -->
                <el-col :span="8">
                  <el-form-item label="机构别称" prop="">
                    <div>{{ state.info.nameAlias || '-' }}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="分类名称" prop="">
                    <div>{{ state.info.categoryName || '-' }}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <!-- <el-form-item label="纬度" prop="">
                    <div>{{ state.info.latitude || '-' }}</div>
                  </el-form-item> -->
                </el-col>

                <!-- 第四行：经营省份、机构主数据编码、校验后纬度 -->
                <el-col :span="8">
                  <el-form-item label="经营省份" prop="">
                    <div>{{ state.info.provinceName || '-' }}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="机构主数据编码" prop="">
                    <div>{{ state.info.mdmCode || '-' }}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <!-- <el-form-item label="校验后纬度" prop="">
                    <div>{{ state.info.calibrateLatitude || '-' }}</div>
                  </el-form-item> -->
                </el-col>

                <!-- 第五行：经营城市、三方主数据编码、启用状态 -->
                <el-col :span="8">
                  <el-form-item label="经营城市" prop="">
                    <div>{{ state.info.cityName || '-' }}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="三方主数据编码" prop="">
                    <div>{{ state.info.btCode || '-' }}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="启用状态" prop="">
                    <div>{{ state.info.status === '1' ? '停用' : '启用' }}</div>
                  </el-form-item>
                </el-col>

                <!-- 第六行：经营区县、上级机构主数据编码、失效时间 -->
                <el-col :span="8">
                  <el-form-item label="经营区县" prop="">
                    <div>{{ state.info.districtName || '-' }}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="上级机构主数据编码" prop="" label-width="140">
                    <div>{{ state.info.superiorMdmCode || '-' }}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="失效时间" prop="">
                    <div>{{ state.info.expireTime || '-' }}</div>
                  </el-form-item>
                </el-col>

                <!-- 第七行：详细经营地址 -->
                <el-col :span="8">
                  <el-form-item label="详细经营地址" prop="">
                    <div>{{ state.info.address || '-' }}</div>
                  </el-form-item>
                </el-col>
                <!-- <el-col :span="8">
                  <el-form-item label="编码" prop="">
                    <div>{{ state.info.superiorMdmCode || '-' }}</div>
                  </el-form-item>
                </el-col> -->
              </el-row>
            </el-form>
          </el-row>
        </div>
      </el-card>
    </div>

    <!-- 机构属性 -->
    <div class="card-block">
      <el-card shadow="hover" class="card-block-content">
        <div class="card-block-top">
          <div class="top-title">机构属性</div>
          <el-row>
            <el-form :label-position="'left'" :model="state.queryForms" class="query-form-ref" label-width="130px" style="width: 100%">
              <!-- 药店类型时显示的字段 -->
              <el-row :gutter="20" v-if="isPharmacy">
                <!-- 第一行：药店区域类型、药店性质、经营范围 -->
                <el-col :span="8">
                  <el-form-item label="药店区域类型" prop="">
                    <div>{{ state.info?.drugstoreFeature?.dsAreaType || '-' }}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="药店性质" prop="">
                    <div>{{ state.info?.drugstoreFeature?.dsNature || '-' }}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="经营范围" prop="">
                    <div>{{ state.info?.drugstoreFeature?.businessScope || '-' }}</div>
                  </el-form-item>
                </el-col>

                <!-- 第二行：药店总部、药店支付类型、冷链存储销售条件 -->
                <el-col :span="8">
                  <el-form-item label="药店总部" prop="">
                    <div>{{ state.info?.drugstoreFeature?.headquarters || '-' }}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="药店支付类型" prop="">
                    <div>{{ state.info?.drugstoreFeature?.dsEconomic || '-' }}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="冷链存储销售条件" prop="">
                    <div>{{ state.info?.drugstoreFeature?.codeSales || '-' }}</div>
                  </el-form-item>
                </el-col>

                <!-- 第三行：药店数据采集方式、供货方式 -->
                <el-col :span="8">
                  <el-form-item label="药店数据采集方式" prop="">
                    <div>{{ state.info?.drugstoreFeature?.dsSource || '-' }}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="供货方式" prop="">
                    <div>{{ state.info?.drugstoreFeature?.supplyMode || '-' }}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <!-- 空列 -->
                </el-col>
              </el-row>

              <!-- 非药店类型时显示的字段 -->
              <el-row :gutter="20" v-else>
                <!-- 第一行：机构级别、机构等次、经济类型 -->
                <el-col :span="8">
                  <el-form-item label="机构级别" prop="">
                    <div>{{ state.info.insGrade || '-' }}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="机构等次" prop="">
                    <div>{{ state.info.insLevel || '-' }}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="经济类型" prop="">
                    <div>{{ state.info.economicType || '-' }}</div>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-row>
        </div>
      </el-card>
    </div>

    <!-- 注册信息 - 非药店时显示 -->
    <div class="card-block" v-if="!isPharmacy">
      <el-card shadow="hover" class="card-block-content">
        <div class="card-block-top">
          <div class="top-title">注册信息</div>
          <el-row>
            <el-form :label-position="'left'" :model="state.queryForms" class="query-form-ref" label-width="130px" style="width: 100%">
              <el-row :gutter="20">
                <!-- 第一行：统一社会信用代码、注册省份 -->
                <el-col :span="8">
                  <el-form-item label="统一社会信用代码" prop="">
                    <div>{{ state.info.socialCreditCode || '-' }}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="注册省份" prop="">
                    <div>{{ state.info.registeredProvince || '-' }}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <!-- 空列 -->
                </el-col>

                <!-- 第二行：经营许可证名称、注册城市 -->
                <el-col :span="8">
                  <el-form-item label="经营许可证名称" prop="">
                    <div>{{ state.info.businessPermitName || '-' }}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="注册城市" prop="">
                    <div>{{ state.info.registeredCity || '-' }}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <!-- 空列 -->
                </el-col>

                <!-- 第三行：营业执照名称、注册区县 -->
                <el-col :span="8">
                  <el-form-item label="营业执照名称" prop="">
                    <div>{{ state.info.businessLicenseName || '-' }}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="注册区县" prop="">
                    <div>{{ state.info.registeredDistrict || '-' }}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <!-- 空列 -->
                </el-col>

                <!-- 第四行：营业期限、详细注册地址 -->
                <el-col :span="8">
                  <el-form-item label="营业期限" prop="">
                    <div>{{ state.info.businessTerm || '-' }}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="详细注册地址" prop="">
                    <div>{{ state.info.registeredAddress || '-' }}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <!-- 空列 -->
                </el-col>

                <!-- 第五行：法人、完整注册地址 -->
                <el-col :span="8">
                  <el-form-item label="法人" prop="">
                    <div>{{ state.info.legalPerson || '-' }}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="16">
                  <el-form-item label="完整注册地址" prop="">
                    <div>{{ state.info.fullRegisteredAddress || '-' }}</div>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-row>
        </div>
      </el-card>
    </div>

    <!-- 经营范围 - 药店时显示 -->
    <div class="card-block" v-if="isPharmacy">
      <el-card shadow="hover" class="card-block-content">
        <div class="card-block-top">
          <div class="top-title">经营范围</div>
          <el-table :data="state.businessScopeList" border style="width: 100%" :header-cell-style="{ background: '#f5f7fa', color: '#606266' }">
            <el-table-column prop="businessScope" label="经营范围" min-width="200">
              <template #default="scope">
                <div>{{ scope.row.businessScope || '-' }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="productSpecCode" label="产品规格编码" min-width="150">
              <template #default="scope">
                <div>{{ scope.row.productSpecCode || '-' }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="启用状态" min-width="100">
              <template #default="scope">
                <div>{{ scope.row.status === '1' ? '停用' : '启用' }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="expireTime" label="失效时间" min-width="150">
              <template #default="scope">
                <div>{{ scope.row.expireTime || '-' }}</div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>
  </div>
</template>
<script setup>
import { reactive, computed } from 'vue'

const state = reactive({
  queryForms: {},
  info: {},
  businessScopeList: [] // 经营范围表格数据
});

// 判断是否为药店
const isPharmacy = computed(() => {
  return state.info.insType === '药店'
});

const setInfo = (info) => {
  state.info = JSON.parse(JSON.stringify(info));
  state.queryForms = JSON.parse(JSON.stringify(info));

  // 设置经营范围表格数据
  if (info.businessScopeList && Array.isArray(info.businessScopeList)) {
    state.businessScopeList = info.businessScopeList;
  } else {
    // 如果没有传入表格数据，可以设置默认数据或空数组
    state.businessScopeList = [];
  }
};

const editStatus = () => {
  // 编辑状态处理
};

const handelCancel = () => {
  // 取消编辑处理
  state.queryForms = JSON.parse(JSON.stringify(state.info));
};

const resetFields = () => {
  // 重置字段处理
  state.queryForms = {};
};

defineExpose({
  setInfo,
  editStatus,
  handelCancel,
  resetFields
});
</script>
<style lang="scss" scoped>
:deep(.el-form-item__label) {
  font-family: PingFang SC, PingFang SC;
  color: #86909c !important;
  font-weight: 400;
}

:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-form-item__content) {
  div {
    color: #1d212b;
    font-weight: 400;
  }
}

.detailInformation {
  .card-block {
    // margin-bottom: 20px;
    padding: 0;
  }

  .top-title {
    color: #1d212b;
    font-weight: 600;
    margin-bottom: 16px;
    font-size: 16px;
  }

  .card-block-content {
    padding: 16px 20px;
  }
}
</style>
