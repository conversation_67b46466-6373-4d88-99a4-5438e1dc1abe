<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="initial-scale=1.0, user-scalable=no, width=device-width" />
    <title>详情查询</title>
    <link rel="stylesheet" href="https://cache.amap.com/lbs/static/main1119.css" />
    <script type="text/javascript" src="https://cache.amap.com/lbs/static/addToolbar.js"></script>
  </head>
  <body>
    <div id="container"></div>
    <script type="text/javascript" src="https://webapi.amap.com/maps?v=2.0&key=您申请的key值&plugin=AMap.PlaceSearch"></script>
    <script type="text/javascript">
        var lnglat = new AMap.LngLat(116.397, 39.918);
        var map = new AMap.Map("container", {
            resizeEnable: true,
            center: lnglat,
        });
        var placeSearch = new AMap.PlaceSearch({
            city: 'beijing', // 兴趣点城市
            citylimit: true,  //是否强制限制在设置的城市内搜索
            pageSize: 10, // 单页显示结果条数
            children: 0, //不展示子节点数据
            pageIndex: 1, //页码
            extensions: 'base' //返回基本地址信息
        });
        //详情查询
        placeSearch.getDetails("B000A83U0P", function(status, result) {
            if (status === 'complete' && result.info === 'OK') {
                placeSearch_CallBack(result);
            }
        });
        var infoWindow = new AMap.InfoWindow({
            autoMove: true,
            offset: {x: 0, y: -30}
        });
        //回调函数
        function placeSearch_CallBack(data) {
            var poiArr = data.poiList.pois;
            //添加marker
            var marker = new AMap.Marker({
                map: map,
                position: poiArr[0].location
            });
            map.setCenter(marker.getPosition());
            infoWindow.setContent(createContent(poiArr[0]));
      infoWindow.open(map, marker.getPosition());
        }
        function createContent(poi) {  //信息窗体内容
            var s = [];
            s.push("<b>名称：" + poi.name+"</b>");
            s.push("地址：" + poi.address);
            s.push("电话：" + poi.tel);
            s.push("类型：" + poi.type);
            return s.join("<br>");
        }
    </script>
  </body>
</html>
