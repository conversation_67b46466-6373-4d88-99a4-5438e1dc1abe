/**
 * 标签数据处理测试
 * 用于验证BU归属标签的显示逻辑
 */

// 模拟接口返回的标签数据
const mockTagData = [
  {
    "bu": "集团",
    "tagType": "bu标签",
    "tagValue": ["广阔"]
  },
  {
    "bu": "华北BU",
    "tagType": "区域标签",
    "tagValue": ["核心", "重点"]
  },
  {
    "bu": "华东BU", 
    "tagType": "业务标签",
    "tagValue": ["优质机构"]
  }
];

// 模拟机构数据
const mockInstitutionData = {
  wbId: "12345",
  insName: "北京协和医院",
  insType: "综合医院",
  provinceName: "北京",
  cityName: "北京",
  districtName: "东城区",
  institutionTagVos: mockTagData
};

/**
 * 获取机构标签显示文本 - 与实际代码保持一致
 */
function getInstitutionTags(institutionTagVos) {
  if (!Array.isArray(institutionTagVos)) {
    return []
  }
  
  const tags = []
  institutionTagVos.forEach(tagVo => {
    // 根据实际接口数据结构调整
    if (tagVo.tagValue && Array.isArray(tagVo.tagValue)) {
      // 显示格式：BU名称-标签值
      tagVo.tagValue.forEach(value => {
        tags.push(`${tagVo.bu || tagVo.tagType}-${value}`)
      })
    }
  })
  
  return tags
}

/**
 * 测试标签处理函数
 */
function testTagProcessing() {
  console.log('🧪 测试标签数据处理...\n');
  
  console.log('📋 原始标签数据:');
  console.log(JSON.stringify(mockTagData, null, 2));
  
  console.log('\n🏷️  处理后的标签显示:');
  const processedTags = getInstitutionTags(mockTagData);
  processedTags.forEach((tag, index) => {
    console.log(`${index + 1}. ${tag}`);
  });
  
  console.log('\n📊 完整机构数据示例:');
  console.log(`机构名称: ${mockInstitutionData.insName}`);
  console.log(`机构类型: ${mockInstitutionData.insType}`);
  console.log(`地址: ${mockInstitutionData.provinceName}${mockInstitutionData.cityName}${mockInstitutionData.districtName}`);
  console.log(`BU归属标签: ${processedTags.join(', ')}`);
  
  return processedTags;
}

/**
 * 测试边界情况
 */
function testEdgeCases() {
  console.log('\n🔍 测试边界情况...\n');
  
  // 测试空数组
  console.log('1. 空数组测试:');
  const emptyResult = getInstitutionTags([]);
  console.log(`结果: [${emptyResult.join(', ')}] (长度: ${emptyResult.length})`);
  
  // 测试null/undefined
  console.log('\n2. null/undefined测试:');
  const nullResult = getInstitutionTags(null);
  const undefinedResult = getInstitutionTags(undefined);
  console.log(`null结果: [${nullResult.join(', ')}] (长度: ${nullResult.length})`);
  console.log(`undefined结果: [${undefinedResult.join(', ')}] (长度: ${undefinedResult.length})`);
  
  // 测试无效数据结构
  console.log('\n3. 无效数据结构测试:');
  const invalidData = [
    { bu: "测试BU", tagType: "测试类型" }, // 缺少tagValue
    { bu: "测试BU2", tagValue: "不是数组" }, // tagValue不是数组
    { tagValue: ["孤立标签"] } // 缺少bu和tagType
  ];
  const invalidResult = getInstitutionTags(invalidData);
  console.log(`结果: [${invalidResult.join(', ')}] (长度: ${invalidResult.length})`);
}

/**
 * 生成HTML表格显示
 */
function generateHTMLTable() {
  const tags = getInstitutionTags(mockTagData);
  
  let html = `
<table border="1" style="border-collapse: collapse; width: 100%; margin: 20px 0;">
  <thead>
    <tr style="background-color: #f5f5f5;">
      <th style="padding: 10px;">字段</th>
      <th style="padding: 10px;">值</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td style="padding: 10px; font-weight: bold;">机构ID</td>
      <td style="padding: 10px;">${mockInstitutionData.wbId}</td>
    </tr>
    <tr>
      <td style="padding: 10px; font-weight: bold;">机构名称</td>
      <td style="padding: 10px;">${mockInstitutionData.insName}</td>
    </tr>
    <tr>
      <td style="padding: 10px; font-weight: bold;">机构类型</td>
      <td style="padding: 10px;">${mockInstitutionData.insType}</td>
    </tr>
    <tr>
      <td style="padding: 10px; font-weight: bold;">省份</td>
      <td style="padding: 10px;">${mockInstitutionData.provinceName}</td>
    </tr>
    <tr>
      <td style="padding: 10px; font-weight: bold;">城市</td>
      <td style="padding: 10px;">${mockInstitutionData.cityName}</td>
    </tr>
    <tr>
      <td style="padding: 10px; font-weight: bold;">区县</td>
      <td style="padding: 10px;">${mockInstitutionData.districtName}</td>
    </tr>
    <tr>
      <td style="padding: 10px; font-weight: bold;">BU归属</td>
      <td style="padding: 10px;">
        ${tags.map(tag => `<span style="background: #e3f2fd; padding: 2px 8px; margin: 2px; border-radius: 4px; display: inline-block;">${tag}</span>`).join('')}
      </td>
    </tr>
  </tbody>
</table>`;
  
  return html;
}

// 导出函数
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    getInstitutionTags,
    testTagProcessing,
    testEdgeCases,
    generateHTMLTable,
    mockTagData,
    mockInstitutionData
  };
}

// 浏览器环境
if (typeof window !== 'undefined') {
  window.tagTest = {
    getInstitutionTags,
    testTagProcessing,
    testEdgeCases,
    generateHTMLTable,
    mockTagData,
    mockInstitutionData
  };
  
  console.log('🔧 标签处理测试工具已加载');
  console.log('使用方法: window.tagTest.testTagProcessing()');
}
