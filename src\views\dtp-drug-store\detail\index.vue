<template>
  <div class="p-2">
    <!-- 基本信息 -->
    <div class="card-block mb-6">
      <div class=" flex justify-between">
        <h2 class="text-xl font-bold mb-4">基本信息</h2>
        <el-button @click="goBack">返回</el-button>
      </div>
      <el-form :model="drugstoreInfo" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="药店名称">
              <span>{{ drugstoreInfo.drugName || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="药店编码">
              <span>{{ drugstoreInfo.drugCode || '--' }}</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="省份">
              <span>{{ drugstoreInfo.drugProvince || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="城市">
              <span>{{ drugstoreInfo.drugCity || '--' }}</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="地址">
              <span>{{ drugstoreInfo.address || '--' }}</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="照片（营业执照、经营许可证、门头照）" label-position="top">
              <div v-if="drugstoreInfo.fileList && drugstoreInfo.fileList.length > 0" class="image-gallery">
                <el-image
                  v-for="(file, index) in drugstoreInfo.fileList"
                  :key="index"
                  :src="file.url"
                  fit="cover"
                  class="mr-2 h-24 w-24 object-cover"
                  :preview-src-list="drugstoreInfo.fileList.map(f => f.url)"
                  :initial-index="index"
                />
              </div>
              <span v-else>--</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- 产品信息 -->
    <div class="card-block">
      <h2 class="text-xl font-bold mb-4">产品信息</h2>

      <!-- 搜索筛选区域 -->
      <el-form :model="queryParams" inline class="mb-4">
        <!-- <el-form-item label="申请人所属BU">
          <el-select v-model="queryParams.bu" placeholder="请选择BU" clearable filterable>
            <el-option v-for="item in buOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item> -->

        <el-form-item label="申请人">
          <el-input v-model="queryParams.applicant" placeholder="请输入申请人姓名" clearable />
        </el-form-item>

        <el-form-item label="申请人HRCode">
          <el-input v-model="queryParams.applicantCode" placeholder="请输入申请人HRCode" clearable />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 按钮操作区 -->
      <div class="mb-4 flex justify-between">
        <div>
          <el-button type="primary" plain @click="openAddProductDialog">新增产品</el-button>
          <el-button type="danger" plain @click="handleBatchDelete">删除产品</el-button>
        </div>
        <div>
          <el-button type="success" :loading="submitting" :disabled="!hasChanges" @click="submitAllChanges">提交审批</el-button>
        </div>
      </div>

      <!-- 产品列表 -->
      <el-table v-loading="loading" :data="productList" style="width: 100%" border @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="productCode" label="产品编码" min-width="120" />
        <el-table-column prop="productName" label="产品" min-width="180" />
        <el-table-column prop="specCode" label="品规编码" min-width="120" />
        <el-table-column prop="specName" label="品规名称" min-width="180" />
        <el-table-column prop="applicant" label="申请人" min-width="120" />
        <el-table-column prop="applicantCode" label="申请人HRCode" min-width="120" />
        <el-table-column prop="applicantDept" label="申请人所在部门" min-width="180" />
        <el-table-column prop="bu" label="申请人所属BU" min-width="120" />
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button type="danger" link @click="handleDelete(row)">删除产品</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="flex justify-end mt-4">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 新增产品弹窗 -->
    <el-dialog title="选择产品" v-model="productDialogVisible" width="600px">
      <el-form :model="addProductForm" label-width="80px">
        <el-form-item label="产品" required>
          <el-select v-model="addProductForm.productCode" placeholder="请选择产品" class="w-full" filterable @change="handleProductChange">
            <el-option v-for="item in availableProducts" :key="item.productCode" :label="item.productName" :value="item.productCode" />
          </el-select>
        </el-form-item>

        <el-form-item label="品规" required>
          <el-select
            v-model="addProductForm.specCodes"
            placeholder="请选择品规"
            multiple
            class="w-full"
            filterable
            :disabled="!addProductForm.productCode"
          >
            <el-option v-for="item in availableSpecs" :key="item.code" :label="item.name" :value="item.code" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="productDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmAddProduct" :loading="submitting">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getDtpDrugstoreDetail, getDrugstoreProducts, getJurisdictionProducts, getSpecsByProductApi, deleteProductApplication, addProductApplication } from '@/api/dtp-drug-store';
import { getFileInfo } from '@/api/flow';
import { useUserStore } from '@/store/modules/user';
import type { ProductSpec, DtpJurDrugProductVo, DtpJurProductVo, DtpJurDrugVo, AeTaskAttachment } from '@/api/dtp-drug-store/types';

// 路由和用户信息
const route = useRoute();
const router = useRouter();
const userStore = useUserStore();
const user = userStore.info || {};

// 状态定义
const loading = ref(false);
const loadingSpecs = ref(false);
const submitting = ref(false);
const total = ref(0);
const productDialogVisible = ref(false);
const drugstoreInfo = ref<DtpJurDrugVo>({} as DtpJurDrugVo);
const productList = ref<DtpJurDrugProductVo[]>([]);
const selectedProducts = ref<DtpJurDrugProductVo[]>([]);

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  productName: '',
  specNames: [] as string[],
  bu: '',
  applicant: '',
  applicantCode: ''
});

// 新增产品表单
const addProductForm = reactive({
  productCode: '',
  productName: '',
  specCodes: [] as string[]
});

// 选项列表
const productOptions = ref<{label: string; value: string}[]>([]);
const specOptions = ref<{label: string; value: string}[]>([]);
const buOptions = ref<{label: string; value: string}[]>([
  { label: 'DTP', value: 'DTP' },
  { label: 'OTC', value: 'OTC' },
  { label: 'RX', value: 'RX' }
]);

// 可选产品和品规
const availableProducts = ref<DtpJurProductVo[]>([]);
const availableSpecs = ref<{code: string; name: string}[]>([]);

// 计算当前药店ID
const drugstoreId = computed(() => {
  const id = route.params.id || route.query.id;
  return Array.isArray(id) ? id[0] : id as string;
});

// 记录修改前的原始产品列表
const originalProductList = ref<DtpJurDrugProductVo[]>([]);

// 判断是否有变更
const hasChanges = computed(() => {
  if (originalProductList.value.length !== productList.value.length) {
    return true;
  }

  // 检查是否有任何产品与原始列表不同
  const productMap = new Map();
  originalProductList.value.forEach(p => {
    const key = `${p.productCode}-${p.specCode}`;
    productMap.set(key, true);
  });

  return productList.value.some(p => {
    const key = `${p.productCode}-${p.specCode}`;
    return !productMap.has(key);
  });
});

// 加载药店详情
const loadDrugstoreDetail = async () => {
  if (!drugstoreId.value) return;

  loading.value = true;
  try {
    const res = await getDtpDrugstoreDetail(drugstoreId.value);
    if (res.code === 200 && res.data) {
      drugstoreInfo.value = res.data;

      // 处理文件列表
      if (drugstoreInfo.value.fileList) {
        await handleFileList();
      }
    }
  } catch (error) {
    console.error('获取药店详情失败', error);
    ElMessage.error('获取药店详情失败');
  } finally {
    loading.value = false;
  }
};

// 处理文件列表数据
const handleFileList = async () => {
  try {
    if (!drugstoreInfo.value || !drugstoreInfo.value.fileList) {
      if (drugstoreInfo.value) {
        drugstoreInfo.value.fileList = [];
      }
      return;
    }

    // 如果是字符串，尝试解析JSON
    if (typeof drugstoreInfo.value.fileList === 'string') {
      try {
        drugstoreInfo.value.fileList = JSON.parse(drugstoreInfo.value.fileList) as AeTaskAttachment[];
      } catch (e) {
        console.error('解析文件列表JSON失败:', e);
        drugstoreInfo.value.fileList = [];
        return;
      }
    }

    // 获取文件详细信息
    const filePromises = (drugstoreInfo.value.fileList as AeTaskAttachment[]).map(async (file) => {
      if (file.ossId) {
        try {
          const res = await getFileInfo(file.ossId);
          if (res?.data?.rows && res.data.rows[0]) {
            const fileInfo = res.data.rows[0];
            return {
              id: file.id || '',
              name: fileInfo.fileName || file.name,
              url: fileInfo.url || file.url,
              ossId: file.ossId,
              aeFileId: file.aeFileId || '',
              size: file.size || '',
              mime_type: file.mime_type || ''
            } as AeTaskAttachment;
          }
        } catch (error) {
          console.error('获取文件信息失败:', error);
        }
      }
      return file; // 如果获取失败，返回原始文件对象
    });

    // 等待所有文件信息获取完成
    const processedFiles = await Promise.all(filePromises);
    drugstoreInfo.value.fileList = processedFiles as AeTaskAttachment[];
  } catch (error) {
    console.error('处理文件列表失败:', error);
    ElMessage.error('处理文件列表失败');
  }
};

// 加载产品列表
const loadProductList = async () => {
  if (!drugstoreId.value) return;

  loading.value = true;
  try {
    const params: any = {
      jurDsCode: drugstoreId.value,
      pageNum: queryParams.pageNum,
      pageSize: queryParams.pageSize,
      productName: queryParams.productName,
      specNames: queryParams.specNames,
      bu: queryParams.bu,
      applicant: queryParams.applicantCode
    };

    const res = await getDrugstoreProducts(params);
    if (res.code === 200 && res.data) {
      productList.value = res.data;
      // 保存原始数据用于对比
      originalProductList.value = JSON.parse(JSON.stringify(res.data));
      total.value = res.data.length;
    }
  } catch (error) {
    console.error('获取产品列表失败', error);
    ElMessage.error('获取产品列表失败');
  } finally {
    loading.value = false;
  }
};

// 加载可选产品
const loadAvailableProducts = async () => {
  try {
    loading.value = true;
    const res = await getJurisdictionProducts();
    if (res.code === 200 && res.data) {
      availableProducts.value = res.data;

      // 构建产品选项
      productOptions.value = res.data.map(item => ({
        label: item.productName,
        value: item.productCode
      }));
    }
  } catch (error) {
    console.error('获取可选产品失败', error);
    ElMessage.error('获取可选产品失败');
  } finally {
    loading.value = false;
  }
};

// 加载产品的品规
const loadProductSpecs = async (productCode: string) => {
  if (!productCode) return;

  loadingSpecs.value = true;
  try {
    const res = await getSpecsByProductApi({ level3Code: productCode });
    if (res.code === 200 && res.data) {
      // 转换API返回的数据为所需格式
      const specs = res.data?.rows?.map((item: any) => ({
        code: item.specCode,
        name: item.specName
      })) || [];

      availableSpecs.value = specs;

      // 构建品规选项
      specOptions.value = specOptions.value.concat(
        specs.map((item: any) => ({
          label: item.name,
          value: item.code
        })).filter((spec: any) => !specOptions.value.some(existingSpec => existingSpec.value === spec.value))
      );
    }
  } catch (error) {
    console.error('获取产品品规失败', error);
    ElMessage.error('获取产品品规失败');
  } finally {
    loadingSpecs.value = false;
  }
};

// 搜索处理
const handleSearch = () => {
  queryParams.pageNum = 1;
  loadProductList();
};

// 重置查询
const resetQuery = () => {
  queryParams.productName = '';
  queryParams.specNames = [];
  queryParams.bu = '';
  queryParams.applicant = '';
  queryParams.applicantCode = '';
  queryParams.pageNum = 1;
  loadProductList();
};

// 选择变更处理
const handleSelectionChange = (selection: DtpJurDrugProductVo[]) => {
  selectedProducts.value = selection;
};

// 处理分页大小变化
const handleSizeChange = (size: number) => {
  queryParams.pageSize = size;
  queryParams.pageNum = 1;
  loadProductList();
};

// 处理页码变化
const handleCurrentChange = (page: number) => {
  queryParams.pageNum = page;
  loadProductList();
};

// 返回上一页
const goBack = () => {
  router.go(-1);
};

// 打开产品添加对话框
const openAddProductDialog = () => {
  addProductForm.productCode = '';
  addProductForm.productName = '';
  addProductForm.specCodes = [];
  availableSpecs.value = [];
  productDialogVisible.value = true;
};

// 产品选择变化处理
const handleProductChange = async (productCode: string) => {
  addProductForm.specCodes = [];
  const selectedProduct = availableProducts.value.find(p => p.productCode === productCode);
  if (selectedProduct) {
    addProductForm.productName = selectedProduct.productName;
  }
  await loadProductSpecs(productCode);
};

// 确认添加产品
const confirmAddProduct = async () => {
  if (!addProductForm.productCode) {
    ElMessage.warning('请选择产品');
    return;
  }

  if (addProductForm.specCodes.length === 0) {
    ElMessage.warning('请至少选择一个品规');
    return;
  }

  submitting.value = true;
  try {
    // 构建新增产品的列表
    const newProducts: DtpJurDrugProductVo[] = [];

    for (const specCode of addProductForm.specCodes) {
      const spec = availableSpecs.value.find(s => s.code === specCode);
      if (spec) {
        const selectedProduct = availableProducts.value.find(p => p.productCode === addProductForm.productCode);

        // 检查是否已经存在此产品规格
        const existingProduct = productList.value.find(
          p => p.productCode === addProductForm.productCode && p.specCode === specCode
        );

        if (existingProduct) {
          ElMessage.warning(`产品"${selectedProduct?.productName} - ${spec.name}"已存在`);
          continue;
        }

        // 创建新产品对象
        const newProduct: DtpJurDrugProductVo = {
          jurDsCode: drugstoreInfo.value.jurDsCode,
          jurDsSpecCode: '',
          productCode: addProductForm.productCode,
          productName: selectedProduct?.productName || '',
          specCode: spec.code,
          specName: spec.name,
          applicant: user.nickName,
          applicantCode: user.userName || '',
          applicantDept: user.dept?.deptName || '',
          bu: 'DTP',
          hrCode: user.userName || '',
          inDept: user.dept?.deptName || '',
          ancestors: user.dept?.ancestors || '',
          deptName: user.dept?.deptName || ''
        };

        newProducts.push(newProduct);

        // 将新产品添加到显示列表
        productList.value.push(newProduct);
      }
    }

    // 关闭对话框
    productDialogVisible.value = false;

    if (newProducts.length > 0) {
      ElMessage.success(`成功添加${newProducts.length}个产品`);
    }
  } catch (error) {
    console.error('添加产品失败', error);
    ElMessage.error('添加产品失败');
  } finally {
    submitting.value = false;
  }
};

// 批量删除产品
const handleBatchDelete = () => {
  if (selectedProducts.value.length === 0) {
    ElMessage.warning('请至少选择一条数据删除');
    return;
  }

  ElMessageBox.confirm(
    `确定要删除选中的${selectedProducts.value.length}个产品吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 执行删除操作
    removeProducts(selectedProducts.value);
  }).catch(() => {
    // 用户取消删除操作
  });
};

// 删除单个产品
const handleDelete = (row: DtpJurDrugProductVo) => {
  ElMessageBox.confirm(
    `确定要删除产品"${row.productName} - ${row.specName}"吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 执行删除操作
    removeProducts([row]);
  }).catch(() => {
    // 用户取消删除操作
  });
};

// 执行产品删除操作
const removeProducts = (products: DtpJurDrugProductVo[]) => {
  for (const product of products) {
    // 从显示列表中移除
    const index = productList.value.findIndex(
      p => p.productCode === product.productCode && p.specCode === product.specCode
    );

    if (index !== -1) {
      productList.value.splice(index, 1);
    }
  }

  ElMessage.success(`成功删除${products.length}个产品`);
};

// 统一提交所有变更
const submitAllChanges = async () => {
  if (!hasChanges.value) {
    ElMessage.warning('没有需要提交的变更');
    return;
  }

  submitting.value = true;

  try {
    // 计算删除的产品和新增的产品
    const addedProducts: DtpJurDrugProductVo[] = [];
    const deletedProducts: DtpJurDrugProductVo[] = [];

    // 找出被删除的产品
    originalProductList.value.forEach(originalProduct => {
      const exists = productList.value.some(
        p => p.productCode === originalProduct.productCode && p.specCode === originalProduct.specCode
      );
      if (!exists) {
        deletedProducts.push(originalProduct);
      }
    });

    // 找出新增的产品
    productList.value.forEach(currentProduct => {
      const exists = originalProductList.value.some(
        p => p.productCode === currentProduct.productCode && p.specCode === currentProduct.specCode
      );
      if (!exists) {
        addedProducts.push(currentProduct);
      }
    });

    // 如果有产品变动，提交申请
    if (addedProducts.length > 0 || deletedProducts.length > 0) {
      const submitData = {
        applyContent: {
          dsCode: drugstoreInfo.value.drugCode,
          dsName: drugstoreInfo.value.drugName,
          dsMdmCode: drugstoreInfo.value.drugMdmCode,
          province: drugstoreInfo.value.drugProvince,
          city: drugstoreInfo.value.drugCity,
          district: drugstoreInfo.value.drugDistrict || '',
          districtCode: '',
          provinceCode: '',
          cityCode: '',
          jurCode: drugstoreInfo.value.jurCode,
          jurType: '',
          bu: 'DTP',
          address: '',
          specList: productList.value.map(p => ({
            mdmCode: '',
            productCode: p.productCode,
            productName: p.productName,
            specCode: p.specCode,
            specName: p.specName
          }))
        },
        applyType: 'dtpDsProductInsert',
        dsCode: drugstoreInfo.value.drugCode,
        dsName: drugstoreInfo.value.drugName,
        jurCode: drugstoreInfo.value.jurCode,
        bu: 'DTP',
        dsMdmCode: drugstoreInfo.value.drugMdmCode,
        // 用户信息
        applicant: user?.nickName,
        applicantCode: user?.userName,
        postCode: user?.posts?.postCode,
        postName: user?.posts?.postName,
        deptCode: user?.dept?.deptCode,
        deptName: user?.dept?.deptName,
        ancestors: user?.dept?.ancestors,
        tenantId: import.meta.env.VITE_APP_TENANT_ID,
        enableWorkflow: true,
        appCode: import.meta.env.VITE_APP_CODE,
        postIdList: user?.postIdList?.map((item: any) => item.postId)
      };

      // 调用API提交变更
      const res = await addProductApplication(submitData);
      if (res.code !== 200) {
        throw new Error(res.msg || '提交产品变更申请失败');
      }

      if(res.code === 200 && res.msg) {
        ElMessage.error(res.msg);
        return;
      }

      ElMessage.success('产品变更申请提交成功');

      // 将当前列表设为原始列表
      originalProductList.value = JSON.parse(JSON.stringify(productList.value));
    }
  } catch (error) {
    console.error('提交变更失败', error);
    ElMessage.error(typeof error === 'object' && error !== null ? (error as Error).message : '提交变更失败');
  } finally {
    submitting.value = false;
  }
};

// 初始化
onMounted(() => {
  loadDrugstoreDetail();
  loadProductList();
  loadAvailableProducts();
});
</script>

<style lang="scss" scoped>
.card-block {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.image-gallery {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;

  .el-image {
    border-radius: 4px;
    border: 1px solid #ebeef5;
  }
}
</style>
