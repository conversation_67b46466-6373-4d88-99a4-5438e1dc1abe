<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { PictureFilled } from '@element-plus/icons-vue'
import { getFileInfo } from '@/api/flow'

const props = defineProps({
  formData: {
    type: Object,
    default: () => ({
      drugstoreName: '示例药店',
      drugstoreCode: 'DS12345',
      province: '广东省',
      city: '深圳市',
      address: '南山区科技园路100号',
      files: [
        { name: '营业执照.jpg', url: '' },
        { name: '经营许可证.jpg', url: '' },
        { name: '门头照.jpg', url: '' }
      ]
    })
  },
  productList: {
    type: Array,
    default: () => [
      {
        productCode: '0JSM',
        productName: '金斯明',
        specCode: '0JSMYUTREE',
        specName: '金斯明品规1'
      },
      {
        productCode: '0SBJ',
        productName: '赛必健',
        specCode: '0SBJJJJHGFFDS',
        specName: '赛必健品规1'
      }
    ]
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const router = useRouter()

// 返回上一页
const handleReturn = () => {
  router.back()
}
</script>

<template>
  <div class="">
    <div class="">
      <!-- 加载状态 -->
      <el-skeleton v-if="props.loading" :rows="10" animated />

      <template v-else>
        <!-- 药店信息 -->
        <div>
          <h2 class="text-xl font-bold mb-4">药店信息</h2>
          <el-form label-width="120px">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="药店名称">
                  <span>{{ props.formData.dsName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="药店编码">
                  <span>{{ props.formData.dsCode }}</span>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="省份">
                  <span>{{ props.formData.province }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="城市">
                  <span>{{ props.formData.city }}</span>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="地址">
                  <span>{{ props.formData.address }}</span>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="照片">
                  <div class="flex flex-wrap gap-4">
                    <div v-for="(file, index) in props.formData.attUrlList" :key="index" class="w-40">
                      <el-image class="w-full h-32 object-cover border rounded" :src="file.url" fit="cover" :preview-src-list="[file.url]">
                        <template #error>
                          <div class="flex items-center justify-center h-full bg-gray-100">
                            <el-icon><picture-filled /></el-icon>
                          </div>
                        </template>
                      </el-image>
                      <div class="text-center text-sm mt-1 text-gray-700">{{ file.name }}</div>
                    </div>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>

        <!-- 产品信息 -->
        <div>
          <h2 class="text-xl font-bold mb-4">产品信息</h2>

          <el-table :data="props.formData?.specList || []" border style="width: 100%">
            <el-table-column prop="productCode" label="产品编码" width="150" />
            <el-table-column prop="productName" label="产品" width="180" />
            <el-table-column prop="specCode" label="品规编码" width="180" />
            <el-table-column prop="specName" label="品规名称" />
          </el-table>
        </div>

        <!-- 操作按钮 -->
        <div class="flex justify-center mt-8 space-x-4"></div>
      </template>
    </div>
  </div>
</template>

<style scoped>
.el-table {
  margin-bottom: 20px;
}

.card-block {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}
</style>
