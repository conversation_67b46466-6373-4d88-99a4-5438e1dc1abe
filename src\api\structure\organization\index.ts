import request from '@/utils/request';
import { AxiosPromise } from 'axios';
// 获取部门下权限范围
export const getDepListApi = (query: any, version: any): AxiosPromise<any> => {
  return request({
    url: `/hr/user-dept${version ? '-version' : ''}/list-by-dept`,
    method: 'get',
    params: query
  });
};

// 获取岗位下权限范围
export const getPostListApi = (query: any, version: any): AxiosPromise<any> => {
  return request({
    url: `/hr/user-dept${version ? '-version' : ''}/list-by-post`,
    method: 'get',
    params: query
  });
};

// 新增岗位下权限范围
export const addUserDeptApi = (data: any, version: any) => {
  return request({
    url: `/hr/user-dept${version ? '-version' : ''}/insert`,
    method: 'post',
    data: data
  });
};

// 转岗
export const updateUserDeptApi = (data: any, version: any) => {
  return request({
    url: `/hr/user-dept${version ? '-version' : ''}/update`,
    method: 'post',
    data: data
  });
};

// 删除权限范围
export const delUserDeptApi = (query: any, version: any) => {
  return request({
    url: `/hr/user-dept${version ? '-version' : ''}`,
    method: 'delete',
    params: query
  });
};

// 获取部门列表 /hr/dept/list
export const getDeptListApi = (query: any, version: any) => {
  return request({
    url: `/hr/dept${version ? '-version' : ''}/list`,
    method: 'get',
    params: query
  });
};

// 枚举
export const getEnumerateApi = (query: any, url: string) => {
  return request({
    url,
    method: 'get',
    params: query
  });
};

// 获取直属领导
export const getLeaderApi = (id: any) => {
  return request({
    url: `/hr/user/get-leader/${id}`,
    method: 'get'
    // params: query
  });
};
// 获取操作记录
export const logListPage = (query: any) => {
  return request({
    url: '/hr/log/list-page',
    method: 'get',
    params: query
  });
};
export const setManager = (data: any) => {
  return request({
    url: '/hr/user-dept/set/manager',
    method: 'post',
    data: data
  });
};

// 根据姓名搜索姓名工号
export const getNameApi = (query: any, version: any) => {
  return request({
    url: `/hr/user${version ? '-version' : ''}/get-name`,
    method: 'get',
    params: query
  });
};

// 获取是否展示空岗添加按钮
export const getIsaddDeptUserApi = (query: any) => {
  return request({
    url: `/hr/dept/is_add_dept_user`,
    method: 'get',
    params: query
  });
};
// 获取是否展示空岗添加按钮
export const deptManager = (id: any) => {
  return request({
    url: `/hr/dept/manager/${id}`,
    method: 'get'
  });
};

// 获取部门详情
export const getDeptDetailApi = (query: any, version: any) => {
  return request({
    url: `/hr/dept${version ? '-version' : ''}/detail`,
    method: 'get',
    params: query
  });
};

// 获取岗位标签
export const getPsotTagsApi = (postId: any) => {
  return request({
    url: `/hr/post/tag/${postId}`,
    method: 'get'
  });
};

// 修改岗位科室标签
export const updateTagApi = (data: any) => {
  return request({
    url: '/hr/post/update-tag',
    method: 'post',
    data: data
  });
};

//导入接口
export const importApi = (data: any) => {
  return request({
    url: `/hr/post/upload-tag`,
    method: 'post',
    data: data
  });
};

export const userDeptHandle = (data: any, version: any) => {
  return request({
    url: `/hr/user-dept${version ? '-version' : ''}/handle`,
    method: 'post',
    data: data
  });
};
