<template>
  <div class="effective-tags">
    <h3 class="title">生效标签</h3>
    <div v-for="(group, groupIdx) in tagsData" :key="groupIdx" class="tag-group">
      <div class="tag-group-container">
        <div class="group-title">{{ group.bu }}</div>
        <div class="sub-groups">
          <div class="sub-group">
            <div class="sub-title">{{ group.tagType }}</div>
            <div class="tag-list">
              <span v-for="(tag, tagIdx) in group.tagValue" :key="tagIdx" class="tag-card">
                {{ tag }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { useHcoDetail } from '../composables/useHco';

const route = useRoute();
const hcoDetail = useHcoDetail();
const tagsData = ref([]);

// 获取标签数据方法
const getTagsData = async () => {
  try {
    const insCode = route.query.insCode || route.query.insDsSpecCode;
    if (insCode) {
      await hcoDetail.fetchInsTags(insCode);

      // 将API返回的标签数据转换为组件需要的格式
      // 这里需要根据实际API返回的数据结构进行调整
      if (hcoDetail.tags.value && hcoDetail.tags.value.length > 0) {
        // 如果API返回的是简单的字符串数组，转换为组件需要的格式
        tagsData.value = hcoDetail.tags.value;
      } else {
        // 如果没有数据，显示默认的模拟数据
        tagsData.value = [];
      }
    }
  } catch (error) {
    console.error('获取标签数据失败:', error);
    // 出错时显示默认数据
    tagsData.value = [];
  }
};

onMounted(() => {
  getTagsData();
});
</script>

<style scoped lang="scss">
.effective-tags {
  .title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 16px;
  }
  .tag-group {
    margin-bottom: 20px;
    .tag-group-container {
      display: flex;
      align-items: flex-start;
      .group-title {
        font-size: 16px;
        font-weight: 500;
        min-width: 80px;
        margin-right: 12px;
        flex-shrink: 0;
      }
      .sub-groups {
        flex: 1;
        .sub-group {
          display: flex;
          align-items: flex-start;
          margin-bottom: 8px;
          .sub-title {
            min-width: 80px;
            color: #666;
            font-size: 14px;
            margin-right: 12px;
            flex-shrink: 0;
          }
          .tag-list {
        display: flex;
        flex-wrap: wrap;
        gap: 8px 8px;
        .tag-card {
          display: inline-block;
          background: #f3f6fa;
          color: #333;
          border-radius: 6px;
          padding: 4px 14px;
          font-size: 14px;
          margin-bottom: 4px;
          min-width: 40px;
          text-align: center;
          box-sizing: border-box;
          transition: background 0.2s;
        }
      }
    }
  }
}
}
}
</style>
