<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { ElTabs, ElTabPane } from 'element-plus'
import drugStore from './components/drug-store.vue'
import drugStoreApply from './components/drug-store-apply.vue'

const route = useRoute()
const activeTab = ref('application')

// 监听路由参数变化并设置活动标签页
function setActiveTabFromQuery() {
  const { action } = route.query
  if (action === 'add') {
    activeTab.value = 'management'
  }
}

// 页面挂载时设置初始标签页
onMounted(() => {
  setActiveTabFromQuery()
})

// 当路由参数变化时，更新标签页
watch(() => route.query, () => {
  setActiveTabFromQuery()
}, { deep: true })
</script>

<template>
  <div class="p-2">
    <div class="card-block">
      <el-tabs v-model="activeTab" class="w-full">
        <el-tab-pane label="DTP药店申请单" name="application">
          <drug-store-apply />
        </el-tab-pane>

        <el-tab-pane label="DTP药店管理" name="management">
          <drug-store />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
