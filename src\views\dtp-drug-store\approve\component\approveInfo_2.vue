<template>
  <div class="approve-text">
    <div class="approve-info-content">
      <!--      <FormCreate v-model:api="formApi" :rule="options.form || []" :disabled="true" :option="options" v-if="options.formData" />-->
      <div>
        <div class="text-[#1D212B] text-[14px] py-[10px]">基本信息</div>
        <div>
          <div class="flex items-center mb-[8px]">
            <div class="text-[#869199] text-[14px] font-400 w-[200px]">医疗机构</div>
            <div class="text-[#1D212B] text-[14px] font-400">{{ options.formData.insName }}</div>
          </div>
          <div class="flex items-center mb-[8px]">
            <div class="text-[#869199] text-[14px] font-400 w-[200px]">药店距离</div>
            <div class="text-[#1D212B] text-[14px] font-400">{{ options.formData.distance }}</div>
          </div>
          <div class="flex items-center mb-[8px]">
            <div class="text-[#869199] text-[14px] font-400 w-[200px]">地址</div>
            <div class="text-[#1D212B] text-[14px] font-400">
              {{ options.formData.province }}{{ options.formData.city }}{{ options.formData.district }}{{ options.formData.address }}
            </div>
          </div>
        </div>
      </div>
      <div class="pt-[16px]">
        <div class="text-[#1D212B] text-[14px] py-[10px]">药店信息</div>
        <div class="bg-white rounded-[8px] mx-[8px] mb-[16px]">
          <div class="p-[16px]">
            <div class="flex items-center mb-[8px]">
              <div class="text-[#869199] text-[14px] font-400 w-[124px]">药店名称</div>
              <div class="text-[#1D212B] text-[14px] font-400">{{ options.formData.dsName }}</div>
            </div>
            <div class="flex items-center mb-[8px]">
              <div class="text-[#869199] text-[14px] font-400 w-[124px]">药店编码</div>
              <div class="text-[#1D212B] text-[14px] font-400">{{ options.formData.dsMdmCode }}</div>
            </div>
            <div class="flex items-center mb-[8px]">
              <div class="text-[#869199] text-[14px] font-400 w-[124px]">关联药店类型</div>
              <div class="text-[#1D212B] text-[14px] font-400">{{ options.formData.insDsType }}</div>
            </div>
            <div class="flex items-center mb-[8px]">
              <div class="text-[#869199] text-[14px] font-400 w-[124px]">药店类型</div>
              <div class="text-[#1D212B] text-[14px] font-400">{{ options.formData.dsType }}</div>
            </div>
            <div class="flex items-center mb-[8px]">
              <div class="text-[#869199] text-[14px] font-400 w-[124px]">上级药店</div>
              <div class="text-[#1D212B] text-[14px] font-400">{{ options.formData.superiorDsName }}</div>
            </div>
            <div class="flex items-center mb-[8px]">
              <div class="text-[#869199] text-[14px] font-400 w-[124px]">上级药店编码</div>
              <div class="text-[#1D212B] text-[14px] font-400">{{ options.formData.superiorDsCode }}</div>
            </div>
            <div class="flex items-center mb-[8px]">
              <div class="text-[#869199] text-[14px] font-400 w-[124px]">数据采集方式</div>
              <div class="text-[#1D212B] text-[14px] font-400">{{ options.formData.dsSource }}</div>
            </div>
            <div class="flex items-center mb-[8px]">
              <div class="text-[#869199] text-[14px] font-400 w-[124px]">药店性质</div>
              <div class="text-[#1D212B] text-[14px] font-400">{{ options.formData.dsNature }}</div>
            </div>
            <div class="flex items-center">
              <div class="text-[#869199] text-[14px] font-400 w-[124px]">药店支付类型</div>
              <div class="text-[#1D212B] text-[14px] font-400">{{ options.formData.dsEconomicStr }}</div>
            </div>
            <div class="flex items-center">
              <div class="text-[#869199] text-[14px] font-400 w-[124px]">是否HIS对接</div>
              <div class="text-[#1D212B] text-[14px] font-400">{{ options.formData.hisDocking }}</div>
            </div>

            <view class="mx-[16px] my-[8px] h-[1px] border-[1px] border-dashed border-transparent border-b-[#E5E6EB]"></view>
            <view v-for="spec in options.formData.specList || []">
              <view class="flex items-top mb-[8px]">
                <view class="text-[#869199] text-[14px] font-400 w-[124px]">品规名称</view>
                <view class="text-[#1D212B] text-[14px] font-400">{{ spec.value }}</view>
              </view>
              <view class="flex items-top mb-[8px]">
                <view class="text-[#869199] text-[14px] font-400 w-[124px]">是否商务开发</view>
                <view class="text-[#1D212B] text-[14px] font-400">{{ options.formData.businessContact === '1' ? '是' : '否' }}</view>
              </view>
              <view class="flex items-top mb-[8px]">
                <view class="text-[#869199] text-[14px] font-400 w-[124px]">开发状态</view>
                <view class="text-[#1D212B] text-[14px] font-400">{{ options.formData.salesStatus }}</view>
              </view>
              <!--            <view class="flex items-center">-->
              <!--              <view class="text-[#869199] text-[14px] font-400 w-[124px]">是否上架</view>-->
              <!--              <view class="text-[#1D212B] text-[14px] font-400">药店xxxx</view>-->
              <!--            </view>-->
              <view class="mx-[16px] my-[8px] h-[1px] border-[1px] border-dashed border-transparent border-b-[#E5E6EB]"></view>
            </view>

            <div class="mx-[16px] my-[8px] h-[1px] border-[1px] border-dashed border-transparent border-b-[#E5E6EB]"></div>
            <div class="flex flex-col mb-[8px]">
              <div class="text-[#869199] text-[14px] font-400 mb-[16px]">照片(营业执照、经营许可证、门头照)</div>
              <div style="display: flex">
                <div style="margin-right: 20px" v-for="(item,index) in ossUrlList" :key="index">
                  <el-image :initial-index="index" :preview-src-list="ossUrlList" style="width: 300px; height: 200px;" :src="item" fit="fill" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="text-[#1D212B] text-[14px] py-[10px]">关联药店</div>
      <div class="bg-white px-[16px] pb-[16px]">
        <div class="text-[#4E595E] text-[14px] pb-[12px]">已关联({{options.formData.insDsList?.length || '0'}})</div>
        <div
          v-for="item in options.formData.insDsList || []"
          :key="item.dsMdmCode"
          class="px-[16px] py-[8px] bg-[#F2F6FF] rounded-[4px] mb-[12px]"
          style="border: 1px solid #92A8F8"
        >
          <div class="text-[#2551F2] text-[16px] pb-[4px]">{{item.dsName}}</div>
          <div class="text-[#869199] text-[12px]">药店编码：{{item.dsMdmCode}}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup name="ApproveInfo">
import { useRoute } from "vue-router";

import { ref } from 'vue';
import { getFileInfo, taskInstanceFormApi } from "./../../../../api/flow/index.ts";
import { getToken } from "./../../../../utils/auth.ts";
import FormCreate from "@form-create/element-ui";
import { getInsDsChannelsSalesStatusApi, insDsSpecListApi } from "./../../../../api/business/insDs";
import { dowTemplte } from '@/api/system/oss';

const route = useRoute();
const props = defineProps(['form', 'currentProcess'])

const formType = ref('');
const flowProcess = ref({});
const specList = ref([]);
const salesStatusList = ref([]);
const options = ref({
  onSubmit: (formData) => {
    alert(JSON.stringify(formData))
  },
  submitBtn: {show: false},
  resetBtn: {show: false},
  formData: {},
  beforeFetch: (config, data) => {
    config.headers = {
      ...(config.headers || {}),
      authorization: 'Bearer ' + localStorage.getItem('Admin-Token'), // 让每个请求携带自定义token 请根据实际情况自行修改
      clientid: import.meta.env.VITE_APP_CLIENT_ID
    }
  }
});
const ossUrlList = ref([])
const formApi = ref(null);

const refactorFileList = async (data) => {
  const attUrlList = data.attUrlList || [];
  const _ = await Promise.all(attUrlList.map(async d => {
    const res = await getFileInfo(d.ossId);
    return res?.data?.rows && res.data.rows[0]
  }))
  data.attUrlList = _;
}

const getFormValue = async () => {
  const res = await taskInstanceFormApi({
    processId: route.query.processId,
    instanceId: route.query.instanceId
  })
  const form = FormCreate.parseJson(FormCreate.parseJson(res.data.flowProcess.formContent).rules);

  const formData = res.data?.formData && JSON.parse(res.data.formData) || {}
  await refactorFileList(formData);
  options.value.formData = {
    ...formData,
    salesFlow: formData.salesFlow || []
  }
  console.log("ddddd",options.value.formData)
  if(options.value.formData.fileList) {
     JSON.parse(options.value.formData.fileList).forEach(async (el) => {
     const res = await dowTemplte(el.ossId);
    ossUrlList.value.push(res.data.rows?.[0]?.url);
  })
   }
  options.value.form = [form.rule[0], form.rule[1], form.rule[2], form.rule[3], form.rule[4], form.rule[5], form.rule[6], form.rule[7]] || [];
  await getInsDsSpecList(formData);
}

const getInsDsSpecList = async (formData) => {
  const res = await insDsSpecListApi({
    insCode: formData.insCode
  });

  specList.value = res.rows || [];
}

const salesStatusOptions = async () => {
  const { rows } = await getInsDsChannelsSalesStatusApi();
  if(rows && rows.length) {
    salesStatusList.value = rows.map(d => ({...d, label: d.dictLabel, value: d.dictValue}));
  }
}

const getSalesStatusText = value => {
  const _ = salesStatusList.value.find(d => d.dictValue === value) || {};

  return _.dictLabel || '';
}

// 院外关联渠道
const external_affiliation_channels = ['gs_special_external_affiliation_channels_apply', 'gs_routine_external_affiliation_channels_apply'];

const getFormDetail = async () => {
  try {
    const res = await taskInstanceFormApi({
      processId: route.query.processId,
      instanceId: route.query.instanceId
    })

    const formData = res.data?.formData && JSON.parse(res.data.formData) || {}
    flowProcess.value = res?.data || {}
    // 获取审批表单类型
    formType.value = res?.data?.flowProcess?.processKey
    if(external_affiliation_channels.includes(res?.data?.flowProcess?.processKey)) {
      formType.value = 'external_affiliation_channels';
    }
    options.value.formData = formData
  } catch (e) {

  } finally {
  }
}


const getExtConfig = str => {
  if (!str) return {};
  try {
    return JSON.parse(str).keyWords;
  } catch (e) {
    return {};
  }
}

const getFormData = (str) => {
  if (!str) return {};
  try {
    return JSON.parse(str);
  } catch (e) {
    return {};
  }
};

onMounted(() => {
  getFormValue();
  salesStatusOptions();
  getFormDetail();
})
</script>
<style lang="scss" scoped>
.approve-text {
 overflow-y: scroll;

  .approve-info-content {
    min-height: 600px;
    overflow-y: auto;
    .item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    .label {
      width: 30%;
    color: #86909C;
  }
  .result {
    color: #1D212B;
  }
    }
  }
  .avatar {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    vertical-align: middle;
  }
   .nameAvatar {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background: #92a8f8;
    color: #fff;
    line-height: 28px;
    text-align: center;
    min-width: 28px;

  }
  .dot {
   position: relative;
    .dot-bottom {
      position: absolute;
      width: 10px;
    height: 10px;
    bottom: 0;
    right: 0;

    }

  }
:deep(.el-timeline-item__dot) {
  left:-7px
}
:deep(.el-timeline-item__tail) {
  top: 39%;
  border-left: 2px solid #92a8f8;
  height: 48px;
}
}

.approve-text::-webkit-scrollbar-track {
  background-color: #fff;
}
</style>
