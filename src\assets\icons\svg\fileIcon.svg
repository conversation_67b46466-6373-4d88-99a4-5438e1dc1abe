<svg xmlns="http://www.w3.org/2000/svg" width="40" height="27" viewBox="0 0 40 27" fill="none">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M24.3713 3.00928H24.3538H14.6667C13.1964 3.00928 12 4.20572 12 5.67594V8.34261V10.1204V16.3426C12 17.8128 13.1964 19.0093 14.6667 19.0093H25.3333C26.8036 19.0093 28 17.8128 28 16.3426V10.1204V8.34261V6.54329L24.3713 3.00928Z" fill="#FFC83B"/>
  <g filter="url(#filter0_di_262_141102)">
    <path d="M22.6809 9.12012H15.2763C14.9387 9.12012 14.665 9.39379 14.665 9.73138C14.665 10.069 14.9387 10.3426 15.2763 10.3426H22.6809C23.0185 10.3426 23.2922 10.069 23.2922 9.73138C23.2922 9.39379 23.0185 9.12012 22.6809 9.12012Z" fill="white"/>
  </g>
  <g filter="url(#filter1_di_262_141102)">
    <path d="M19.6428 6.73499H15.237C14.9211 6.73499 14.665 6.99107 14.665 7.30696C14.665 7.62286 14.9211 7.87894 15.237 7.87894H19.6428C19.9587 7.87894 20.2148 7.62286 20.2148 7.30696C20.2148 6.99107 19.9587 6.73499 19.6428 6.73499Z" fill="white"/>
  </g>
  <g filter="url(#filter2_di_262_141102)">
    <path d="M25.3347 12.786C25.3347 12.2953 24.9925 11.8971 24.5726 11.8971H15.4272C15.0072 11.8971 14.665 12.2953 14.665 12.786L14.665 14.2651C14.665 15.0936 15.3366 15.7651 16.165 15.7651H23.8347C24.6631 15.7651 25.3347 15.0936 25.3347 14.2651V12.786Z" fill="white"/>
  </g>
  <g filter="url(#filter3_f_262_141102)">
    <ellipse cx="20.3332" cy="17.3427" rx="7.66667" ry="1.66667" fill="#FDD97C"/>
  </g>
  <path d="M26.3618 6.54335H28.0001L24.3618 3V4.54335C24.3618 5.64792 25.2572 6.54335 26.3618 6.54335Z" fill="#FAE0B9"/>
  <defs>
    <filter id="filter0_di_262_141102" x="0.152617" y="2.52356" width="37.6518" height="30.2474" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="7.91587"/>
      <feGaussianBlur stdDeviation="7.25621"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.45 0 0 0 0 0.324 0 0 0 0 0 0 0 0 0.25 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_262_141102"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_262_141102" result="shape"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="-0.5"/>
      <feGaussianBlur stdDeviation="1"/>
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.991667 0 0 0 0 0.84804 0 0 0 0 0.338819 0 0 0 0.5 0"/>
      <feBlend mode="normal" in2="shape" result="effect2_innerShadow_262_141102"/>
    </filter>
    <filter id="filter1_di_262_141102" x="0.152617" y="0.138429" width="34.5746" height="30.1688" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="7.91587"/>
      <feGaussianBlur stdDeviation="7.25621"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.45 0 0 0 0 0.378 0 0 0 0 0 0 0 0 0.25 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_262_141102"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_262_141102" result="shape"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="-0.5"/>
      <feGaussianBlur stdDeviation="1"/>
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.991667 0 0 0 0 0.84804 0 0 0 0 0.338819 0 0 0 0.5 0"/>
      <feBlend mode="normal" in2="shape" result="effect2_innerShadow_262_141102"/>
    </filter>
    <filter id="filter2_di_262_141102" x="0.152617" y="5.30054" width="39.6943" height="32.8929" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="7.91587"/>
      <feGaussianBlur stdDeviation="7.25621"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.45 0 0 0 0 0.27 0 0 0 0 0 0 0 0 0.25 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_262_141102"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_262_141102" result="shape"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="-2"/>
      <feGaussianBlur stdDeviation="3.29828"/>
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.950431 0 0 0 0 0.690196 0 0 0 0.6 0"/>
      <feBlend mode="normal" in2="shape" result="effect2_innerShadow_262_141102"/>
    </filter>
    <filter id="filter3_f_262_141102" x="0.666504" y="3.67603" width="39.3335" height="27.3334" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
      <feGaussianBlur stdDeviation="6" result="effect1_foregroundBlur_262_141102"/>
    </filter>
  </defs>
</svg>