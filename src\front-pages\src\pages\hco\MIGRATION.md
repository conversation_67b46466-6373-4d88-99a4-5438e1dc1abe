# HCO API 迁移指南

本文档说明如何从旧版本的 HCO API 迁移到最新版本。

## 🔄 主要变化

### HCO 分页列表查询接口重大变化

**接口路径**: `/data/institution/page`
**接口名称**: HCO 分页列表查询（支持自定义条件组）

| 项目       | v1.0 版本 | v1.1 版本    | v1.3 版本（最新）   |
| ---------- | --------- | ------------ | ------------------- |
| HTTP 方法  | `GET`     | `POST`       | `POST`              |
| 查询方式   | 简单参数  | queryBo 对象 | QueryCondition 数组 |
| 逻辑支持   | 无        | 无           | and/or 逻辑组合     |
| 操作符支持 | 基础      | 基础         | 6 种操作符          |

## 📝 代码迁移示例

### 1. 类型定义变化

**v1.0 版本**:

```typescript
interface HcoPageQuery {
  insName?: string;
  insType?: string;
  provinceName?: string;
  pageSize?: number;
  pageNum?: number;
  orderByColumn?: string;
  isAsc?: string;
}
```

**v1.1 版本**:

```typescript
interface InstitutionQueryBo {
  insName?: string;
  insType?: string;
  provinceName?: string;
  // ... 其他查询条件
}

interface HcoPageQuery {
  queryBo?: InstitutionQueryBo;  // 查询条件
  pageSize?: number;             // 分页参数
  pageNum?: number;
  orderByColumn?: string;
  isAsc?: string;
}
```

**v1.3 版本（最新）**:

```typescript
interface QueryCondition {
  field?: string;     // 字段名
  operator?: string;  // 操作符: eq, neq, contains, not_contains, empty, not_empty
  value?: string;     // 右值
}

interface HcoPageQuery {
  conditions?: QueryCondition[];  // 条件数组
  logic?: string;                 // 条件逻辑 "and" 或 "or"
  pageSize?: number;              // 分页参数
  pageNum?: number;
}
```

### 2. API 调用变化

**v1.0 版本**:

```typescript
const params: HcoPageQuery = {
  insName: '北京医院',
  insType: '医院',
  pageSize: 10,
  pageNum: 1
};

const response = await hcoPageQuery(params);
```

**v1.1 版本**:

```typescript
const params: HcoPageQuery = {
  queryBo: {
    insName: '北京医院',
    insType: '医院'
  },
  pageSize: 10,
  pageNum: 1
};

const response = await hcoPageQuery(params);
```

**v1.3 版本（最新）**:

```typescript
const params: HcoPageQuery = {
  conditions: [
    {
      field: 'insName',
      operator: 'contains',
      value: '北京医院'
    },
    {
      field: 'insType',
      operator: 'eq',
      value: '医院'
    }
  ],
  logic: 'and',
  pageSize: 10,
  pageNum: 1
};

const response = await hcoPageQuery(params);
```

### 3. Composable 使用变化

**旧版本**:

```typescript
const hcoList = useHcoList();

// 直接传递查询参数
await hcoList.fetchHcoList({
  insName: '北京医院',
  pageSize: 10
});
```

**新版本**:

```typescript
const hcoList = useHcoList();

// 只传递查询条件，分页参数由composable管理
await hcoList.fetchHcoList({
  insName: '北京医院'
});
```

## 🛠️ 迁移步骤

### 步骤 1: 更新类型定义

1. 导入新的 `InstitutionQueryBo` 类型
2. 更新 `HcoPageQuery` 的使用方式

### 步骤 2: 更新 API 调用

1. 将查询条件包装在 `queryBo` 对象中
2. 分页参数保持在顶层

### 步骤 3: 更新组件代码

1. 如果直接使用 API，按照新的参数结构调用
2. 如果使用 composable，只需传递查询条件

### 步骤 4: 测试验证

1. 确保查询功能正常
2. 验证分页功能
3. 检查错误处理

## ⚠️ 注意事项

1. **向后兼容性**: 新版本的 composable 保持了相同的使用接口，只是内部实现有所调整
2. **错误处理**: 错误处理逻辑保持不变
3. **类型安全**: 新版本提供了更好的类型安全性

## 🔍 常见问题

### Q: 为什么要改为 POST 方法？

A: POST 方法可以支持更复杂的查询条件，特别是当查询参数较多或包含复杂对象时。

### Q: 旧的查询方式还能用吗？

A: 不能，需要按照新的参数结构进行调用。但 composable 的使用方式基本保持不变。

### Q: 如何快速迁移现有代码？

A: 主要是将查询条件包装在 `queryBo` 对象中，其他逻辑基本不变。

## 📞 技术支持

如果在迁移过程中遇到问题，请参考：

1. `README.md` - 完整的使用文档
2. `api-usage-example.ts` - 最新的使用示例
3. 或联系开发团队获取支持
