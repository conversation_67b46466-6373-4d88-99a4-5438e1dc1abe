<template>
  <view class="gdmap" v-show="false">
    <view id="containerBox" />

    <view>
      <!-- <TopSearch
        @top-search="search"
        placeholder="请输入"
        ref="topSearchRef"
        :searchFlag="true"
      /> -->
    </view>
    <!-- <view id="containerBox" /> -->
    <view class="address">
      <!-- <nut-radio-group v-model="state.radioChecked" text-position="left">
        <GlobalNutRadio
          v-for="item in state.pois"
          :value="item.id"
          :key="item.id"
        >
          <template #name>
            <view @click="selectAdress(item)">
              <text>{{ item.name }}</text>
            </view>
          </template>
        </GlobalNutRadio>
      </nut-radio-group> -->
    </view>
  </view>
  <view>
    <!-- <view>
      <FooterButton text="确定" @click-button="clickAddButton">
        <template #icon>
          <Uploader color="#fff" />
        </template>
      </FooterButton>
    </view> -->
  </view>
</template>

<script setup>
// import Taro, { useDidShow, useRouter } from "@tarojs/taro";
import { onMounted, reactive } from 'vue';
// import TopSearch from "@/pages/components/topSearch/topSearch.vue";
// import FooterButton from "@/pages/components/footerButton/index.vue";
// import GlobalNutRadio from "@/pages/components/globalNutRadio/globalNutRadio.vue";
const emits = defineEmits(['get:option']);
const state = reactive({
  leftTetx: '地图',
  text: '确认',
  pois: [],
  value: '',
  tuningMap: null,
  currentMarker: null,
  position: null,
  radioChecked: ''
});

const search = (value) => {
  console.log("search!!!!!!!!!!!!!!!!!!!!!")
  state.value = value;
  autoInput();
};

const clickAddButton = () => {
  const location = state.pois.filter((item) => item.id === state.radioChecked)[0];

  // Taro.setStorage({ key: 'location', data: location });
  // Taro.navigateBack({
  //   delta: 1
  // });
};

// 获取搜索信息
const autoInput = () => {
  // Taro.showToast({
  //   title: '加载中',
  //   icon: 'loading'
  // });
  console.log("---autoInput")
  AMap.plugin('AMap.PlaceSearch', function () {
    var placeSearch = new AMap.PlaceSearch();
    placeSearch.search(state.value, function (status, result) {
        console.log(result,"---ppppppppp");

      if (status === 'complete') {
        console.log(result,"---rrr");
        state.pois = result?.poiList.pois;
        emits('get:option', state.pois || []);
        // Taro.hideLoading();
      } else {
        // Taro.showToast({
        //   title: result,
        //   icon: 'none',
        //   duration: 2000
        // });
      }
    });
  });
};
const selectAdress = (item) => {
  state.currentMarker.setPosition([item.location.lng, item.location.lat]);
  state.tuningMap.setCenter([item.location.lng, item.location.lat]);
};

onMounted(() => {
  const map = new AMap.Map('containerBox', {
    zoom: 15
    // resizeEnable: true
  });
  map.on('click', function (ev) {
    //触发事件的对象
    var target = ev.target;
    //触发事件的地理坐标，AMap.LngLat 类型
    var lnglat = ev.lnglat;
    //触发事件的像素坐标，AMap.Pixel 类型
    var pixel = ev.pixel;
    //触发事件类型
    var type = ev.type;
    console.log(ev, '---target');
  });
  state.tuningMap = map;
  AMap.plugin(['AMap.Geolocation', 'AMap.PlaceSearch'], () => {
    // const geolocation = new AMap.Geolocation({
    //   enableHighAccuracy: true, // 是否使用高精度定位，默认:true
    //   timeout: 10000, // 超过10秒后停止定位，默认：5s
    //   position: 'RB', // 定位按钮的停靠位置
    //   offset: [10, 20], // 定位按钮与设置的停靠位置的偏移量，默认：[10, 20]
    //   zoomToAccuracy: true // 定位成功后是否自动调整地图视野到定位点
    // });
    // geolocation.getCurrentPosition((status, result) => {
    //   if (status === 'complete') {
    //     const { position } = result;
    //     const { lng, lat } = position;
    //     map.setCenter([lng, lat]);
    //     const marker = new AMap.Marker({
    //       icon: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png',
    //       position: [lng, lat],
    //       anchor: 'bottom-center'
    //     });
    //     state.currentMarker = marker;
    //     map.add(marker);
    //     map.setFitView();
    //   } else {
    //     console.error('定位失败');
    //   }
    // });
  });
});
defineExpose({
  search
});
</script>

<style lang="scss" scoped>
.gdmap {
  width: 100%;
  height: 200px;
  overflow: hidden;
  .address {
    padding: 0 12px;
    background: #fff;
    height: 230px;
    // overflow: hidden;
    // overflow-y: scroll;
  }
}

#containerBox {
  display: block;
  width: 100%;
  height: 200px;
  margin-bottom: 8px;
}

#panel {
  background-color: white;
}
</style>
