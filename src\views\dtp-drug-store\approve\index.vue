<template>
  <div class="p-2" v-loading="loading">
    <div class="approve">
      <div class="left overflow-y-auto">
        <div class="flex justify-between">
          <div class="top"><!--来自金妍迪科小程序审批 <el-divider direction="vertical" />-->编号：{{ route.query.instanceId }}</div>
          <!-- DTP药店 二审节点 特殊处理 -->
          <div v-if="detail.currentNodeKey === 'flk_second_approve'" class="flex items-center gap-[8px]">
            <!-- 冯磊 选择DTP专员 -->
            <div
              class="bg-[#FEEDEC] text-[14px] text-center w-[90px] h-[36px] leading-[36px] text-[#F54A45] cursor-pointer"
              @click="approveHandle('refuse')"
              v-if="operationStatus ==='WAIT_AUDITOR_SUBMIT' && bpmRole === 'SECOND_AUDIT'"
            >
              拒绝
            </div>
            <div
              v-show="applyButtonStatus === '1' && operationStatus ==='WAIT_AUDITOR_SUBMIT' && bpmRole === 'SECOND_AUDIT'"
              class="bg-[#E8F0FF] text-[14px] text-center w-[120px] h-[36px] leading-[36px] text-[#2551F2] cursor-pointer"
              @click="adminApprove()"
            >
              提交DTP专员
            </div>
            <template v-if="operationStatus === 'DTP_EMP_OPERATION' && applyButtonStatus === '4' && bpmRole === 'DTP_AUDIT'">
              <div
                class="bg-[#E8F0FF] text-[14px] text-center w-[120px] h-[36px] leading-[36px] text-[#2551F2] cursor-pointer"
                @click="handleDtpAction('1')"
              >
                接受
              </div>
              <div
                class="bg-[#FEEDEC] text-[14px] text-center w-[90px] h-[36px] leading-[36px] text-[#F54A45] cursor-pointer"
                @click="showRejectDialog()"
              >
                拒绝
              </div>
            </template>
            <!-- DTP专员 上传资料  -->
            <template v-if="operationStatus === 'DTP_EMP_OPERATION' && applyButtonStatus === '3' && bpmRole === 'DTP_AUDIT'">
              <div
                class="bg-[#E8F0FF] text-[14px] text-center w-[120px] h-[36px] leading-[36px] text-[#2551F2] cursor-pointer"
                @click="handleDtpSubmit()"
              >
                提交
              </div>
              <div
                class="bg-[#E8F0FF] text-[14px] text-center w-[90px] h-[36px] leading-[36px] text-[#4E595E] cursor-pointer"
                @click="handleDtpSave()"
              >
                保存
              </div>
            </template>
            <!-- 冯磊 再次编辑标签 并审核 -->
            <template v-if="applyButtonStatus === '1' && operationStatus === 'DTP_EMP_COMPLETE' && bpmRole === 'SECOND_AUDIT'">
              <div
                class="bg-[#E8F0FF] text-[14px] text-center w-[120px] h-[36px] leading-[36px] text-[#2551F2] cursor-pointer"
                @click="handleMgrApprove('1')"
              >
                同意
              </div>
              <div
                class="bg-[#FEEDEC] text-[14px] text-center w-[90px] h-[36px] leading-[36px] text-[#F54A45] cursor-pointer"
                @click="approveHandle('refuse')"
              >
                拒绝
              </div>
            </template>
          </div>
          <div v-else class="flex items-center gap-[8px]">
            <div
              v-show="isRefuse"
              class="bg-[#FEEDEC] text-[14px] text-center w-[90px] h-[36px] leading-[36px] text-[#F54A45] cursor-pointer"
              @click="approveHandle('refuse')"
            >
              拒绝
            </div>
            <div
              v-show="isRevoke"
              class="bg-[#E8F0FF] text-[14px] text-center w-[90px] h-[36px] leading-[36px] text-[#4E595E] cursor-pointer"
              @click="approveHandle('revoke')"
            >
              撤回
            </div>
            <div
              v-show="isAgree"
              class="bg-[#F3F4F5] text-[14px] text-center w-[90px] h-[36px] leading-[36px] text-[##4E595E] cursor-pointer"
              @click="approveHandle('agree')"
            >
              同意
            </div>
            <div @click="handleBack()" class="cursor-pointer pr-[6px]">
              <img :src="backIcon" class="w-[10px] w-[10px]" />
            </div>
          </div>
        </div>
        <el-divider :style="{ margin: '10px 0 10px 0' }" />
        <div class="approve-info">
          <div class="title flex flex-wrap">
            <span
              class="title-text"
              style="font-size: 24px;"
              >{{ getFormData(flowProcess.formData)?.[getExtConfig(flowProcess?.flowProcess?.extConfig)?.mainTitle?.field] }}</span
            >
            <span
              class="px-[6px] text-[12px] text-[#2551F2] h-[20px] leading-[20px] mr-[8px]"
              style="border: 1px solid #2551F2; font-size: 12px"
              >{{ flowProcess?.flowProcess?.processName }}</span
            >
            <ProcessStatusTag :instanceState="detail?.instanceState?.toString()" />
          </div>
          <div class="approve-info-name flex items-center">
            <img src="" alt="" class="avatar" v-if="false" />
            <div class="nameAvatar">{{ detail?.creator?.slice(-1) }}</div>
            <span style="color: #1D212B;">{{ detail?.creator }}</span>
            <el-divider direction="vertical" />
            <span class="color-86909C">提交于：{{ detail?.createTime}}</span>
          </div>
          <ApproveInfo :formData="detail.variable" />
          <template v-if="applyButtonStatus === '1' && operationStatus ==='WAIT_AUDITOR_SUBMIT' && bpmRole === 'SECOND_AUDIT'">
            <h2 class="text-xl font-bold mb-4">指派DTP专员</h2>
            <!-- 添加指派DTP专员按钮 -->
            <div class="mt-4 mb-4 flex items-center">
              <el-button type="primary" @click="showAssignDialog" icon="Plus" size="large">选择专员</el-button>
            </div>
          </template>

          <!-- 显示已选专员列表 -->
          <div v-if="selectedPersons.length > 0" class="mb-4">
            <h3 class="text-lg font-semibold mb-2">已选择的专员（{{ selectedPersons.length }}人）</h3>
            <el-table :data="selectedPersons" border style="width: 100%">
              <el-table-column type="index" width="50" label="序号" />
              <el-table-column prop="code" label="工号" width="120" />
              <el-table-column prop="name" label="姓名" width="120" />
              <el-table-column prop="deptName" label="所属部门" />
            </el-table>
          </div>

          <!-- 添加标签组件 -->
          <div>
            <div
              v-if="(applyButtonStatus === '1' || applyButtonStatus === '0') && (operationStatus === 'DTP_EMP_OPERATION' || operationStatus === 'DTP_EMP_COMPLETE')"
            >
              <DtpAssistantRecordsView
                v-if="assistantAllRecordData"
                :recordData="assistantAllRecordData"
                :editable="true"
                v-model="selectedTags"
                @change="handleTagsChange"
                :disabled="applyButtonStatus === '0' || detail.currentNode !== 'flk_second_approve'"
                :canEditTag="applyButtonStatus === '1' && operationStatus === 'DTP_EMP_COMPLETE' && bpmRole === 'SECOND_AUDIT'"
              />
            </div>
            <DtpPersonModify
              v-model="selectedTags"
              @change="handleTagsChange"
              v-model:fileList="uploadFiles"
              @file-change="handleFileChange"
              v-model:drugScoreList="drugScoreList"
              @score-change="handleScoreChange"
              v-else-if="operationStatus === 'DTP_EMP_OPERATION' || operationStatus === 'DTP_EMP_COMPLETE'"
              :disabled="applyButtonStatus !== '3'"
            />
          </div>

          <!-- 历史记录展示 -->
          <div v-if="(bpmRole === 'SECOND_AUDIT' || bpmRole === 'THIRD_AUDIT') && historyRecords?.length && applyType === 'DTP关联药店新增'">
            <h2 class="text-xl font-bold">历史审批记录</h2>
            <DtpAssistantRecordsView v-for="(item, index) in historyRecords" :key="index" :recordData="item" :editable="false" :disabled="true" />
          </div>

          <!-- 将AssignPerson组件与状态绑定 -->
          <AssignPerson
            v-model:visible="assignDialogVisible"
            :loading="assignLoading"
            :selectedPersons="selectedPersons"
            @confirm="handleConfirmAssign"
            @cancel="handleCancelAssign"
          />
        </div>
        <el-dialog v-model="optionShow" title="审批意见" show-cancel-button width="600">
          <el-input v-model="opinion" label="审批意见" type="textarea" placeholder="请输入审批意见" />
          <template #footer>
            <el-button type="info" plain @click="cancel">取消</el-button>
            <el-button type="primary" @click="confirm" :loading="loading">确认</el-button>
          </template>
        </el-dialog>
        <el-dialog v-model="rejectDialogVisible" title="拒绝原因" width="500px">
          <el-input v-model="rejectReason" type="textarea" :rows="4" placeholder="请输入拒绝原因"></el-input>
          <template #footer>
            <span class="dialog-footer">
              <el-button @click="rejectDialogVisible = false">取消</el-button>
              <el-button type="primary" @click="handleDtpAction('2')" :loading="dtpActionLoading"> 确认 </el-button>
            </span>
          </template>
        </el-dialog>

        <!-- 渠道负责人拒绝对话框 -->
        <el-dialog v-model="mgrRejectDialogVisible" title="拒绝原因" width="500px">
          <el-input v-model="mgrRejectReason" type="textarea" :rows="4" placeholder="请输入拒绝原因"></el-input>
          <template #footer>
            <span class="dialog-footer">
              <el-button @click="mgrRejectDialogVisible = false">取消</el-button>
              <el-button type="primary" @click="handleMgrReject()" :loading="mgrActionLoading"> 确认 </el-button>
            </span>
          </template>
        </el-dialog>
      </div>
      <div class="right overflow-y-auto">
        <ApproveAction :instanceId="route.query.instanceId" />
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRoute, useRouter } from "vue-router";
import backIcon from '@/bpm/src/assets/images/approve_back_icon.png';
import ApproveInfo from "./component/approveInfo";
import ProcessStatusTag from "./component/processStatusTag";
import ApproveAction from "./component/approveAction";
import AssignPerson from "./component/assignPerson";
import DtpPersonModify from './component/dtpPersonModify.vue';
import DtpAssistantRecordsView from './component/dtpAssistantRecordsView.vue';

import {
  agreeTaskApi,
  ccTaskListApi,
  doneListApi,
  refuseTaskApi,
  taskApplyListApi,
  taskNodeInfoApi,
  todoTaskApi,
  taskInstanceDetailApi,
  taskInstanceActionApi,
  revokeTaskApi,
  taskInstanceFormApi,
  getFileInfo
} from "@/bpm/src/api/flow";
import { ElMessage } from 'element-plus';
import { assignDtpEmployee, getApplyStatusApi, getOperationStatusApi, dtpEmployeeOperation, dtpEmployeeWrite, getAssistantRecord, getAllAssistantRecords, channelManagerSupplementTag, bpmRoleApi, getAssignHistory } from "@/api/dtp-drug-store";
import { getDicts } from "@/api/system/dict/data";

const route = useRoute();
const router = useRouter();

const state = reactive({
  queryParams: {
    name: '',
    pageSize: 999,
    pageNum: 1,
    activeIndex: 1
  },
  menuList: [
    { name: '待办', icon: 'wait', key: 1 },
    { name: '已办', icon: 'done', key: 2 },
    { name: '抄送我', icon: 'copy', key: 3 },
    { name: '已发送', icon: 'lssued', key: 4 },
  ],
  activeName: '1',
  taskList: []
})

const currentProcess = ref(null);
const currentNodeInfo = ref(null);
const approveRef = ref(null);
const approveType = ref('');
const optionShow = ref(false);
const opinion = ref('');
const loading = ref(false);
const detail = ref({});
const flowProcess = ref({});
const isAgree = ref(false);
const isRefuse = ref(false);
const isRevoke = ref(false);

// 添加assignPerson相关的状态
const assignDialogVisible = ref(false);
const assignLoading = ref(false);
const selectedPersons = ref([]);
const saveLoading = ref(false);

// 添加标签相关状态
const selectedTags = ref([]);
const tagList = ref([]);
// 添加文件相关状态
const uploadFiles = ref([]);
// 添加药店评分相关状态
const drugScoreList = ref([]);
// 申请列表按钮展示状态 0:详情 1:管理员审批 2:DTP专员查看详情 3:DTP专员填写资料
const applyButtonStatus = ref('0');
const applyType = ref('');
const operationStatus = ref('');

// 添加新的对话框状态
const rejectDialogVisible = ref(false);
const rejectReason = ref('');
const dtpActionLoading = ref(false);

// 添加管理员拒绝对话框状态
const mgrRejectDialogVisible = ref(false);
const mgrRejectReason = ref('');
const mgrActionLoading = ref(false);

// Add state for storing the assistant record data
const assistantAllRecordData = ref(null);

// Add state for storing the history records
const historyRecords = ref([]);

// FIRST_AUDIT("dtpEmpRole", "firstAudit","一审专员"),
// SECOND_AUDIT("dtpEmpRole", "firstAudit","二审专员"),
// THIRD_AUDIT("dtpEmpRole", "firstAudit","三审专员"),
// DTP_AUDIT("dtpEmpRole", "firstAudit","DTP专员"),
const bpmRole = ref('');

const handleClick = () => {
}
const handleQuery = () => {
  statusSelect({
    key: state.queryParams.activeIndex
  })
}

const handleBack = () => {
  router.go(-1)
}
// 选择审批状态
const statusSelect = async e => {
  try {
    let api = () => {};
    state.queryParams.activeIndex = e.key;
    if(e.key === 1) {
      api = todoTaskApi
    }else if(e.key === 2) {
      api = doneListApi
    }else if(e.key === 3) {
      api = ccTaskListApi
    }else if(e.key === 4) {
      api = taskApplyListApi
    }
    loading.value = true;
    const res = await api({
      pageSize: state.queryParams.pageSize,
      pageNum: state.queryParams.pageNum,
      processName: state.queryParams.name
    });
    state.taskList = res.rows || [];
    currentProcess.value = null;
    currentNodeInfo.value = null;
  } catch (e) {
    console.error('Error fetching task list:', e);
    ElMessage.error('获取任务列表失败');
  } finally {
    loading.value = false
  }
}

// 选择某一个审批
const selectProcess = async (item) => {
  currentProcess.value = item;
  await getNodeInfo();
}

// 获取节点详情
const getNodeInfo = async () => {
  const res = await taskNodeInfoApi({
    processId: currentProcess.value.processId,
    nodeKey: currentProcess.value.currentNode
  });
  currentNodeInfo.value = res.data || {};
}

// 审批流操作 同意/拒绝
const approveHandle = async (type) => {
  approveType.value = type;
  optionShow.value = true;
}

const cancel = () => {
  approveType.value = '';
  optionShow.value = false;
}

const confirm = async () => {
  try {
    if(approveType.value === 'refuse' && (opinion.value === '' || opinion.value === null || opinion.value === undefined)) {
      ElMessage.error('请输入审批意见')
      return false
    }

    loading.value = true;
    const query = {
      taskId: detail.value.taskId,
      instanceId: route.query.instanceId,
      opinion: opinion.value
    }
    if(route.query.taskId === 'undefined') {
      delete query.taskId;
    }
    if(approveType.value === 'agree') {
      await agreeTaskApi(query)
    }else if(approveType.value === 'refuse') {
      await refuseTaskApi(query)
    }else if(approveType.value === 'revoke') {
      await revokeTaskApi(query)
    }
    optionShow.value = false;
    router.go(-1);
  } catch (e) {
    console.error('Error processing approval action:', e);
    ElMessage.error('审批操作失败');
  } finally {
    loading.value = false;
  }
}

// 管理员同意审批按钮处理
const adminApprove = async () => {
  try {
    if (selectedPersons.value.length === 0) {
      ElMessage.warning('请先选择专员后再提交');
      return;
    }

    loading.value = true;
    // 先保存选择的专员数据
    await saveAssignedPersonnel();

    router.go(-1);
  } catch (error) {
    console.error('管理员审批失败:', error);
    ElMessage.error(error.message || '管理员审批失败');
  } finally {
    loading.value = false;
  }
}

const refactorFileList = async (data) => {
  const attUrlList = data.fileList && JSON.parse(data.fileList) || [];
  const _ = await Promise.all(attUrlList.map(async d => {
    const res = await getFileInfo(d.ossId);
    return res?.data?.rows && res.data.rows[0]
  }))
  data.attUrlList = _;
}


const getDetail = async () => {
  try {
    const res = await taskInstanceDetailApi({
      instanceId: route.query.instanceId
    })
    detail.value = res.data || {}
    console.log(detail.value, 'detail value')
    detail.value.variable = detail.value.variable && JSON.parse(detail.value.variable) || {}

    const formRes = await taskInstanceFormApi({
      processId: route.query.processId,
      instanceId: route.query.instanceId
    })
    flowProcess.value = formRes?.data || {}
    await refactorFileList(detail.value.variable || {})
  } catch (e) {
    console.error('Error fetching instance details:', e);
    ElMessage.error('获取实例详情失败');
  }
}

const getAction = async () => {
  const res = await taskInstanceActionApi({
    instanceId: route.query.instanceId
  });
  isAgree.value = res.data?.isAgree
  isRevoke.value = res.data?.isRevoke
  isRefuse.value = res.data?.isRefuse
};

const getExtConfig = str => {
  if (!str) return {};
  try {
    return JSON.parse(str).keyWords;
  } catch (e) {
    return {};
  }
}

const getFormData = (str) => {
  if (!str) return {};
  try {
    return JSON.parse(str);
  } catch (e) {
    return {};
  }
};

// 显示分配人员对话框的方法
const showAssignDialog = () => {
  assignDialogVisible.value = true;
};

// 处理确认分配人员
const handleConfirmAssign = async (persons) => {
  // 只保存选中的人员到前端状态，不调用API
  selectedPersons.value = persons;
  console.log('已选择的DTP专员:', selectedPersons.value);

  // 关闭弹窗
  assignDialogVisible.value = false;

  // 显示选择成功的消息
  ElMessage.success('已选择DTP专员');
};

// 处理取消分配人员
const handleCancelAssign = () => {
  console.log('取消选择DTP专员');
};

// 获取标签字典
const fetchTagDict = async () => {
  try {
    const response = await getDicts('dtp_ds_tag');
    if (response && response.data) {
      tagList.value = response.data.map(item => ({
        label: item.dictLabel,
        value: item.dictValue,
        elTagType: item.listClass,
        elTagClass: item.cssClass
      }));
    }
  } catch (error) {
    console.error('获取标签字典失败:', error);
    ElMessage.error('获取标签字典失败');
  }
};

// 处理标签选择变化
const handleTagsChange = (tags) => {
  console.log('选中的标签:', tags);
  selectedTags.value = tags;
};

// 处理文件上传变化
const handleFileChange = (files) => {
  console.log('上传的文件:', files);
  uploadFiles.value = files;
};

// 处理评分变化
const handleScoreChange = (scores) => {
  console.log('药店评分:', scores);
  drugScoreList.value = scores;
};

// 更新保存方法，同时保存人员、标签、附件和评分
const saveAssignedPersonnel = async () => {
  try {
    saveLoading.value = true;

    // 收集所有分类级别的图片ossId
    const categoryOssIds = [];
    drugScoreList.value.forEach(category => {
      if (category.images && category.images.length > 0) {
        category.images.forEach(image => {
          categoryOssIds.push(image.ossId);
        });
      }
    });

    // 收集所有评分项级别的图片ossId
    const scoreItemOssIds = [];
    drugScoreList.value.forEach(category => {
      category.scoreItem.forEach(item => {
        if (item.images && item.images.length > 0) {
          item.images.forEach(image => {
            scoreItemOssIds.push(image.ossId);
          });
        }
      });
    });

    // 合并所有图片ossId
    const allOssIds = [
      ...uploadFiles.value.map(file => file.ossId), // 原有的通用附件
      ...categoryOssIds, // 分类级别图片
      ...scoreItemOssIds // 评分项级别图片
    ];

    // 准备API请求数据，根据assignDtpEmployee API的要求格式化
    const assignData = {
      flowId: route.query.instanceId,
      assignEmpBoList: selectedPersons.value.map(person => ({
        empCode: person.code,
        empName: person.name
      })),
      tagList: selectedTags.value.map(tagValue => {
        const tag = tagList.value.find(t => t.value === tagValue);
        return {
          tagCode: tagValue,
          tagName: tag ? tag.label : tagValue
        };
      }),
      ossIdList: uploadFiles.value.map(file => file.ossId), // 包含所有类型的图片
      drugSocreList: drugScoreList.value.map(category => ({
        scoreItemName: category.scoreItemName,
        // 包含分类级别的图片信息
        ossIdList: category.images ? category.images.map(img => (img.ossId)) : [],
        scoreItem: category.scoreItem.map(item => ({
          scorePointsTitle: item.scorePointsTitle,
          score: item.score || "0",
          inputScore: item.inputScore || null, // 使用用户输入的分数或默认为0
          // 包含评分项级别的图片信息
          itemImages: item.images ? item.images.map(img => ({
            ossId: img.ossId,
            url: img.url,
            name: img.name
          })) : []
        }))
      }))
    };

    console.log('保存的完整数据:', assignData);
    // 调用API分配DTP专员
    await assignDtpEmployee(assignData);
    ElMessage.success('保存指派成功');

    // 刷新数据
    if (operationStatus.value === 'DTP_EMP_COMPLETE' && applyButtonStatus.value === '1') {
      // 如果是管理员查看DTP专员提交的资料，重新获取汇总数据
      await getAssistantAllRecordData();
    } else {
      // 其他情况，刷新页面数据
      await getDetail();
    }
  } catch (error) {
    console.error('保存指派专员失败:', error);
    ElMessage.error(error.message || '保存指派专员失败');
  } finally {
    saveLoading.value = false;
  }
};

const getApplyStatus = async () => {
  const res = await getApplyStatusApi({
    flowId: route.query.instanceId
  })
  // 申请列表按钮展示状态 0:详情 1:管理员审批 2:DTP专员查看详情 3:DTP专员填写资料
  applyButtonStatus.value = res.data?.applyButtonStatus
  applyType.value = res.data?.applyType
}

const getOperationStatus = async () => {
  const res = await getOperationStatusApi(route.query.instanceId);
  operationStatus.value = res.data

}

// 显示拒绝对话框
const showRejectDialog = () => {
  rejectDialogVisible.value = true;
  rejectReason.value = '';
};

// 处理DTP专员接受或拒绝操作
const handleDtpAction = async (status) => {
  try {
    if (status === '2' && !rejectReason.value) {
      ElMessage.warning('请输入拒绝原因');
      return;
    }

    dtpActionLoading.value = true;
    const params = {
      flowId: route.query.instanceId,
      status: status
    };

    // 如果是拒绝操作，添加拒绝原因
    if (status === '2') {
      params.refuseReason = rejectReason.value;
    }

    // 调用DTP专员接受或拒绝接口
    await dtpEmployeeOperation(params);

    ElMessage.success(status === '1' ? '已同意接受任务' : '已拒绝任务');

    if (rejectDialogVisible.value) {
      rejectDialogVisible.value = false;
    }

    // 刷新页面状态
    await getOperationStatus();
    window.location.reload();
  } catch (error) {
    console.error('DTP专员操作失败:', error);
    ElMessage.error(error.message || 'DTP专员操作失败');
  } finally {
    dtpActionLoading.value = false;
  }
};

// 处理DTP专员提交资料
const handleDtpSubmit = async () => {
  try {
    // 验证是否已上传必要资料
    if (uploadFiles.value.length === 0) {
      ElMessage.warning('请先上传相关资料');
      return;
    }

    dtpActionLoading.value = true;

    // 收集所有图片ossId
    const categoryOssIds = [];
    drugScoreList.value.forEach(category => {
      if (category.images && category.images.length > 0) {
        category.images.forEach(image => {
          categoryOssIds.push(image.ossId);
        });
      }
    });

    const scoreItemOssIds = [];
    drugScoreList.value.forEach(category => {
      category.scoreItem.forEach(item => {
        if (item.images && item.images.length > 0) {
          item.images.forEach(image => {
            scoreItemOssIds.push(image.ossId);
          });
        }
      });
    });

    const allOssIds = [
      ...uploadFiles.value.map(file => file.ossId),
      ...categoryOssIds,
      ...scoreItemOssIds
    ];

    // 准备提交的数据
    const params = {
      flowId: route.query.instanceId,
      status: '4', // 4表示提交
      tagList: selectedTags.value.map(tagValue => {
        const tag = tagList.value.find(t => t.value === tagValue);
        return {
          tagCode: tagValue,
          tagName: tag ? tag.label : tagValue
        };
      }),
      ossIdList: uploadFiles.value.map(file => file.ossId),
      drugSocreList: drugScoreList.value.map(category => ({
        scoreItemName: category.scoreItemName,
        ossIdList: category.images ? category.images.map(img => (img.ossId)) : [],
        scoreItem: category.scoreItem.map(item => ({
          scorePointsTitle: item.scorePointsTitle,
          score: item.score,
          inputScore: item.inputScore || null, // 使用用户输入的分数或默认为0
        }))
      }))
    };

    // 调用DTP专员提交资料接口
    await dtpEmployeeWrite(params);

    ElMessage.success('资料提交成功');

    // 刷新页面状态
    await init();
    router.go(-1);
  } catch (error) {
    console.error('DTP专员提交资料失败:', error);
    ElMessage.error(error.message || 'DTP专员提交资料失败');
  } finally {
    dtpActionLoading.value = false;
  }
};

// 处理DTP专员保存资料（不提交）
const handleDtpSave = async () => {
  try {
    dtpActionLoading.value = true;

    // 准备保存的数据
    const params = {
      flowId: route.query.instanceId,
      status: '3', // 3表示保存不提交
      tagList: selectedTags.value.map(tagValue => {
        const tag = tagList.value.find(t => t.value === tagValue);
        return {
          tagCode: tagValue,
          tagName: tag ? tag.label : tagValue
        };
      }),
      ossIdList: uploadFiles.value.map(file => file.ossId),
      drugSocreList: drugScoreList.value.map(category => ({
        scoreItemName: category.scoreItemName,
        scoreItem: category.scoreItem.map(item => ({
          score: item.score || null,
          inputScore: item.inputScore || null // 使用用户输入的分数或默认为0
        }))
      }))
    };

    // 调用DTP专员保存资料接口
    await dtpEmployeeWrite(params);

    ElMessage.success('资料保存成功');

    // 刷新页面状态，但不跳转回列表页
    await init();
  } catch (error) {
    console.error('DTP专员保存资料失败:', error);
    ElMessage.error(error.message || 'DTP专员保存资料失败');
  } finally {
    dtpActionLoading.value = false;
  }
};

// 获取DTP专员已填写的资料信息
const getAssistantRecordData = async () => {
  try {
    loading.value = true;
    const res = await getAssistantRecord(route.query.instanceId);

    if (res.code === 200 && res.data) {
      console.log('DTP专员资料信息:', res.data);

      // 如果有标签数据，设置标签状态
      if (res.data.tagList && res.data.tagList.length > 0) {
        selectedTags.value = res.data.tagList.map((tag) => tag.tagCode || tag);
      }

      // 如果有文件数据，设置文件列表
      if (res.data.ossIdList && res.data.ossIdList.length > 0) {
        // 转换为文件对象格式
        // 注意：这里只有ossId，实际URL需要另外获取或处理
        const filePromises = res.data.ossIdList.map(async (ossId) => {
          try {
            const fileInfo = await getFileInfo(ossId);
            if (fileInfo?.data?.rows && fileInfo.data.rows[0]) {
              return fileInfo.data.rows[0];
            }
            return null;
          } catch (error) {
            console.error('获取文件信息失败:', error);
            return null;
          }
        });

        const files = await Promise.all(filePromises);
        uploadFiles.value = files.filter(Boolean);
      }

      // 如果有评分数据，设置评分列表并获取图片详情
      if (res.data.drugSocreList && res.data.drugSocreList.length > 0) {
        // 处理每个分类的图片数据
        const processedScoreList = await Promise.all(
          res.data.drugSocreList.map(async (category) => {
            const processedCategory = { ...category };

            // 处理分类级别的图片
            if (category.ossIdList && category.ossIdList.length > 0) {
              const categoryFilePromises = category.ossIdList.map(async (ossId) => {
                try {
                  const fileInfo = await getFileInfo(ossId);
                  if (fileInfo?.data?.rows && fileInfo.data.rows[0]) {
                    return fileInfo.data.rows[0];
                  }
                  return null;
                } catch (error) {
                  console.error('获取分类图片信息失败:', error);
                  return null;
                }
              });

              const categoryFiles = await Promise.all(categoryFilePromises);
              processedCategory.images = categoryFiles.filter(Boolean);
            } else {
              processedCategory.images = [];
            }

            return processedCategory;
          })
        );

        drugScoreList.value = processedScoreList;
      }
    }
  } catch (error) {
    console.error('获取DTP专员资料信息失败:', error);
    ElMessage.error('获取DTP专员资料信息失败');
  } finally {
    loading.value = false;
  }
};

// 商务和渠道负责人查看DTP专员填写的资料信息
const getAssistantAllRecordData = async () => {
  try {
    loading.value = true;
    const res = await getAllAssistantRecords(route.query.instanceId);

    if (res.code === 200 && res.data) {
      console.log('DTP专员填写的资料信息(商务/渠道负责人视图):', res.data);

      // 保存完整的响应数据
      assistantAllRecordData.value = res.data;

      // 如果有标签数据，设置标签状态
      if (res.data.tagList && res.data.tagList.length > 0) {
        selectedTags.value = res.data.tagList.map(tag => tag.tagCode || tag);
      }

      // 如果有文件数据，设置文件列表
      if (res.data.ossIdList && res.data.ossIdList.length > 0) {
        const filePromises = res.data.ossIdList.map(async (ossId) => {
          try {
            const fileInfo = await getFileInfo(ossId);
            if (fileInfo?.data?.rows && fileInfo.data.rows[0]) {
              return fileInfo.data.rows[0];
            }
            return null;
          } catch (error) {
            console.error('获取文件信息失败:', error);
            return null;
          }
        });

        const files = await Promise.all(filePromises);
        uploadFiles.value = files.filter(Boolean);
      }

      // 如果有评分数据，设置评分列表并获取图片详情
      if (res.data.drugSocreList && res.data.drugSocreList.length > 0) {
        // 处理每个分类的图片数据
        const processedScoreList = await Promise.all(
          res.data.drugSocreList.map(async (category) => {
            const processedCategory = { ...category };

            // 处理分类级别的图片
            if (category.ossIdList && category.ossIdList.length > 0) {
              const categoryFilePromises = category.ossIdList.map(async (ossId) => {
                try {
                  const fileInfo = await getFileInfo(ossId);
                  if (fileInfo?.data?.rows && fileInfo.data.rows[0]) {
                    return fileInfo.data.rows[0];
                  }
                  return null;
                } catch (error) {
                  console.error('获取分类图片信息失败:', error);
                  return null;
                }
              });

              const categoryFiles = await Promise.all(categoryFilePromises);
              processedCategory.images = categoryFiles.filter(Boolean);
            } else {
              processedCategory.images = [];
            }

            return processedCategory;
          })
        );

        drugScoreList.value = processedScoreList;
      }

    }
  } catch (error) {
    console.error('获取DTP专员资料信息失败:', error);
    ElMessage.error('获取DTP专员资料信息失败');
  } finally {
    loading.value = false;
  }
};

const getBpmRole = async () => {
  const res = await bpmRoleApi(route.query.instanceId);
  bpmRole.value = res.data
}

// 获取历史审批记录
const getHistoryRecords = async () => {
  try {
    loading.value = true;
    const res = await getAssignHistory(detail.value?.variable?.dsCode);

    if (res.code === 200 && res.data) {
      console.log('历史审批记录:', res.data);
      historyRecords.value = res.data || [];
    }
  } catch (error) {
    console.error('获取历史审批记录失败:', error);
    ElMessage.error('获取历史审批记录失败');
  } finally {
    loading.value = false;
  }
};

const init = async () => {
  try {
    loading.value = true;

    // 先获取基本信息
    await getBpmRole(); // 获取当前登录人在dtp审批流里的角色
    await getDetail();
    await getAction();
    await fetchTagDict();
    await getApplyStatus();
    await getOperationStatus();

    // 二审和三审角色需要展示历史记录
    if(bpmRole.value === 'SECOND_AUDIT' || bpmRole.value === 'THIRD_AUDIT') {
      await getHistoryRecords();
      await getAssistantAllRecordData();
    }

    if(bpmRole.value === 'DTP_AUDIT') {
      await getAssistantRecordData();
    }
  } catch (error) {
    console.error('初始化数据失败:', error);
    ElMessage.error('加载数据失败');
  } finally {
    loading.value = false;
  }
}

// 管理员同意审核
const handleMgrApprove = async (status) => {
  try {
    mgrActionLoading.value = true;

    // 准备提交的数据
    const params = {
      flowId: route.query.instanceId,
      status: status, // 1表示接受
      tagList: selectedTags.value.map(tagValue => {
        const tag = tagList.value.find(t => t.value === tagValue);
        return {
          tagCode: tagValue,
          tagName: tag ? tag.label : tagValue
        };
      })
    };

    // 调用渠道负责人补充标签API
    await channelManagerSupplementTag(params);
    await approveHandle('agree');
  } catch (error) {
    console.error('渠道负责人审核失败:', error);
    ElMessage.error(error.message || '审核操作失败');
  } finally {
    mgrActionLoading.value = false;
  }
};

onMounted(() => {
  init()
})
</script>
<style lang="scss" scoped>
.approve {
  font-family: PingFang SC, PingFang SC;
  display: flex;
  height: calc(100vh);

  .left {
    flex: 1;
    background: #fff;
    margin-right: 16px;
    padding: 16px;
    overflow-y: auto;

    .meun {
      margin-top: 8px;
    }
  }

  .middle {
    flex: 1;
    background: #fff;
    margin-left: 16px;
    padding: 16px;

    .filter {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .content {
      margin-top: 8px;
      border-radius: 4px;
      border: 1px solid #E5E6EB;
      padding: 12px;
      font-size: 14px;

      .params {
        margin-top: 8px;
      }

      .user-info {
        margin-top: 8px;
      }
    }
  }

  .right {
    position: relative;
    min-width: 375px;
    background: #fff;
    padding: 16px;
    overflow-y: auto;

    .top {
      padding: 9px 16px;
      font-size: 13px;
    }

    .approve-info {
      padding: 16px;
      font-size: 14px;

      .approve-info-name {
        display: flex;
        align-items: center;
        margin: 8px 0;
      }
    }

    .footerButton {
      border-top: 1px solid #E5E6EB;
      width: 100%;
      position: absolute;
      bottom: 0;
      height: 60px;
      line-height: 60px;
      padding-right: 40px;
    }
  }

  .avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    vertical-align: middle;
  }

  .nameAvatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #92a8f8;
    color: #fff;
    line-height: 24px;
    text-align: center;
    min-width: 24px;
    margin-right: 4px;
  }

  .title {
    display: flex;
    align-items: center;

    .title-text {
      font-weight: 600;
      font-size: 16px;
      color: #1D212B;
      margin-right: 8px;
    }
  }

  .color-86909C {
    color: #86909C;
  }

  .color-4E595E {
    color: #4E595E;
  }

  :deep(.el-menu) {
    border-right: none;
  }

  :deep(.el-menu-item) {
    padding: 0 8px !important;
    margin: 0;
  }
}
</style>
