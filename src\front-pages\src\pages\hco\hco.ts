/**
 * HCO管理API接口
 *
 * 基于API文档 api.md 生成
 * 包含HCO（Healthcare Organization）管理相关的所有接口
 *
 * <AUTHOR> from api.md
 * @date 2024-01-XX
 */

import request from '@/utils/request';
import { AxiosPromise } from 'axios';

// ==================== 类型定义 ====================

/**
 * 自定义表头VO
 */
export interface CustomizeHeaderVo {
  /** 表头 */
  header: string;
  /** 排序 */
  index: string;
  /** 是否冻结 */
  isFrozen: string;
}

/**
 * 机构标签VO
 */
export interface InstitutionTagVo {
  /** BU名称 */
  bu: string;
  /** 标签类型 */
  tagType: string;
  /** 标签值列表 */
  tagValue: string[];
}

/**
 * MDM客户VO
 */
export interface MdmCustomerVo {
  /** wbid */
  wbId: string;
  /** wb编码 */
  wbCode: string;
  /** 主数据编码 */
  mdmCode: string;
  /** 客户名称 */
  name: string;
  /** 性别 */
  sex: string;
  /** 年龄 */
  age: string;
  /** 专业技术职称 */
  professionTechTitle: string;
  /** 教学职称 */
  teachTitle: string;
  /** 客户级别 */
  level: string;
  /** 客户类型 */
  customerType: string;
  /** 职务 */
  job: string;
  /** 是否主职业点 */
  isMain: string;
  /** 是否启用 */
  status: string;
}

/**
 * MDM机构VO
 */
export interface MdmInstitutionVo {
  /** 机构id */
  wbId: string;
  /** 主数据编码 */
  mdmCode: string;
  /** 机构名称 */
  insName: string;
  /** 机构类别 */
  insType: string;
  /** 机构级别 */
  insGrade: string;
  /** 机构等次 */
  insLevel: string;
  /** 省 */
  provinceName: string;
  /** 市 */
  cityName: string;
  /** 区 */
  districtName: string;
  /** 地址 */
  address: string;
  /** 倍通编码 */
  btCode: string;
  /** sap编码 */
  sapCode: string;
  /** u8编码 */
  u8Code: string;
  /** bu归属标签 */
  institutionTagVos: InstitutionTagVo[];
  /** 经度 */
  longitude: string;
  /** 纬度 */
  latitude: string;
  /** 校准后经度 */
  calibrateLongitude: string;
  /** 校准后纬度 */
  calibrateLatitude: string;
  /** 机构商业名称 */
  businessName: string;
  /** 机构别名 */
  nameAlias: string;
  /** 分类编码 */
  categoryCode: string;
  /** 分类名称 */
  categoryName: string;
  /** 经济类型 */
  economicType: string;
  /** 统一社会信用代码 */
  socialCreditCode: string;
  /** 经营许可证名称 */
  businessPermitName: string;
  /** 营业执照名称 */
  businessLicenseName: string;
  /** 营业期限 */
  businessTerm: string;
  /** 法人 */
  legalPerson: string;
  /** 注册省份 */
  registeredProvince: string;
  /** 注册城市 */
  registeredCity: string;
  /** 注册区县 */
  registeredCounty: string;
  /** 详细注册地址 */
  registeredAddress: string;
  /** 完整注册地址 */
  fullRegisteredAddress: string;
  /** 标准科室 */
  insDept: string[];
  /** 证照信息 */
  licensing: string[];
  /** 现场照片 */
  onSitePhotos: string[];
}

/**
 * 表格分页数据对象
 */
export interface TableDataInfo<T> {
  /** 总记录数 */
  total: number;
  /** 列表数据 */
  rows: T[];
  /** 消息状态码 */
  code: number;
  /** 消息内容 */
  msg: string;
}

/**
 * 响应信息主体
 */
export interface ResponseResult<T = any> {
  /** 消息状态码 */
  code: number;
  /** 消息内容 */
  msg: string;
  /** 数据对象 */
  data: T;
}

/**
 * 查询条件 (对应API文档中的QueryCondition)
 */
export interface QueryCondition {
  /** 字段名 */
  field?: string;
  /** 操作符: eq, neq, contains, not_contains, empty, not_empty */
  operator?: string;
  /** 右值 */
  value?: string;
}

/**
 * 机构查询请求 (对应API文档中的InstitutionQueryRequest)
 */
export interface InstitutionQueryRequest {
  /** 条件数组 */
  conditions?: QueryCondition[];
  /** 条件逻辑"and" 或 "or" */
  logic?: string;
  /** 分页数 */
  pageNum?: number;
  /** 分页大小 */
  pageSize?: number;
}

/**
 * HCO分页查询参数 (简化的接口，用于组件内部)
 */
export interface HcoPageQuery {
  /** 查询条件数组 */
  conditions?: QueryCondition[];
  /** 条件逻辑 */
  logic?: string;
  /** 分页大小 */
  pageSize?: number;
  /** 当前页数 */
  pageNum?: number;

  status?: string;
}

/**
 * HCP分页查询参数
 */
export interface HcpPageQuery {
  /** 机构编码 */
  insCode: string;
  /** 分页大小 */
  pageSize?: number;
  /** 当前页数 */
  pageNum?: number;
  /** 排序列 */
  orderByColumn?: string;
  /** 排序的方向desc或者asc */
  isAsc?: string;
}

// ==================== HCO管理API接口 ====================

/**
 * 自定义表头
 * @param data 自定义表头数据
 */
export const customizeHeader = (data: CustomizeHeaderVo[]): AxiosPromise<ResponseResult<void>> => {
  return request({
    url: '/data/institution/customize-header',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json'
    }
  });
};

/**
 * HCP分页列表查询
 * @param params 查询参数
 */
export const queryByIns = (params: HcpPageQuery): AxiosPromise<TableDataInfo<MdmCustomerVo>> => {
  return request({
    url: '/data/customer/by-ins',
    method: 'post',
    params
  });
};

/**
 * 机构标签查询
 * @param insCode 机构编码
 */
export const insTagQuery = (insCode: string): AxiosPromise<string[]> => {
  return request({
    url: '/data/institution/tag',
    method: 'get',
    params: { insCode }
  });
};

/**
 * 获取BU归属下拉框选项
 * 调用 /data/institution/tag 接口获取BU归属标签数据
 */
export const getBuOptions = (): AxiosPromise<ResponseResult<InstitutionTagVo[]>> => {
  return request({
    url: '/data/institution/tag',
    method: 'get',
    params: { tagType: 'BU标签' }
  });
};

/**
 * HCO分页列表查询（支持自定义条件组）
 * @param params 查询参数
 */
export const hcoPageQuery = (params: HcoPageQuery): AxiosPromise<TableDataInfo<MdmInstitutionVo>> => {
  const requestData: InstitutionQueryRequest = {
    conditions: params.conditions || [],
    logic: params.logic || 'and',
    pageNum: params.pageNum || 1,
    pageSize: params.pageSize || 20
  };

  return request({
    url: '/data/institution/page',
    method: 'post',
    data: requestData
  });
};

/**
 * 机构/药店证照查询
 * @param insCode 机构编码
 */
export const insLicenseQuery = (insCode: string): AxiosPromise<string[]> => {
  return request({
    url: '/data/institution/license',
    method: 'get',
    params: { insCode }
  });
};

/**
 * 自定义查询表头
 */
export const headerQuery = (): AxiosPromise<ResponseResult<CustomizeHeaderVo[]>> => {
  return request({
    url: '/data/institution/header-query',
    method: 'get'
  });
};

/**
 * 标准科室查询
 * @param insCode 机构编码
 */
export const insDeptQuery = (insCode: string): AxiosPromise<string[]> => {
  return request({
    url: '/data/institution/dept',
    method: 'get',
    params: { insCode }
  });
};

/**
 * 机构类别查询
 * 获取所有可用的机构类别选项
 */
export const getInstitutionTypes = (): AxiosPromise<ResponseResult<string[]>> => {
  return request({
    url: '/data/institution/type',
    method: 'get'
  });
};

/**
 * HCO详情查询
 * @param insCode 机构编码
 */
export const queryDetail = (insCode: string): AxiosPromise<ResponseResult<MdmInstitutionVo>> => {
  return request({
    url: '/data/institution/detail',
    method: 'get',
    params: { insCode }
  });
};

export const exportURLData = (data: object): AxiosPromise<any> => {
  return request({
    url: `/data/imp-task/download`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
};

export const district = (): AxiosPromise<any> => {
  return request({
    url: `/mdm/hcd/district`,
    method: 'get'
  });
};
