.el-divider--horizontal {
  margin-bottom: 10px;
  margin-top: 10px;
}

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type='file'] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.el-input__wrapper,
.el-textarea__inner {
  background: var(--neutral-color-bg-f-5, #f5f5f5) !important;
  width: 240px;
}

.el-input__count {
  background: var(--neutral-color-bg-f-5, #f5f5f5) !important;
}

.el-input__wrapper.is-focus {
  background: #ffffff !important;
}

.el-range-editor.is-active,
.el-textarea__inner:focus,
.el-textarea__inner:focus + span {
  background: #ffffff !important;
}

// .el-textarea__inner:focus{
//   background:  #FFFFFF !important;
// }
// .el-textarea__inner:focus + span{
//   background:  #FFFFFF !important;
// }
.el-input,
.el-date-editor,
.el-textarea {
  --el-input-border-color: var(--neutral-color-bg-f-5, #f5f5f5) !important;
  --el-input-hover-border-color: #f5f5f5 !important;
}

.el-select__wrapper {
  background: var(--neutral-color-bg-f-5, #f5f5f5) !important;
  box-shadow: none !important;
}
.el-select {
  --el-select-border-color-hover: var(--neutral-color-bg-f-5, #f5f5f5) !important;
  width: 240px !important;
}

//  .el-cascader-menu__list{
//     width: 180px !important;
//  }

.is-disabled {
  border: none;

  .el-input__wrapper {
    background: var(--neutral-color-2, #f3f4f5) !important;
    border: 0px solid #f3f4f5;

    .el-input__inner {
      color: var(--neutral-color-4, #c9cdd0) !important;
      -webkit-text-fill-color: var(--neutral-color-4, #c9cdd0) !important;
      border: none;
    }
  }
}

.el-input.is-disabled .el-input__wrapper {
  box-shadow: 0 0 0 1px #f3f4f5 inset !important;
}

.el-dialog {
  .el-input.el-input--default.el-input--prefix.el-input--suffix.el-date-editor {
    width: 100%;
  }

  .el-input__wrapper {
    // width: 205px;
    width: 100%;
  }
}

.el-picker-panel__body {
  .el-input__wrapper {
    width: 100%;
  }
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

/*-------------Dialog-------------**/
.el-overlay {
  overflow: hidden;

  .el-overlay-dialog {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;

    .el-dialog {
      margin: 0 auto !important;

      .el-dialog__body {
        padding: 15px !important;
      }
    }
  }
}

.el-dialog__body {
  max-height: calc(90vh - 111px) !important;
  overflow-y: auto;
  overflow-x: hidden;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block;
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
  border: none;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

.el-menu--collapse > div > .el-submenu > .el-submenu__title .el-submenu__icon-arrow {
  display: none;
}

.el-dropdown .el-dropdown-link {
  color: var(--el-color-primary) !important;
}

.el-card {
  border-style: none !important;
  border-radius: 0 !important;

  .el-card__body {
    padding: 0 !important;
  }

  .el-card__header {
    // border-bottom: none;
    padding: 16px !important;
  }
}

.el-card.is-hover-shadow:hover {
  box-shadow: none !important;
}

.el-tag.el-tag--danger {
  --el-tag-bg-color: var(--1, #feedec);
  --el-tag-hover-color: var(--el-color-danger);
  color: var(--6, #f54a45);
}

.el-tag.el-tag--warning {
  --el-tag-bg-color: var(--1, #fff3e6);
  --el-tag-hover-color: var(--el-color-warning);
  color: var(--6, #f80);
}

.el-tag.el-tag--default.jzy {
  --el-tag-bg-color: var(--1, #fffce8);
  --el-tag-hover-color: var(--el-color-warning);
  color: var(--6, #f7ba1e);
}

.el-tag.el-tag--default.jzy-tag-cyan {
  --el-tag-bg-color: var(--1, #e8fffb);
  --el-tag-hover-color: var(--1, #e8fffb);
  color: var(--5, #33d1c9);
}

.el-tag.el-tag--default.jzy-tag-purple {
  --el-tag-bg-color: #f5e8ff;
  --el-tag-hover-color: #f5e8ff;
  color: #722ed1;
}

.el-tag.el-tag--default.jzy-tag-danger {
  --el-tag-bg-color: #ffe8f1;
  --el-tag-hover-color: #ffe8f1;
  color: #f5319d;
}

.el-tag.el-tag--default.jzy-tag-primary {
  --el-tag-bg-color: #e8f7ff;
  --el-tag-hover-color: #e8f7ff;
  color: #3491fa;
}

.el-tag.el-tag--success {
  --el-tag-bg-color: var(--1, #ebf9e9);
  --el-tag-hover-color: var(--el-color-success);
  color: var(--6, #34c724);
}

.el-tag.el-tag--info {
  --el-tag-bg-color: var(--1, #f3f4f5);
  color: var(--6, #4e595e);
}

// 下拉框多选时右边的对号
.el-select-dropdown.is-multiple .el-select-dropdown__item.selected::after {
  display: none;
}

.el-tabs__nav-wrap::after {
  height: 1px !important;
  background-color: #e5e6eb !important;
}

.dialog {
  .el-dialog__header {
    color: var(--light-text-color-text-1, #1d2129) !important;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
    /* 150% */
    padding: 12px 16px;
    margin-bottom: 0;
    margin-right: 0;
    text-align: center !important;
    border-bottom: 1px solid var(--neutral-color-line-2, #e5e6eb);
  }

  .el-dialog__close-btn {
    color: #4e5969;
    font-size: 16px;
  }

  .el-dialog__body {
    padding: 12px 16px;
  }
}

.el-message-box__status.el-message-box-icon--warning {
  --el-messagebox-color: #ff8800 !important;
  color: #ff8800;
}

.el-input__suffix {
  .el-icon {
    color: #4e595e !important;
  }
}

.el-input-group__append {
  color: #1d212b !important;
}

.el-cascader .el-cascader__tags .el-tag {
  background: #fff;
}
