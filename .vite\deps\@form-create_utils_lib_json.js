import {
  type_default
} from "./chunk-XJIU6EXH.js";
import "./chunk-DFKQJ226.js";

// node_modules/@form-create/utils/lib/modify.js
function $set(target, field, value) {
  target[field] = value;
}

// node_modules/@form-create/utils/lib/deepextend.js
function deepExtend(origin, target = {}, mode) {
  let isArr = false;
  for (let key in target) {
    if (Object.prototype.hasOwnProperty.call(target, key)) {
      let clone = target[key];
      if ((isArr = Array.isArray(clone)) || type_default.Object(clone)) {
        let nst = origin[key] === void 0;
        if (isArr) {
          isArr = false;
          nst && $set(origin, key, []);
        } else if (clone._clone && mode !== void 0) {
          if (mode) {
            clone = clone.getRule();
            nst && $set(origin, key, {});
          } else {
            $set(origin, key, clone._clone());
            continue;
          }
        } else {
          nst && $set(origin, key, {});
        }
        origin[key] = deepExtend(origin[key], clone, mode);
      } else {
        $set(origin, key, clone);
        if (!type_default.Undef(clone)) {
          if (!type_default.Undef(clone.__json)) {
            origin[key].__json = clone.__json;
          }
          if (!type_default.Undef(clone.__origin)) {
            origin[key].__origin = clone.__origin;
          }
        }
      }
    }
  }
  return mode !== void 0 && Array.isArray(origin) ? origin.filter((v) => !v || !v.__ctrl) : origin;
}

// node_modules/@form-create/utils/lib/console.js
function format(type, msg, rule) {
  return `[form-create ${type}]: ${msg}` + (rule ? "\n\nrule: " + JSON.stringify(rule.getRule ? rule.getRule() : rule) : "");
}
function err(msg, rule) {
  console.error(format("err", msg, rule));
}

// node_modules/@form-create/utils/lib/json.js
var PREFIX = "[[FORM-CREATE-PREFIX-";
var SUFFIX = "-FORM-CREATE-SUFFIX]]";
var $T = "$FN:";
var $TX = "$FNX:";
var $ON = "$GLOBAL:";
var FUNCTION = "function";
function toJson(obj, space) {
  return JSON.stringify(deepExtend(Array.isArray(obj) ? [] : {}, obj, true), function(key, val) {
    if (val && val._isVue === true)
      return void 0;
    if (typeof val !== FUNCTION) {
      return val;
    }
    if (val.__json) {
      return val.__json;
    }
    if (val.__origin)
      val = val.__origin;
    if (val.__emit)
      return void 0;
    return PREFIX + val + SUFFIX;
  }, space);
}
function makeFn(fn) {
  return new Function("return " + fn)();
}
function parseFn(fn, mode) {
  if (fn && type_default.String(fn) && fn.length > 4) {
    let v = fn.trim();
    let flag = false;
    try {
      if (v.indexOf(SUFFIX) > 0 && v.indexOf(PREFIX) === 0) {
        v = v.replace(SUFFIX, "").replace(PREFIX, "");
        flag = true;
      } else if (v.indexOf($T) === 0) {
        v = v.replace($T, "");
        flag = true;
      } else if (v.indexOf($ON) === 0) {
        const name = v.replace($ON, "");
        v = function(...args) {
          const callback = args[0].api.getGlobalEvent(name);
          if (callback) {
            return callback.call(this, ...args);
          }
          return void 0;
        };
        v.__json = fn;
        v.__inject = true;
        return v;
      } else if (v.indexOf($TX) === 0) {
        v = makeFn("function($inject){" + v.replace($TX, "") + "}");
        v.__json = fn;
        v.__inject = true;
        return v;
      } else if (!mode && v.indexOf(FUNCTION) === 0 && v !== FUNCTION) {
        flag = true;
      }
      if (!flag)
        return fn;
      const val = makeFn(v.indexOf(FUNCTION) === -1 && v.indexOf("(") !== 0 ? FUNCTION + " " + v : v);
      val.__json = fn;
      return val;
    } catch (e) {
      err(`解析失败:${v}

err: ${e}`);
      return void 0;
    }
  }
  return fn;
}
function parseJson(json, mode) {
  return JSON.parse(json, function(k, v) {
    if (type_default.Undef(v) || !v.indexOf)
      return v;
    return parseFn(v, mode);
  });
}
export {
  parseFn,
  parseJson,
  toJson
};
//# sourceMappingURL=@form-create_utils_lib_json.js.map
