import request from '@/utils/request';
import { AxiosPromise } from 'axios';
export const institutionList = (query: any): AxiosPromise<any> => {
  return request({
    url: '/mdm/gs-institution/list',
    method: 'get',
    params: query
  });
};
export const editReplace = (id: any, query: any): AxiosPromise<any> => {
  return request({
    url: `/mdm/gs-institution/replace/${id}`,
    method: 'get',
    params: query
  });
};
