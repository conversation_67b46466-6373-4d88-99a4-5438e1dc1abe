import request from '@/utils/request';

export const formListApi = (data: any) => {
  return request({
    url: `/bpm/form/page`,
    method: 'get',
    params: data
  });
};

export const processListApi = (query: any) => {
  return request({
    url: `/bpm/workflow/process/page`,
    method: 'get',
    params: query
  });
};
export const bpmStartApi = (data: any) => {
  return request({
    url: `/hcd/test/bpm/start`,
    method: 'post',
    data
  });
};
