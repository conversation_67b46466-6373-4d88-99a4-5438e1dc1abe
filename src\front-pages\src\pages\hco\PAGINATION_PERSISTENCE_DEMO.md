# 分页信息持久化功能演示

## 功能说明

分页信息持久化功能确保用户在浏览 HCO 列表时，跳转到详情页面后返回时能够保持原来的分页状态，包括：

- **当前页码**：用户所在的页面位置
- **每页显示数量**：用户选择的每页显示条数（10、20、50、100）

## 使用场景演示

### 场景 1：基本分页持久化

1. **用户操作**：
   - 在 HCO 列表页面，用户翻到第 5 页
   - 设置每页显示 50 条记录
   - 点击某个机构名称进入详情页

2. **系统行为**：
   - 自动保存当前分页状态（第 5 页，每页 50 条）
   - 跳转到详情页面

3. **返回效果**：
   - 用户点击详情页的"返回"按钮
   - 自动回到列表页面的第 5 页
   - 每页仍然显示 50 条记录
   - 保持用户离开时的确切位置

### 场景 2：分页变化时的自动保存

1. **用户操作**：
   - 在列表页面设置查询条件并查询
   - 翻页到第 3 页
   - 改变每页显示数量为 100 条
   - 继续浏览到第 2 页

2. **系统行为**：
   - 每次分页变化都自动保存状态
   - 最终保存状态：第 2 页，每页 100 条

3. **跳转详情后返回**：
   - 自动恢复到第 2 页，每页 100 条的状态

### 场景 3：复合条件持久化

1. **用户操作**：
   - 设置 BU 归属为"BU1, BU2"
   - 设置机构类别为"医院"
   - 设置高级筛选：省份="北京"
   - 翻页到第 4 页，每页 20 条
   - 点击机构详情

2. **系统保存**：
   ```json
   {
     "basicQuery": {
       "bu": ["BU1", "BU2"],
       "type": ["医院"],
       "orgName": ""
     },
     "advancedFilters": [
       {"left": "provinceName", "op": "eq", "right": "北京"}
     ],
     "queryParams": {
       "pageSize": 20,
       "pageNum": 4,
       "conditions": [...],
       "logic": "and"
     }
   }
   ```

3. **返回效果**：
   - 所有查询条件完全恢复
   - 分页状态精确恢复到第 4 页，每页 20 条

## 技术实现要点

### 1. 分页事件处理

```javascript
// 自定义分页处理函数
function handlePageChange(page) {
  hcoList.handlePageChange(page);
  // 分页变化后保存当前状态
  hcoList.saveCurrentQueryState(queryParams.value, advancedFilters.value);
}

function handleSizeChange(size) {
  hcoList.handleSizeChange(size);
  // 分页大小变化后保存当前状态
  hcoList.saveCurrentQueryState(queryParams.value, advancedFilters.value);
}
```

### 2. 分页组件绑定

```vue
<el-pagination
  v-model:current-page="hcoList.queryParams.pageNum"
  v-model:page-size="hcoList.queryParams.pageSize"
  :page-sizes="[10, 20, 50, 100]"
  :total="hcoList.total.value"
  layout="total, sizes, prev, pager, next, jumper"
  @size-change="handleSizeChange"
  @current-change="handlePageChange"
/>
```

### 3. 状态恢复

```javascript
// 恢复查询参数（包括分页信息）
if (savedState.queryParams) {
  Object.assign(hcoList.queryParams, savedState.queryParams);
}
```

## 用户体验优势

### ✅ 无缝浏览体验
- 用户不会因为跳转详情而丢失浏览位置
- 减少重新翻页的操作成本
- 提高工作效率

### ✅ 智能状态管理
- 自动保存，无需用户手动操作
- 支持复杂的查询和分页组合
- 数据过期自动清理

### ✅ 一致性保证
- 分页状态与查询条件同步保存
- 恢复时保证数据的完整性和一致性
- 支持各种分页大小的切换

## 测试验证

可以使用 `test-persistence.html` 中的分页测试功能：

1. 点击"测试分页持久化"按钮
2. 系统会自动测试保存和恢复分页信息
3. 验证页码和每页大小是否正确恢复

## 注意事项

1. **数据有效期**：分页状态随查询条件一起保存，有效期 24 小时
2. **浏览器支持**：需要支持 localStorage 的现代浏览器
3. **状态同步**：分页变化会触发状态保存，确保实时同步
4. **重置清理**：点击重置按钮会清除所有保存的状态，包括分页信息

## 后续优化

1. **性能优化**：考虑分页变化的防抖处理
2. **用户提示**：添加状态恢复的视觉反馈
3. **配置选项**：允许用户选择是否启用分页持久化
4. **数据压缩**：对保存的数据进行压缩以节省存储空间
