<template>
  <div class="detailInformation">
    <div class="card-block-ins">
      <el-card shadow="hover" class="card-block-content">
        <div class="card-block-top">
          <el-row class="paddingH20">
            <el-form
              ref="queryForm"
              :label-position='_props.type=="add"?"top":"left" '
              :model="state.queryForms"
              lass="query-form-ref"
              label-width="110px"
              :rules="state.rules"
              style="width: 100%;padding-top:30px"
            >
              <el-row :gutter="20">
                <el-col :span="formStyle">
                  <el-form-item label="省份" prop="pcdvalue" :class="{'edit-type':_props.type!='add'}">
                    <el-cascader
                      v-if="state.edit"
                      v-model="state.queryForms.pcdvalue"
                      clearable
                      :options="state.distList"
                      :props="props"
                      style="width: 100%;"
                      @change="handleChange"
                      disabled
                    />
                    <!-- <div v-else>
                      {{`${state.info.province}${state.info.city ? '/'+state.info.city:''}${state.info.district?'/'+state.info.district:''}`}}
                    </div> -->
                    <div v-else>
                      {{state.info.district}}
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="formStyle">
                  <el-form-item label="机构名称" :class="{'edit-type':_props.type!='add'}" prop="name">
                    <el-input
                      v-if="state.edit"
                      disabled
                      v-model="state.queryForms.name"
                      clearable
                      placeholder="请输入"
                      style="width: 100%;"
                    ></el-input>
                    <div v-else>{{state.info.name}}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="formStyle">
                  <el-form-item label="地址搜索" :class="{'edit-type':_props.type!='add'}">
                    <el-input
                      v-if="state.edit"
                      v-model="state.queryForms.searchAddress"
                      clearable
                      placeholder="请输入地址搜索"
                      style="width: 100%;"
                      @input="handleSearchInput"
                    >
                      <template #prefix>
                        <el-icon><Search /></el-icon>
                      </template>
                    </el-input>
                    <div v-else>{{state.info.searchAddress}}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item v-if="state.edit" label="" prop="" :class="{'edit-type':_props.type!='add'}">
                    <GdmapComponent @get:option="getOption" ref="mapRef" v-show="false" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-divider style="margin: 0;" />
              <el-radio-group v-model="state.queryForms.radio" class="grid" v-if="hostpelOptions?.length!=0">
                <el-radio @click="setLL(el)" class="paddingTB" v-for="(el,index) in hostpelOptions" :key="index" :label="el.id">
                  <template #default>
                    <span style="font-size:16px;font-weight:500;color:#303133">{{el.name}}</span>
                    <div style="color:#909399">{{el.address}}</div>
                  </template>
                </el-radio>
              </el-radio-group>
            </el-form>
          </el-row>
        </div>
      </el-card>
    </div>
  </div>
</template>
<script setup>
import {  editReplace } from '@/api/mdm/institution';
import { usedDstrictManagementStore } from '@/store/modules/districtManagement';
import { district } from './../hco';
import { inject } from 'vue';
import GdmapComponent from './gdmap.vue';
import { Search } from '@element-plus/icons-vue';
const props = {
  label: 'value',
  value: 'value',
  checkStrictly: true
};
const _props = defineProps({
  type: {
    type: String,
    required: false,
    default: ''
  }
});
const formStyle = computed(() => (_props.type == 'add' ? 24 : 6));
const mapRef = ref(null);
const hostpelOptions = ref([]);
const dstrictManagement = usedDstrictManagementStore();
const { proxy } = getCurrentInstance();
const queryForm = ref(null);
const hotpel = ref({});
const emit = defineEmits(['updateButton', 'butLoading']);
const quInit = {
  id:'',
  radio:'',
  calibrateLongitude:'',
  calibrateLatitude:'',
  pcdvalue: [],
  name: '',
  type: '',
  searchAddress: '',
};
const rules =
  _props.type == 'add'
    ? {
        name: [
          // { required: true, message: '必填', trigger: 'blur' },
          { max: 100, message: '输入最大值不超过100字符' }
        ],
        // pcdvalue: [{ required: true, message: '必选', trigger: 'change' }],
        radio: [{  message: '必选', trigger: 'change' }],

      }
    : {};
const state = reactive({
  queryForms: { ...quInit },
  distList: dstrictManagement.districtList,
  rules,
  edit: false,
  info: {}
});
const getOption = (option) => {
  console.log(option,"--option")
  hostpelOptions.value = option;
};
const setLL = ({location})=>{
  state.queryForms.calibrateLatitude=location.lat
  state.queryForms.calibrateLongitude=location.lng
  console.log(location,"-el")
}

const setInfo = (info) => {
    if (info.provinceName) {
     info.pcdvalue = [info.provinceName];
   }

   if (info.provinceName&&info.cityName) {
     info.pcdvalue = [info.provinceName,info.cityName];
   }

   if (info.districtName&&info.cityName&&info.provinceName) {
     info.pcdvalue = [info.provinceName,info.cityName,info.districtName];
   }
  state.info = JSON.parse(JSON.stringify(info));
  state.queryForms = JSON.parse(JSON.stringify(info));
  input(`${info.provinceName}${info.cityName}${info.districtName}${info.address}`)
};
const editStatus = () => {
  state.edit = true;
};

const handelCancel = () => {
  state.edit = false;
  state.queryForms = JSON.parse(JSON.stringify(state.info));
  state.queryForms.pcdvalue = [state.info.provinceCode, state.info.cityCode, state.info.districtCode];
};
const insDetailUpdate = async () => {
  if (!queryForm.value) return;
      if(!state.queryForms.calibrateLongitude||!state.queryForms.calibrateLatitude) return proxy?.$modal.msgError('地址未选择');

  await queryForm.value.validate(async (valid, fields) => {
    if (valid) {
      emit('butLoading', true);
      const params = {
       calibrateLongitude:state.queryForms.calibrateLongitude,
       calibrateLatitude:state.queryForms.calibrateLatitude
      };
      const res = await editReplace(state.queryForms.id,params);
      if (res.msg) proxy?.$modal.msgSuccess(res.msg);
      if (_props.type != 'add') state.edit = false;
      emit('updateButton');
      emit('butLoading', false);
    } else {
      console.log('error submit!', fields);
    }
  });
};
const handleChange = (value) => {
  if (!value) {
    state.queryForms.pcdvalue = [];
  }
  console.log(value,"--value")
  state.queryForms.province = value?.[0] || "";
  state.queryForms.city = value?.[1] || "";
  state.queryForms.district = value?.[2] || "";
  input(`${state.queryForms.province}${state.queryForms.city}${state.queryForms.district}${state.queryForms.address}`)

};
const input = (v) => {
  setTimeout(() => {
  mapRef.value.search(v);
  }, 300);
};
const resetFields = () => {
  state.queryForms = quInit;
  state.queryForms.annualTotalDrugIntake = '';
  queryForm.value.resetFields();
};
const infoJect = inject('info');
const handleSearchInput = (value) => {
  if (value) {
    input(value);
  }
};

const getDistrict = async () => {
  const res = await district();

  dstrictManagement.changeDistrictList(res.data);
  state.distList = res.data || [];
};

onMounted(async () => {
  getDistrict();
  if (_props.type == 'add') {
    editStatus();
    setInfo(infoJect.value)
    console.log(infoJect.value,"--infoJect")
  }
});
defineExpose({
  setInfo,
  editStatus,
  insDetailUpdate,
  handelCancel,
  resetFields
});
</script>
<style lang="scss" scoped>
.paddingTB{
  margin:10px 0
}
.grid{
  display:grid
}
.clor86909 {
  color: #86909c;
}
.paddingH20 {
  padding: 0 20px;
}
:deep(.edit-type .el-form-item__label) {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  color: #86909c !important;
}
:deep(.el-form-item__label) {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  color: #4e595e !important;
}
:deep(.el-input-group__append) {
  border-left: 1px #e5e6eb solid;
  width: fit-content;
  background-color: #f3f4f5;
  color: #1d212b;
}
.detailInformation {
  .top-title {
    color: #1d212b;
    font-weight: 600;
    margin-bottom: 10px;
    margin-top: 24px;
  }
  .list-item {
    background-color: #f3f4f5;
    margin-bottom: 8px;
    display: flex;
    .label {
      width: 80px;
      text-align: right;
    }
  }
}
</style>
