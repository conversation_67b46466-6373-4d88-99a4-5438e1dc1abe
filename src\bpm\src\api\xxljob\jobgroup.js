import request from '@/utils/request';
import { tansParams } from '@/utils/ruoyi';

export function jobgroupPage(data) {
  return request({
    url: '/sdr/job-group/pageList',
    method: 'post',
    data: tansParams(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8' }
  });
}

export function jobgroupSave(data) {
  return request({
    url: '/sdr/job-group/save',
    method: 'post',
    data,
    headers: { 'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8' }
  });
}

export function jobgroupUpdate(data) {
  return request({
    url: '/sdr/job-group/update',
    method: 'post',
    data: tansParams(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8' }
  });
}
export function jobgroupRemove(data) {
  return request({
    url: '/sdr/job-group/remove',
    method: 'post',
    data: tansParams(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8' }
  });
}

export function jobgroupLoadById(data) {
  return request({
    url: '/sdr/job-group/loadById',
    method: 'post',
    data: tansParams(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8' }
  });
}
