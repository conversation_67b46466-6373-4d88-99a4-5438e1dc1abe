import request from '@/utils/request';
import { AxiosPromise } from 'axios';
export const getProductList = (params: any): AxiosPromise<any> => {
  return request({
    url: `/mdm/product/list`,
    method: 'get',
    params
  });
};
export const getLogsApi = (params: any): AxiosPromise<any> => {
  return request({
    url: `/mdm/opr-log/query`,
    method: 'get',
    params
  });
};
export const getListApi = (params: any): AxiosPromise<any> => {
  return request({
    url: `/mdm/dept-product/list`,
    method: 'get',
    params
  });
};
export const addApi = (data: any): AxiosPromise<any> => {
  return request({
    url: `/mdm/dept-product/add`,
    method: 'post',
    data
  });
};
export const deleteApi = (ids: any): AxiosPromise<any> => {
  return request({
    url: `/mmdm/dept-product/${ids}`,
    method: 'delete'
  });
};
export const syncApi = (data: any): AxiosPromise<any> => {
  return request({
    url: `/mdm/dept-product/sync/jur   `,
    method: 'post',
    data
  });
};
