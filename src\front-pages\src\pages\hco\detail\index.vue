<template>
  <div class="insDetail">
    <div class="p-2">
      <div class="card-block">
        <el-card shadow="hover">
          <el-row :gutter="10">
            <div class="top">
              <div><img style="transform: translate(5px)" src="@/assets/images/ydicon.png" alt="" class="icons" /></div>
              <div class="top-right" style="margin-left: 5px">
                <div class="hospital">
                  {{state.info.insName}}
                  <el-tag type="primary" size="small" style="vertical-align:middle">{{state.info.insType}}</el-tag>
                </div>
                <div style="margin-top: 10px;font-size:14px;color:#86909C">
                  <span>{{state.info.wbId}}</span>
                </div>
              </div>
            </div>

            <div class="top-right-btn">
              <el-row :gutter="10">
                <el-col :span="1.5">
                  <el-button type="primary" text plain @click="router.back()">
                    <svg-icon icon-class="back" /><span style="padding-left: 8px;">返回</span></el-button
                  >
                </el-col>
              </el-row>
            </div>
          </el-row>
          <div style="margin-top: 16px;"><span style=" color: #86909C; font-weight: 400;">地区: </span>{{state.info.address}}</div>
        </el-card>
      </div>
    </div>
    <div class="p-2" style="display: flex">
      <div class="card-block" style="flex: 1">
        <el-card shadow="hover">
          <el-tabs v-model="state.activeName" class="demo-tabs" @tab-change="handleClick">
            <el-tab-pane label="详情信息" name="1">
              <DetailInformation ref="detailInformationRef" @updateButton="updateButton" />
            </el-tab-pane>
            <el-tab-pane label="标准科室" name="2">
              <StandardDepartmentTable />
            </el-tab-pane>
            <el-tab-pane label="标签" name="3">
              <EffectiveTags />
            </el-tab-pane>
            <el-tab-pane label="定位信息" name="4">
              <GdMap
                :search="state.info.address || state.info.insName"
                :institution-info="state.info"
                @location-updated="handleLocationUpdated"
                v-if="state.info.address || state.info.insName"
              />
            </el-tab-pane>
            <el-tab-pane label="证照信息" name="5">
              <LicenseInfo />
            </el-tab-pane>
            <el-tab-pane label="现场照片" name="6">
              <SitePhotos :editable="state.edit" />
            </el-tab-pane>
            <el-tab-pane label="商务备案" name="7">
              <BusinessRecord />
            </el-tab-pane>
            <el-tab-pane label="客户信息" name="8">
              <CustomerInfo :ins-ds-spec-code="state.info.insDsSpecCode" />
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </div>
    </div>
  </div>
</template>
<script setup>
import DetailInformation from './detailInformation';
import StandardDepartmentTable from './StandardDepartmentTable.vue';
import EffectiveTags from './EffectiveTags.vue';
import { usedDstrictManagementStore } from '@/store/modules/districtManagement';
import { queryDetail } from './../hco'
import GdMap from './GdMap.vue';
import LicenseInfo from './LicenseInfo.vue';
import SitePhotos from './SitePhotos.vue';
import BusinessRecord from './BusinessRecord.vue';
import CustomerInfo from './CustomerInfo.vue';

const { proxy } = getCurrentInstance();
const dstrictManagement = usedDstrictManagementStore();
const router = useRouter();
const route = useRoute();

const props = defineProps({
  isMdm: {
    type: Boolean,
    default: false
  }
})
const detailInformationRef = ref(null);

const state = reactive({
  activeName: '1',
  edit: false,
  id: dstrictManagement.insId,
  info: {},
  dict: [
    'ins_profession_type', //机构类型
    'ins_level' //机构等级
  ]
});
const recordRef = ref(null);

const handleClick = (v) => {
  if (v === '2') {
  }
  if (v == '3') recordRef.value.getList();
};
const formTargetAffiliatedPharmacy = computed(() => route.query?.srouce == 'targetAffiliatedPharmacy');

const getRelationDrugstoreDetail = async () => {
  let res = {};
    res = await queryDetail(route.query.insCode);
  state.info = res.data;
  detailInformationRef.value.setInfo(state.info);
};

const updateButton = () => {
  state.edit = false;
  getRelationDrugstoreDetail();
};

// 处理定位信息更新
const handleLocationUpdated = () => {
  // 重新获取机构详情，更新定位信息
  getRelationDrugstoreDetail();
};

onMounted(() => {
  getRelationDrugstoreDetail();
});
</script>
<style lang="scss" scoped>
.insDetail {
  font-size: 14px;
  .top {
    display: flex;
    .icons {
      display: inline-block;
      vertical-align: middle;
      width: 58px;
      height: 58px;
      margin-right: 8px;
    }
  }
  .top-right {
    .hospital {
      font-size: 18px;
      font-weight: 600;
    }
  }
  :deep(.el-tag) {
    border-width: 0;
  }
  :deep(.el-form-item__label) {
    color: #86909c;
  }
}
</style>
