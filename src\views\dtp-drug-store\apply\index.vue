<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import {
  getDrugstoreList,
  getDrugstoreDetail,
  addDrugstoreApplication,
  getJurisdictionProducts,
  getSpecsByProductApi
} from '@/api/dtp-drug-store'
import type {
  DtpJurProductVo,
  GsApplyBpmJurDsContext,
  ProductSpec,
  AeTaskAttachment,
  GsApplyBpmJurDsBo
} from '@/api/dtp-drug-store/types'
import { getFileInfo } from "@/bpm/src/api/flow";
import { useUserStore } from '@/store/modules/user'

const user: any = useUserStore()
const router = useRouter()
const isLoading = ref(false)
// 药店列表

const drugstoreOptions = ref<any[]>([])
const selectedDrugstoreId = ref('')
const info = ref<any>({
    applicant: user?.info?.nickName,
    applicantCode: user?.info?.userName,
    postCode: user?.info?.posts?.postCode,
    postName: user?.info?.posts?.postName,
    deptCode: user?.info?.dept?.deptCode,
    deptName: user?.info?.dept?.deptName,
    ancestors: user?.info?.dept?.ancestors,
    tenantId: import.meta.env.VITE_APP_TENANT_ID,
    enableWorkflow: true,
    appCode: import.meta.env.VITE_APP_CODE,
    postIdList: user?.info?.postIdList?.map((item: any) => item.postId)
})

// 药店信息类型
interface DrugstoreDetail {
  id?: string;
  code?: string;
  name?: string;
  province?: string;
  city?: string;
  address?: string;
  files?: AeTaskAttachment[];
}

// 表单数据
const formData = reactive<any>({
  name: '',
  code: '',
  province: '',
  city: '',
  address: '',
  files: []
})

// 产品列表
const productList = ref<ProductSpec[]>([])

// 产品选择相关数据
const productDialogVisible = ref(false)
const productOptions = ref<DtpJurProductVo[]>([])
const selectedProduct = ref('')

// 品规类型
interface SpecOption {
  code: string;
  name: string;
}

const productSpecOptions = ref<SpecOption[]>([])
const selectedSpecs = ref<string[]>([])

// 加载药店列表
async function loadDrugstores() {
  try {
    isLoading.value = true
    const res = await getDrugstoreList({ pageNum: 1, pageSize: 100 })
    if (res.code === 200 && res.data) {
      drugstoreOptions.value = res.data.rows || []
    }
  } catch (error) {
    console.error('获取药店列表失败', error)
    ElMessage.error('获取药店列表失败')
  } finally {
    isLoading.value = false
  }
}

// 加载药店详情
async function loadDrugstoreDetail(dsCode: string) {
  if (!dsCode) return

  try {
    isLoading.value = true
    const res = await getDrugstoreDetail({ dsCode })
    if (res.code === 200 && res.data) {
      const detail = res.data
      formData.dsCode = detail.dsCode || ''
      formData.dsName = detail.dsName || ''
      formData.dsMdmCode = detail.dsMdmCode || ''
      formData.province = detail.province || ''
      formData.city = detail.city || ''
      formData.address = detail.address || ''
      formData.files = detail.files || []

      if(detail.extendInfo) {
        try {
          formData.files = await refactorFileList(JSON.parse(detail.extendInfo)?.fileList && JSON.parse(JSON.parse(detail.extendInfo)?.fileList)) || [];
        } catch(e){console.log(e)}
      }
    }
  } catch (error) {
    console.error('获取药店详情失败', error)
    ElMessage.error('获取药店详情失败')
  } finally {
    isLoading.value = false
  }
}

// 加载辖区产品
async function loadProducts() {
  try {
    isLoading.value = true
    const res = await getJurisdictionProducts()
    if (res.code === 200 && res.data) {
      productOptions.value = res.data
    }
  } catch (error) {
    console.error('获取产品列表失败', error)
    ElMessage.error('获取产品列表失败')
  } finally {
    isLoading.value = false
  }
}

// 根据产品获取品规列表
async function getSpecsByProduct(productCode: string) {
  if (!productCode) return []

  try {
    isLoading.value = true

    // 调用 getDrugstoreProducts API 获取品规列表
    const res = await getSpecsByProductApi({
      level3Code: productCode
    })

    if (res.code === 200 && res.data) {
      // 转换API返回的数据为所需格式
      return res.data?.rows?.map(item => ({
        code: item.specCode,
        name: item.specName
      }))
    }
    return []
  } catch (error) {
    console.error('获取品规列表失败', error)
    ElMessage.error('获取品规列表失败')
    return []
  } finally {
    isLoading.value = false
  }
}

// 当选择药店时
function handleDrugstoreChange(dsCode: string) {
  if (dsCode) {
    const drugstore = drugstoreOptions.value.find(item => item.dsCode === dsCode)
    if (drugstore) {
      loadDrugstoreDetail(dsCode)
    }
  } else {
    // 清空表单
    formData.name = ''
    formData.code = ''
    formData.province = ''
    formData.city = ''
    formData.address = ''
    formData.files = []
  }
}

// 当选择产品时，更新品规列表
async function handleProductChange(productCode: string) {
  selectedSpecs.value = []
  productSpecOptions.value = await getSpecsByProduct(productCode)
}

// 添加新产品
function handleAddProduct() {
  selectedProduct.value = ''
  selectedSpecs.value = []
  productSpecOptions.value = []
  productDialogVisible.value = true
}

// 确认添加产品
function confirmAddProduct() {
  if (!selectedProduct.value) {
    ElMessage.warning('请选择产品')
    return
  }

  if (selectedSpecs.value.length === 0) {
    ElMessage.warning('请至少选择一个品规')
    return
  }

  // 获取选中的产品信息
  const product = productOptions.value.find(item => item.productCode === selectedProduct.value)
  if (!product) return

  // 将选中的品规添加到产品列表
  selectedSpecs.value.forEach(specCode => {
    const spec = productSpecOptions.value.find(item => item.code === specCode)
    if (spec) {
      // 检查是否已经添加过
      const exists = productList.value.some(item =>
        item.productCode === product.productCode && item.specCode === specCode
      )

      if (!exists) {
        productList.value.push({
          productCode: product.productCode,
          productName: product.productName,
          specCode: spec.code,
          specName: spec.name,
          mdmCode: '' // 如果需要可以补充
        })
      }
    }
  })

  productDialogVisible.value = false
  ElMessage.success('产品添加成功')
}

// 删除产品
function handleDeleteProduct(index: number) {
  ElMessageBox.confirm('确认删除该产品？', '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    productList.value.splice(index, 1)
    ElMessage.success('删除成功')
  }).catch(() => {})
}

// 返回上一页
function handleReturn() {
  router.back()
}

// 提交申请
async function handleSubmit() {
  if (!formData.dsName) {
    ElMessage.warning('请选择药店')
    return
  }

  if (productList.value.length === 0) {
    ElMessage.warning('请至少添加一个产品')
    return
  }

  try {
    const loading = ElLoading.service({
      lock: true,
      text: '提交中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    // 构建提交数据
    const applyContent: GsApplyBpmJurDsBo = {
      fileList: formData.files,
      dsCode: formData.dsCode || '',
      dsName: formData.dsName || '',
      dsMdmCode: formData.dsMdmCode || '',
      province: formData.province || '',
      city: formData.city || '',
      address: formData.address || '',
      specList: productList.value,
      applyType: ['dtpDsInsert'],
      // 需要根据实际情况补充其他必填字段

    }
    console.log(info.value, 'applyContent')
    const submitData: GsApplyBpmJurDsContext = {
      applyContent: applyContent,
      applyType: 'dtpDsInsert',
      dsCode: formData.code || '',
      dsName: formData.name || '',
      ...info.value
    }

    const res = await addDrugstoreApplication(submitData)
    loading.close()
    if(res.msg) {
      ElMessage.error(res.msg)
      return;
    }
    if (res.code === 200) {
      ElMessage.success('申请提交成功')
      router.back()
    } else {
      ElMessage.error(res.msg || '申请提交失败')
    }
  } catch (error) {
    console.error('提交申请失败', error)
    ElMessage.error('提交申请失败')
  }
}

const refactorFileList = async (data: any) => {
  const attUrlList = data || [];
  const _ = await Promise.all(attUrlList.map(async (d:any) => {
    const res = await getFileInfo(d.ossId);
    return res?.data?.rows && res.data.rows[0]
  }))
  return _;
}

onMounted(() => {
  loadDrugstores()
  loadProducts()
})
</script>

<template>
  <div class="p-2">
    <div class="card-block">
      <!-- 药店信息 -->
      <div class="mb-8">
        <h2 class="text-xl font-bold mb-4">药店信息</h2>

        <el-form :model="formData" label-width="120px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="药店名称" required>
                <el-select v-model="selectedDrugstoreId" placeholder="请选择药店" class="w-full" filterable @change="handleDrugstoreChange">
                  <el-option v-for="item in drugstoreOptions" :key="item.dsCode" :label="item.dsName" :value="item.dsCode" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="药店编码">
                <span class="text-gray-500">{{ formData.code || '系统自动带出' }}</span>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="省份">
                <span class="text-gray-500">{{ formData.province || '系统自动带出' }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="城市">
                <span class="text-gray-500">{{ formData.city || '系统自动带出' }}</span>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="地址">
                <span class="text-gray-500">{{ formData.address || '系统自动带出' }}</span>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="照片">
                <div v-if="formData.files && formData.files.length > 0">
                  <el-image
                    v-for="(file, index) in formData.files"
                    :key="index"
                    :src="file.url"
                    fit="cover"
                    class="mr-2 h-24 w-24 object-cover"
                    :preview-src-list="formData.files.map(f => f.url)"
                    :initial-index="index"
                  />
                </div>
                <div v-else class="text-gray-500">
                  <span>（营业执照、经营许可证、门头照）</span>
                  <span>系统自动带出</span>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 产品信息 -->
      <div>
        <h2 class="text-xl font-bold mb-4">产品信息</h2>

        <div class="mb-4">
          <el-button type="primary" plain class="border-dashed" @click="handleAddProduct"> 新增产品 </el-button>
        </div>

        <el-table :data="productList" border style="width: 100%" v-loading="isLoading">
          <el-table-column prop="productCode" label="产品编码" width="150" />
          <el-table-column prop="productName" label="产品" width="180" />
          <el-table-column prop="specCode" label="品规编码" width="180" />
          <el-table-column prop="specName" label="品规名称" />
          <el-table-column label="操作" width="120" align="center">
            <template #default="{ $index }">
              <el-button type="primary" link @click="handleDeleteProduct($index)"> 删除 </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 操作按钮 -->
      <div class="flex justify-center mt-8 space-x-4">
        <el-button type="primary" @click="handleSubmit" :loading="isLoading">提交申请</el-button>
        <el-button @click="handleReturn">返回</el-button>
      </div>
    </div>

    <!-- 产品选择弹窗 -->
    <el-dialog title="选择产品" v-model="productDialogVisible" width="600px">
      <el-form label-width="80px">
        <el-form-item label="产品" required>
          <el-select v-model="selectedProduct" placeholder="请选择产品" class="w-full" filterable @change="handleProductChange">
            <el-option v-for="item in productOptions" :key="item.productCode" :label="item.productName" :value="item.productCode" />
          </el-select>
        </el-form-item>

        <el-form-item label="品规" required>
          <el-select v-model="selectedSpecs" placeholder="请选择品规" multiple class="w-full" filterable :disabled="!selectedProduct">
            <el-option v-for="item in productSpecOptions" :key="item.code" :label="item.name" :value="item.code" />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="productDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmAddProduct">确认</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.el-table {
  margin-bottom: 20px;
}

.border-dashed {
  border-style: dashed;
}
</style>
