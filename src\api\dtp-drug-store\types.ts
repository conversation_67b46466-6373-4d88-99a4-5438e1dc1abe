// DTP药店API类型定义

// 文件附件类型
export interface AeTaskAttachment {
  id: string;
  ossId: string;
  url: string;
  aeFileId: string;
  size: string;
  mime_type: string;
  name: string;
}

// 药店评分点
export interface ScorePoint {
  scorePointsTitle: string;
  score: string;
  inputScore: string;
}

// 药店评分类别
export interface DrugScore {
  scoreItemName: string;
  scoreItem: ScorePoint[];
}

// 产品信息
export interface ProductSpec {
  mdmCode: string;
  productCode: string;
  productName: string;
  specCode: string;
  specName: string;
}

// API响应包装器
export interface ApiResponse<T> {
  code: number;
  msg: string;
  data: T;
}

// 分页参数
export interface PaginationParams {
  pageSize: number;
  pageNum: number;
  orderByColumn?: string;
  isAsc?: string;
}

// 分页响应
export interface TableDataInfo<T> {
  total: number;
  rows: T[];
  code: number;
  msg: string;
}

// 响应的分页信息
export interface EsPageInfo<T> {
  total: number;
  list: T[];
  pageNum: number;
  pageSize: number;
  size: number;
  startRow: number;
  endRow: number;
  pages: number;
  prePage: number;
  nextPage: number;
  hasPreviousPage: boolean;
  hasNextPage: boolean;
  navigatePages: number;
  navigatePageNums: number[];
  navigateFirstPage: number;
  navigateLastPage: number;
  firstPage: boolean;
  lastPage: boolean;
}

// DTP操作类型枚举
export enum JurisdictionDtpDrugstoreTypeEnum {
  INSERT = 'JURISDICTION_DTP_DRUGSTORE_INSERT',
  DELETED = 'JURISDICTION_DTP_DRUGSTORE_DELETED',
  PRODUCT_INSERT = 'JURISDICTION_DTP_DRUGSTORE_PRODUCT_INSERT',
  PRODUCT_DELETED = 'JURISDICTION_DTP_DRUGSTORE_PRODUCT_DELETED',
  WAIT_AUDITOR_SUBMIT = 'WAIT_AUDITOR_SUBMIT',
  DTP_EMP_COMPLETE = 'DTP_EMP_COMPLETE',
  DTP_EMP_OPERATION = 'DTP_EMP_OPERATION'
}

// DTP员工操作
export interface DtpEmpOperateBo {
  flowId: number;
  status: string;
  refuseReason?: string;
  ossIdList?: string[];
  tagList?: any[];
  drugSocreList?: DrugScore[];
}

// DTP药店信息
export interface DtpJurDrugVo {
  id: string;
  jurDsCode: string;
  drugMdmCode: string;
  drugName: string;
  drugProvince: string;
  drugCity: string;
  drugDistrict: string;
  lastTime: string;
  fileList: AeTaskAttachment[];
  drugCode: string;
  jurCode: string;
}

// 药店申请
export interface GsApplyBpmJurDsBo {
  createBy?: number;
  createTime?: string;
  updateBy?: number;
  updateTime?: string;
  params?: Record<string, any>;
  appName?: string;
  dsCode: string;
  dsName: string;
  dsMdmCode: string;
  province: string;
  provinceCode: string;
  city: string;
  cityCode: string;
  district: string;
  districtCode: string;
  specList?: ProductSpec[];
  createdEmpName?: string;
  fileList?: AeTaskAttachment[];
  jurCode: string;
  jurType: string;
  bu: string;
  applyType?: Array<string>;
  address?: string;
}

// BPM节点上下文
export interface CrmApplyBpmNodeContext {
  bpmNode: Record<string, string>;
}

// 带药店数据的申请上下文
export interface GsApplyBpmJurDsContext {
  appCode?: string;
  tenantId?: string;
  postIdList?: number[];
  enableWorkflow?: boolean;
  applyType: string;
  processId?: string;
  applyTime?: string;
  applicant: string;
  applicantCode: string;
  postCode: string;
  postName: string;
  deptCode: string;
  deptName: string;
  ancestors: string;
  applyContent: GsApplyBpmJurDsBo;
  appUrl?: string;
  pcUrl?: string;
  applyNodeInfo?: CrmApplyBpmNodeContext[];
  jurCode: string;
  dsCode: string;
  dsName: string;
  bu: string;
  jurDsCode?: string;
  dsMdmCode?: string;
}

// 药店产品查询参数
export interface GsJurDrugProductBo {
  jurDsCode: string;
  productCode?: string;
  specCode?: string;
  bu?: string;
  applicant?: string;
}

// 药店产品信息
export interface DtpJurDrugProductVo {
  jurDsCode: string;
  jurDsSpecCode: string;
  productCode: string;
  productName: string;
  specCode: string;
  specName: string;
  applicant: string;
  applicantCode: string;
  applicantDept: string;
  bu: string;
  ancestors: string;
  deptName: string;
}

// 药店查询参数
export interface GsJurDrugBo extends PaginationParams {
  keyword?: string;
  viewAdmin?: boolean;
  userId?: number;
}

// 助理记录
export interface AssistantWriteRecordVo {
  empCode: string;
  empName: string;
  ossIdList: string[];
  drugScore: DrugScore[];
}

// DTP助理管理视图
export interface DtpAssistantAdminVo {
  flowId: number;
  tagList: any[];
  scoreAvg: number;
  assistantWriteList: AssistantWriteRecordVo[];
}

// DTP助理填写记录
export interface DtpAssistantWriteRecordVo {
  flowId: number;
  status: string;
  refuseReason?: string;
  ossIdList?: string[];
  tagList?: any[];
  drugSocreList?: DrugScore[];
}

// 员工分配
export interface AssignEmpBo {
  empCode: string;
  empName: string;
}

// 药店申请分配
export interface GsDtpDrugApplyAssignEmpBo {
  flowId: number;
  assignEmpBoList: AssignEmpBo[];
}

// 辖区产品
export interface DtpJurProductVo {
  productCode: string;
  productName: string;
}

// 药店申请查询参数
export interface DtpDrugApplyPageBo extends PaginationParams {
  status?: string;
  applyType?: string;
  aeFlowId?: number;
  drugName?: string;
  source?: string;
  instanceStates?: string[];
  createBy?: number;
  createCode?: string;
}

// 药店申请信息
export interface DtpApplyVo {
  createBy?: number;
  createTime?: string;
  updateBy?: number;
  updateTime?: string;
  params?: Record<string, any>;
  id?: number;
  applyType: string;
  status: string;
  jurCode: string;
  dsCode: string;
  dsName: string;
  applyTime: string;
  applicant: string;
  applicantCode: string;
  postCode: string;
  postName: string;
  deptCode: string;
  deptName: string;
  ancestors: string;
  applyContent: string;
  applyNodeInfo: string;
  aeFlowId: number;
  cancelFlag?: string;
  cancelTime?: string;
  completeFlag?: string;
  completeTime?: string;
  bu: string;
  delFlag?: string;
  remark?: string;
  extendInfo?: string;
  applyId?: string;
  flowId?: string;
  dsMdmCode?: string;
  dsProvinceName?: string;
  dsCityName?: string;
  dsAreaName?: string;
  actorId?: number;
  dtpCode?: string;
  dtpStatus?: string;
  applyButtonStatus?: string;
  instanceState?: string;
}

// 产品查询参数
export interface ProductQueryParams {
  level3Code: string;
  [key: string]: any;
}

// 产品规格
export interface ProductSpec {
  mdmCode: string;
  productCode: string;
  productName: string;
  specCode: string;
  specName: string;
}

// DTP药店产品信息
export interface DtpJurDrugProductVo {
  productCode: string;
  productName: string;
  specCode: string;
  specName: string;
  applicant: string;
  hrCode: string;
  inDept: string;
  bu: string;
  [key: string]: any;
}

// DTP药店产品
export interface DtpJurProductVo {
  productCode: string;
  productName: string;
  [key: string]: any;
}

// DTP药店详情
export interface DtpJurDrugVo {
  drugCode: string;
  drugName: string;
  drugMdmCode: string;
  drugProvince: string;
  drugCity: string;
  drugDistrict?: string;
  address?: string;
  jurDsCode: string;
  jurCode?: string;
  fileList?: Array<{ url: string; [key: string]: any }>;
  [key: string]: any;
}
