# 页面卸载清除功能说明

## 功能概述

当用户从 HCO 列表页面跳转到其他页面（通过点击左侧菜单栏）时，系统会自动清除保存的查询条件和分页信息，确保下次访问 HCO 页面时是全新的状态。

## 使用场景

### 场景 1：菜单跳转清除

1. **用户操作**：

   - 在 HCO 列表页面设置查询条件
   - 翻页到第 3 页，每页 50 条
   - 点击左侧菜单栏的其他菜单项（如"用户管理"）

2. **系统行为**：

   - 自动清除 HCO 页面的所有持久化数据
   - 跳转到目标页面

3. **再次访问效果**：
   - 用户再次点击 HCO 菜单项
   - 页面显示默认状态（第 1 页，默认查询条件）
   - 不会保留之前的查询条件和分页状态

### 场景 2：详情页跳转保持 vs 菜单跳转清除

**详情页跳转（保持状态）：**

```
HCO列表页 → 机构详情页 → 返回HCO列表页
状态：保持查询条件和分页信息 ✅
```

**菜单跳转（清除状态）：**

```
HCO列表页 → 其他菜单页面 → 再次访问HCO列表页
状态：清除所有查询条件，恢复默认状态 ✅
```

## 技术实现

### 1. 组件内路由守卫

使用 `onBeforeRouteLeave` 路由守卫来智能判断跳转类型：

```javascript
import { onBeforeRouteLeave } from 'vue-router'

onBeforeRouteLeave((to) => {
  // 检查是否跳转到详情页
  if (to.path !== '/ins_mdm_manage/ins_mdm/detail') {
    // 如果不是跳转到详情页，则清除持久化数据
    hcoList.clearQueryState()
    console.log('离开HCO页面，已清除持久化查询条件')
  } else {
    console.log('跳转到详情页，保持持久化查询条件')
  }
})
```

### 2. 清除时机

`onBeforeRouteLeave` 路由守卫会在以下情况触发：

**会清除数据的情况：**

- ✅ 用户点击左侧菜单跳转到其他页面
- ✅ 用户直接在地址栏输入其他页面 URL
- ✅ 用户通过浏览器前进/后退按钮离开页面

**不会清除数据的情况：**

- ❌ 跳转到详情页面（`/ins_mdm_manage/ins_mdm/detail`）
- ❌ 分页操作（页面内操作）
- ❌ 查询操作（页面内操作）
- ❌ 页面刷新（不会触发路由守卫）

### 3. 清除的数据

清除操作会删除以下持久化数据：

```javascript
// 清除的localStorage数据
localStorage.removeItem('hco_query_conditions')
```

包含的数据结构：

- 基础查询条件（BU 归属、机构类别、机构名称）
- 高级筛选条件
- 分页信息（当前页码、每页大小）
- 查询参数和逻辑关系

## 用户体验

### 优势

1. **避免混淆**：

   - 不同页面之间的查询条件不会相互干扰
   - 用户每次访问 HCO 页面都是清晰的起点

2. **符合预期**：

   - 用户点击菜单项时，期望看到该页面的默认状态
   - 避免意外的查询条件影响用户判断

3. **数据清洁**：
   - 定期清理不需要的持久化数据
   - 避免 localStorage 积累过多数据

### 与详情页跳转的区别

| 跳转类型   | 触发方式     | 状态保持 | 用户期望             |
| ---------- | ------------ | -------- | -------------------- |
| 详情页跳转 | 点击机构名称 | ✅ 保持  | 返回时继续之前的浏览 |
| 菜单跳转   | 点击左侧菜单 | ❌ 清除  | 新的页面访问体验     |

## 测试验证

### 1. 手动测试

**测试步骤：**

1. 在 HCO 列表页面设置查询条件
2. 翻页到非第一页
3. 点击左侧菜单的其他项目
4. 再次点击 HCO 菜单项
5. **验证**：页面应显示默认状态

### 2. 自动化测试

使用 `test-persistence.html` 中的测试功能：

```javascript
// 测试页面卸载清除功能
function testPageUnloadClear() {
  // 1. 保存测试数据
  // 2. 验证数据已保存
  // 3. 模拟页面卸载清除
  // 4. 验证数据已清除
}
```

### 3. 控制台验证

在浏览器控制台中可以看到清除日志：

```
HCO页面卸载，已清除持久化查询条件
```

## 配置选项

### 当前实现

- 页面卸载时自动清除（无法配置）
- 清除所有持久化数据

### 未来可能的配置

```javascript
// 可能的配置选项
const config = {
  clearOnUnmount: true,           // 是否在页面卸载时清除
  clearOnMenuNavigation: true,    // 是否在菜单导航时清除
  preserveBasicQuery: false,      // 是否保留基础查询条件
  preservePagination: false       // 是否保留分页信息
}
```

## 故障排除

### 1. 数据没有被清除

**可能原因：**

- `onUnmounted` 钩子没有正确执行
- `clearQueryState()` 方法有问题
- localStorage 访问权限问题

**解决方法：**

- 检查浏览器控制台是否有错误信息
- 验证是否看到清除日志
- 手动检查 localStorage 状态

### 2. 清除时机不正确

**可能原因：**

- 路由配置问题
- 页面组件层级问题

**解决方法：**

- 检查路由配置是否正确
- 确认 HCO 页面组件的生命周期

### 3. 部分数据没有清除

**可能原因：**

- 存储键名不匹配
- 清除逻辑不完整

**解决方法：**

- 检查 `HCO_QUERY_STORAGE_KEY` 常量
- 验证 `clearQueryConditions()` 方法

## 注意事项

1. **浏览器兼容性**：依赖 localStorage API
2. **数据恢复**：清除后的数据无法恢复
3. **用户习惯**：需要用户适应新的行为模式
4. **开发调试**：开发时可能需要频繁重新设置查询条件

## 相关文档

- `QUERY_PERSISTENCE_GUIDE.md` - 查询条件持久化完整指南
- `PAGINATION_PERSISTENCE_DEMO.md` - 分页持久化演示
- `IMPLEMENTATION_SUMMARY.md` - 功能实现总结
- `test-persistence.html` - 功能测试页面
