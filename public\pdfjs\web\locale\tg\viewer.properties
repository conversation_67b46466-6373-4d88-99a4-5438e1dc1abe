# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=Саҳифаи қаблӣ

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.

zoom_out.title=Хурд кардан
zoom_out_label=<PERSON>у<PERSON>д кардан
zoom_in.title=Ка<PERSON>он кардан
zoom_in_label=Калон кардан
zoom.title=Танзими андоза
open_file.title=Кушодани файл
open_file_label=Кушодан
print.title=Чоп кардан
print_label=Чоп кардан
download.title=Боргирӣ кардан
download_label=Боргирӣ кардан
bookmark.title=Намуди ҷорӣ (нусха бардоштан ё кушодан дар равзанаи нав)
bookmark_label=Намуди ҷорӣ

# Secondary toolbar and context menu
tools.title=Абзорҳо




# Document properties dialog box
document_properties_file_name=Номи файл:
document_properties_file_size=Андозаи файл:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} КБ ({{size_b}} байт)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} МБ ({{size_b}} байт)
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=Мактуб
document_properties_page_size_name_legal=Ҳуқуқӣ
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} × {{height}} {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.

print_progress_message=Омодасозии ҳуҷҷат барои чоп…
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=Бекор кардан

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=Фаъол кардани навори ҷонибӣ

# LOCALIZATION NOTE (page_canvas): "{{page}}" will be replaced by the page number.
# LOCALIZATION NOTE (page_landmark): "{{page}}" will be replaced by the page number.
# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.

# Find panel button title and messages
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit[zero]=Зиёда аз {{limit}} мувофиқат
find_match_count_limit[one]=Зиёда аз {{limit}} мувофиқат
find_match_count_limit[two]=Зиёда аз {{limit}} мувофиқат
find_match_count_limit[few]=Зиёда аз {{limit}} мувофиқат
find_match_count_limit[many]=Зиёда аз {{limit}} мувофиқат
find_match_count_limit[other]=Зиёда аз {{limit}} мувофиқат

# Error panel labels
error_more_info=Маълумоти бештар
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=Файл: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number

# Predefined zoom values
page_scale_width=Аз рӯи паҳнои саҳифа
page_scale_auto=Андозаи худкор
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.

# Loading indicator messages

# Loading indicator messages
invalid_file_error=Файли PDF нодуруст ё вайроншуда мебошад.
missing_file_error=Файли PDF ғоиб аст.

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 – Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
password_label=Барои кушодани ин файли PDF ниҳонвожаро ворид кунед.
password_cancel=Бекор кардан

# LOCALIZATION NOTE (unsupported_feature_signatures): Should contain the same
# exact string as in the `chrome.properties` file.

