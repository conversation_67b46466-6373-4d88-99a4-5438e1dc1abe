<template>
  <div class="p-2">
    <div class="card-block mb-4">
      <el-form :model="queryParams" ref="queryFormRef" :inline="true" label-width="76px" class="query-form">
        <el-row>
          <el-col :span="6">
            <el-form-item label="BU归属" prop="bu">
              <el-select v-model="queryParams.bu" placeholder="请选择" clearable style="width: 100% !important;" multiple>
                <el-option v-for="option in buOptions" :key="option" :label="option" :value="option" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <div class="flex items-start">
              <el-form-item label="机构类别" prop="type">
                <el-select
                  v-model="queryParams.type"
                  placeholder="请选择"
                  clearable
                  style="width: 100% !important;"
                  multiple
                  max-collapse-tags="3"
                  collapse-tags
                >
                <el-option v-for="option in institutionTypeOptions" :key="option" :label="option" :value="option" />
              </el-select>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="flex items-start">
              <el-form-item label="机构名称" prop="orgName">
                <el-input v-model="queryParams.orgName" placeholder="请输入机构名称、别名" @keyup.enter="handleQuery" style="width: 100%;">
                  <template #suffix>
                    <el-icon>
                      <Search />
                    </el-icon>
                  </template>
                </el-input>
              </el-form-item>
              <div class="flex items-center gap-2">
                <advanced-filter ref="advancedFilterRef" @filter="handleAdvancedFilter" />
                <custom-table-header ref="customTableHeaderRef" @update:columns="handleColumnUpdate" />
                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                <el-button icon="Refresh" @click="handleReset">重置</el-button>
                <el-button type="info" plain icon="Download" @click="handleExport" :loading="exportLoading">导出</el-button>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-form>

      <el-divider style="margin: 0 0 16px 0;" />

      <!-- 快捷筛选按钮 -->
      <!-- <div class="mb-3">
        <div class="flex items-center gap-2 flex-wrap">
          <span class="text-sm text-gray-600">快捷筛选:</span>
          <el-button size="small" plain @click="applyQuickFilter('三甲医院')">三甲医院</el-button>
          <el-button size="small" plain @click="applyQuickFilter('二甲医院')">二甲医院</el-button>
          <el-button size="small" plain @click="applyQuickFilter('北京')">北京地区</el-button>
          <el-button size="small" plain @click="applyQuickFilter('上海')">上海地区</el-button>
          <el-button size="small" plain @click="applyQuickFilter('广东')">广东地区</el-button>
        </div>
      </div> -->

      <!-- 当前筛选条件标签 -->
      <!-- <div v-if="activeFiltersDisplay.length > 0" class="mb-4">
        <div class="flex items-center gap-2 flex-wrap">
          <span class="text-sm text-gray-600">当前筛选:</span>
          <el-tag v-for="(filter, index) in activeFiltersDisplay" :key="index" closable @close="removeFilter(index)" class="mr-1 mb-1">
            {{ filter.label }}
          </el-tag>
          <el-button type="primary" link size="small" @click="clearAllFilters">清空全部</el-button>
        </div>
      </div> -->

      <el-table :data="hcoList.tableData.value" stripe v-loading="hcoList.loading.value" style="width: 100%;">
        <el-table-column
          v-for="column in visibleColumns"
          :key="column.field"
          :prop="column.field"
          :label="column.label"
          :width="getColumnWidth(column.field)"
          :min-width="getColumnMinWidth(column.field)"
          :fixed="column.isFrozen"
        >
          <template #default="{ row }">
            <!-- 机构名称 - 可点击跳转详情 -->
            <el-button v-if="column.field === 'insName'" type="primary" link @click="handleDetail(row)">
              {{ row.insName }}
            </el-button>
            <!-- BU归属标签 - 显示标签数组 -->
            <div v-else-if="column.field === 'BU归属'" class="tag-container">
              <el-tag v-for="(tag, index) in getInstitutionTags(row.institutionTagVos)" :key="index" size="small" class="mr-1" type="info">
                {{ tag }}
              </el-tag>
              <span v-if="!getInstitutionTags(row.institutionTagVos).length" class="text-gray-400">-</span>
            </div>
            <!-- 标准科室 - 显示数组 -->
            <div v-else-if="column.field === 'insDept'" class="dept-container">
              <span v-if="Array.isArray(row.insDept) && row.insDept.length">
                {{ row.insDept.join(', ') }}
              </span>
              <span v-else class="text-gray-400">-</span>
            </div>
            <!-- 普通字段 -->
            <span v-else>{{ row[column.field] || '-' }}</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <div class="pagination-container" style="margin-top: 16px;">
        <el-pagination
          v-model:current-page="hcoList.queryParams.pageNum"
          v-model:page-size="hcoList.queryParams.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="hcoList.total.value"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { Search, Download } from '@element-plus/icons-vue'
import CustomTableHeader from '@/front-pages/src/components/custom-table-header.vue'
import AdvancedFilter from '@/front-pages/src/components/advanced-filter.vue'
import { useHcoList } from './composables/useHco'
import { getBuOptions, getInstitutionTypes, exportURLData } from './hco'
import { ElMessage } from 'element-plus'

// Props 定义
const props = defineProps({
  type: {
    type: String,
    default: ''
  },
  businessType: {
    type: String,
    default: 'default'
  }
})

const router = useRouter()

// 使用HCO列表管理，根据businessType区分不同业务场景
const hcoList = useHcoList(props.businessType)

const buOptions = ref([])
const institutionTypeOptions = ref([])
const queryFormRef = ref(null)
const advancedFilterRef = ref(null)
const customTableHeaderRef = ref(null)

const queryParams = ref({
  bu: '',
  type: '',
  orgName: ''
})

// Advanced filter data
const advancedFilters = ref([])

// 防止重复查询的标志位和防抖定时器
const isQuerying = ref(false)
let queryTimer = null

// 导出相关状态
const exportLoading = ref(false)

// Column configuration - 映射到API字段
const defaultColumnConfig = {
  frozen: [],
  nonFrozen: [
    { field: 'wbId', label: '机构ID' },
    { field: 'insName', label: '机构名称' },
    { field: 'insType', label: '机构类别' },
    { field: 'provinceName', label: '省份' },
    { field: 'cityName', label: '城市' },
    { field: 'districtName', label: '区县' },
    { field: 'address', label: '地址' },
    { field: 'insDept', label: '标准科室' },
    { field: '合作门诊', label: '合作门诊' },
    { field: '优质机构', label: '优质机构' },
    { field: 'BU归属', label: 'BU归属' }
  ]
}
// 初始化为空配置，等待自定义表头组件加载
const columnConfig = ref({
  frozen: [],
  nonFrozen: []
})

// Combine frozen and non-frozen columns for display
const visibleColumns = computed(() => {
  return [...(columnConfig.value.frozen).map(d => ({...d, isFrozen: true })), ...columnConfig.value.nonFrozen]
})

// 字段标签映射
const fieldLabelMap = {
  wbId: '机构ID',
  mdmCode: '主数据编码',
  insName: '机构名称',
  insType: '机构类别',
  insGrade: '机构级别',
  insLevel: '机构等次',
  provinceName: '省',
  cityName: '市',
  districtName: '区',
  address: '地址',
  btCode: '倍通编码',
  sapCode: 'SAP编码',
  u8Code: 'U8编码',
  insDept: '标准科室',
  hezuomenzhen: '合作门诊',
  youzhijigou: '优质机构'
}

// 操作符标签映射
const operatorLabelMap = {
  contains: '包含',
  'not-contains': '不包含',
  eq: '等于',
  neq: '不等于',
  empty: '为空',
  'not-empty': '不为空'
}

// 当前激活的筛选条件显示
const activeFiltersDisplay = computed(() => {
  const filters = []

  // 基础筛选条件
  if (queryParams.value.bu) {
    filters.push({
      label: `BU归属: ${queryParams.value.bu}`,
      type: 'basic',
      field: 'bu'
    })
  }

  if (queryParams.value.orgName) {
    filters.push({
      label: `机构名称: ${queryParams.value.orgName}`,
      type: 'basic',
      field: 'orgName'
    })
  }

  if (queryParams.value.type) {
    const typeLabel = Array.isArray(queryParams.value.type)
      ? queryParams.value.type.join(', ')
      : queryParams.value.type
    filters.push({
      label: `机构类别: ${typeLabel}`,
      type: 'basic',
      field: 'type'
    })
  }

  // 高级筛选条件
  advancedFilters.value.forEach((filter, index) => {
    // 检查过滤条件是否有效
    const isValidFilter = () => {
      if (!filter.left || !filter.op) return false

      // 对于数组类型的值（如多选），检查数组是否非空
      if (Array.isArray(filter.right)) {
        return filter.right.length > 0
      }

      // 对于其他类型的值
      return filter.right !== undefined && filter.right !== null && filter.right !== ''
    }

    if (isValidFilter()) {
      const fieldLabel = fieldLabelMap[filter.left] || filter.left
      const operatorLabel = operatorLabelMap[filter.op] || filter.op

      // 处理不同类型的值
      let valueLabel
      if (typeof filter.right === 'boolean') {
        valueLabel = filter.right ? '是' : '否'
      } else if (Array.isArray(filter.right)) {
        // 处理数组类型的值（如BU归属多选）
        valueLabel = filter.right.join(', ')
      } else {
        valueLabel = filter.right
      }

      filters.push({
        label: `${fieldLabel} ${operatorLabel} ${valueLabel}`,
        type: 'advanced',
        index: index
      })
    }
  })

  return filters
})

// Define column widths
function getColumnWidth(field) {
  const widthMap = {
    'wbId': '120',
    'insType': '120',
    'provinceName': '100',
    'cityName': '100',
    'districtName': '100',
    'institutionTagVos': '120'
  }
  return widthMap[field] || null
}

// Define column min-widths
function getColumnMinWidth(field) {
  const minWidthMap = {
    'insName': '180',
    'address': '200'
  }
  return minWidthMap[field] || '120'
}

// 获取机构标签显示文本
function getInstitutionTags(institutionTagVos) {
  if (!Array.isArray(institutionTagVos)) {
    return []
  }

  return institutionTagVos?.[0].tagValue || []
}

// 将高级筛选条件转换为新的QueryCondition格式
function convertFiltersToQueryConditions(filters) {
  const conditions = []

  filters.forEach(filter => {
    if (!filter.left || !filter.op || (filter.right === undefined || filter.right === null || filter.right === '')) {
      return
    }

    const { left: field, op: operator, right: value } = filter

    // 操作符映射
    const operatorMap = {
      'contains': 'contains',
      'not-contains': 'not_contains',
      'eq': 'eq',
      'neq': 'neq',
      'empty': 'empty',
      'not-empty': 'not_empty'
    }

    const mappedOperator = operatorMap[operator] || 'contains'

    // 特殊处理BU归属字段
    if (field === 'BU归属') {
      // 添加标签类型条件
      conditions.push({
        field: 'institutionTagVos.tagType',
        operator: mappedOperator === 'empty' ? 'not_contains' : 'eq',
        value: 'BU标签',
        nested: true,
        nestedPath: 'institutionTagVos'
      })

      // 处理多选值
      if (mappedOperator !== 'empty' && Array.isArray(value)) {
        conditions.push({
          field: 'institutionTagVos.tagValue',
          operator: 'in',
          valueList: value,
          nested: true,
          nestedPath: 'institutionTagVos'
        })
        // value.forEach(item => {
        //   conditions.push({
        //     field: 'institutionTagVos.tagValue',
        //     operator: 'contains',
        //     value: item,
        //     nested: true,
        //     nestedPath: 'institutionTagVos'
        //   })
        // })
      } else if(mappedOperator === 'not_empty') {
        conditions.push({
          field: 'institutionTagVos.tagValue',
          operator: mappedOperator,
          value: String(value),
          nested: true,
          nestedPath: 'institutionTagVos'
        })
      } else {
        // 单选值处理
        // conditions.push({
        //   field: 'institutionTagVos.tagValue',
        //   operator: mappedOperator,
        //   value: String(value),
        //   nested: true,
        //   nestedPath: 'institutionTagVos'
        // })
      }
    } else {
      // 其他字段正常处理
      conditions.push({
        field,
        operator: mappedOperator,
        value: String(value)
      })
    }
  })

  return conditions.map(d => {
    if(d.field === 'insName' && d.operator === 'eq') {
      d.field = 'insName.keyword';
    }
    return d;
  })
}

function handleQuery(resetPage = true) {
  // 清除之前的定时器
  if (queryTimer) {
    clearTimeout(queryTimer)
  }

  // 防抖处理，延迟执行查询
  queryTimer = setTimeout(async () => {
    // 防止重复调用
    if (isQuerying.value) {
      return
    }

    isQuerying.value = true

    try {
      // 构建基础查询条件
      const basicConditions = []

  if (queryParams.value.orgName) {
    basicConditions.push(
      {
        field: 'insName',
        operator: 'contains',
        value: queryParams.value.orgName
      },
      {
        field: 'insName',
        operator: 'contains',
        value: queryParams.value.orgName
      }
    )
  }

  // 处理BU归属标签
  if (queryParams.value.bu && queryParams.value.bu.length) {
    // 添加标签类型条件
    basicConditions.push({
      field: 'institutionTagVos.tagType',
      operator: 'eq',
      value: 'BU标签',
      nested: true,
      nestedPath: 'institutionTagVos'
    })

    // 处理多选值
    if (Array.isArray(queryParams.value.bu)) {
      // queryParams.value.bu.forEach(item => {
      //   basicConditions.push({
      //     field: 'institutionTagVos.tagValue',
      //     operator: 'contains',
      //     value: item,
      //     nested: true,
      //     nestedPath: 'institutionTagVos'
      //   })
      // })

      basicConditions.push({
          field: 'institutionTagVos.tagValue',
          operator: 'in',
          valueList: queryParams.value.bu,
          nested: true,
          nestedPath: 'institutionTagVos'
        })
    } else {
      // 单选值处理
      // basicConditions.push({
      //   field: 'institutionTagVos.tagValue',
      //   operator: 'contains',
      //   value: queryParams.value.bu,
      //   nested: true,
      //   nestedPath: 'institutionTagVos'
      // })
    }
  }

  // 处理机构类别筛选
  if (queryParams.value.type) {
    // // 处理多选值
    // if (Array.isArray(queryParams.value.type)) {
    //   queryParams.value.type.forEach(item => {
    //     basicConditions.push({
    //       field: 'insType',
    //       operator: 'eq',
    //       value: item
    //     })
    //   })
    // } else {
    //   // 单选值处理
    //   basicConditions.push({
    //     field: 'insType',
    //     operator: 'eq',
    //     value: queryParams.value.type
    //   })
    // }

    basicConditions.push({
      field: 'insType',
      operator: 'in',
      valueList: queryParams.value.type
    })
  }

  // 合并高级筛选条件
  const advancedConditions = convertFiltersToQueryConditions(advancedFilters.value)
  const allConditions = [...basicConditions, ...advancedConditions]

    // 只有在新查询时才重置到第一页，恢复状态时不重置
    if (resetPage) {
      hcoList.queryParams.pageNum = 1
    }

      // 执行查询
      await hcoList.fetchHcoList(allConditions, 'and')

      // 查询成功后保存当前查询条件
      hcoList.saveCurrentQueryState(queryParams.value, advancedFilters.value)
    } finally {
      // 查询完成后重置标志位
      isQuerying.value = false
    }
  }, 50) // 50ms 防抖延迟
}

function handleReset() {
  // 重置表单
  queryParams.value = { bu: '', type: '', orgName: '' }
  // 重新设置默认机构类型
  setDefaultInstitutionType()
  // 重置高级筛选
  advancedFilters.value = []
  // 调用子组件的重置方法（如有）
  advancedFilterRef.value?.clearAll?.()
  // 重置HCO查询参数并重新查询（这会清除持久化的查询条件）
  hcoList.resetQuery()
  // 注意：不需要再次调用 fetchHcoList，因为 clearAll 会触发 handleAdvancedFilter
  // 如果 clearAll 没有触发查询，则取消注释下面这行
  // hcoList.fetchHcoList([], 'and')
}

// Handle advanced filter updates
function handleAdvancedFilter(filters) {
  advancedFilters.value = filters
  console.log('Advanced filters applied:', filters)

  // 重新执行查询（会合并基础条件和高级筛选条件），重置页码
  handleQuery(true)
}

// 导出功能
async function handleExport() {
  if (exportLoading.value) {
    return
  }

  exportLoading.value = true

  try {
    // 构建导出参数，使用与查询相同的条件
    const basicConditions = []

    if (queryParams.value.orgName) {
      basicConditions.push(
        {
          field: 'insName',
          operator: 'contains',
          value: queryParams.value.orgName
        }
      )
    }

    // 处理BU归属标签
    if (queryParams.value.bu) {
      basicConditions.push({
        field: 'institutionTagVos.tagType',
        operator: 'eq',
        value: 'BU标签',
        nested: true,
        nestedPath: 'institutionTagVos'
      })

      if (Array.isArray(queryParams.value.bu)) {
        basicConditions.push({
          field: 'institutionTagVos.tagValue',
          operator: 'in',
          valueList: queryParams.value.bu,
          nested: true,
          nestedPath: 'institutionTagVos'
        })
      }
    }

    // 处理机构类别筛选
    if (queryParams.value.type) {
      basicConditions.push({
        field: 'insType',
        operator: 'in',
        valueList: queryParams.value.type
      })
    }

    // 合并高级筛选条件
    const advancedConditions = convertFiltersToQueryConditions(advancedFilters.value)
    const allConditions = [...basicConditions, ...advancedConditions]

    // 构建导出请求参数
    const exportParams = {
      conditions: allConditions,
      logic: 'and',
      status: '0'
    }

    const data = {
      queryParams: JSON.stringify({
        ...exportParams,
        appName: 'athena-tenant-mdm'
      }),
      funcCode: 'excelUtilHCO'
    }

    console.log('导出参数:', data)

    const response = await exportURLData(data)

    if (response && response.msg) {
      ElMessage.success(response.msg)
    } else {
      ElMessage.success('导出任务已提交，请稍后查看下载中心')
    }
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请稍后重试')
  } finally {
    exportLoading.value = false
  }
}

// Handle column configuration updates
function handleColumnUpdate(config) {
  columnConfig.value = config
  console.log('Column configuration updated:', config)
}

// 获取默认列配置（当没有自定义配置时使用）
function getDefaultColumnConfig() {
  return { ...defaultColumnConfig }
}

// 如果自定义表头组件没有加载到配置，使用默认配置
function ensureColumnConfig() {
  if (columnConfig.value.frozen.length === 0 && columnConfig.value.nonFrozen.length === 0) {
    columnConfig.value = getDefaultColumnConfig()
  }
}

// 分页改变处理
function handlePageChange(page) {
  hcoList.handlePageChange(page);
  // 分页变化后保存当前状态
  hcoList.saveCurrentQueryState(queryParams.value, advancedFilters.value);
}

// 分页大小改变处理
function handleSizeChange(size) {
  hcoList.handleSizeChange(size);
  // 分页大小变化后保存当前状态
  hcoList.saveCurrentQueryState(queryParams.value, advancedFilters.value);
}

// 跳转到详情页
function handleDetail(row) {
  // 在跳转前保存当前的查询条件
  hcoList.saveCurrentQueryState(queryParams.value, advancedFilters.value);

  router.push({
    path: '/ins_mdm_manage/ins_mdm/detail',
    query: {
      insCode: row.mdmCode,  // 使用mdmCode作为详情页参数
      orgName: row.insName
    }
  })
}

// 移除单个筛选条件
function removeFilter(index) {
  const filter = activeFiltersDisplay.value[index]

  if (filter.type === 'basic') {
    // 移除基础筛选条件
    if (filter.field === 'bu') {
      queryParams.value.bu = ''
    } else if (filter.field === 'orgName') {
      queryParams.value.orgName = ''
    } else if (filter.field === 'type') {
      queryParams.value.type = ''
    }
  } else if (filter.type === 'advanced') {
    // 移除高级筛选条件
    advancedFilters.value.splice(filter.index, 1)
  }

  // 重新查询，重置页码
  handleQuery(true)
}

// 清空所有筛选条件
function clearAllFilters() {
  queryParams.value = { bu: '', type: '', orgName: '' }
  // 重新设置默认机构类型
  setDefaultInstitutionType()
  advancedFilters.value = []
  advancedFilterRef.value?.clearAll?.()
  hcoList.resetQuery()
  hcoList.fetchHcoList([], 'and')
}

// 获取BU归属下拉框数据
async function fetchBuOptions() {
  try {
    const response = await getBuOptions()
    if (response && response.code === 200 && Array.isArray(response.data)) {
      // 提取所有的tagValue作为下拉选项
      const options = new Set()
      response.data?.forEach(item => {
        if (item.tagValue && Array.isArray(item.tagValue)) {
          item.tagValue.forEach(value => {
            options.add(value)
          })
        }
      })
      buOptions.value = Array.from(options)
    }
  } catch (error) {
    console.error('获取BU归属数据失败:', error)
    // 如果接口失败，使用默认数据
    buOptions.value = []
  }
}

// 获取机构类别下拉框数据
async function fetchInstitutionTypes() {
  try {
    const response = await getInstitutionTypes()
    if (response && response.code === 200 && Array.isArray(response.data)) {
      institutionTypeOptions.value = response.data
      // 获取到机构类别数据后，设置默认值
      setDefaultInstitutionType()
    }
  } catch (error) {
    console.error('获取机构类别数据失败:', error)
    // 如果接口失败，使用默认数据
    institutionTypeOptions.value = []
  }
}

// 根据 props.type 设置默认机构类型
function setDefaultInstitutionType() {
  if (props.type === '药店') {
    // 当 props.type 传入药店时，默认机构类型是 药店
    if (institutionTypeOptions.value.includes('药店')) {
      queryParams.value.type = ['药店']
    }
  } else {
    // 当 props.type 不传或传入其他值时，默认机构类型 非药店
    const nonPharmacyTypes = institutionTypeOptions.value.filter(type => type !== '药店')
    if (nonPharmacyTypes.length > 0) {
      queryParams.value.type = nonPharmacyTypes
    }
  }
}



// 恢复查询条件
function restoreQueryConditions() {
  const savedState = hcoList.restoreQueryState();
  if (savedState) {
    // 恢复基础查询条件
    if (savedState.basicQuery) {
      Object.assign(queryParams.value, savedState.basicQuery);
    }

    // 恢复高级筛选条件
    if (savedState.advancedFilters) {
      advancedFilters.value = savedState.advancedFilters;
      // 通知高级筛选组件恢复状态
      if (advancedFilterRef.value?.restoreFilters) {
        advancedFilterRef.value.restoreFilters(savedState.advancedFilters);
      }
    }

    // 恢复查询参数（分页等）
    if (savedState.queryParams) {
      Object.assign(hcoList.queryParams, savedState.queryParams);
    }

    return true;
  }
  return false;
}

// 页面加载时获取数据
onMounted(async () => {
  // 获取BU归属下拉框数据
  fetchBuOptions()
  // 获取机构类别下拉框数据并等待完成
  await fetchInstitutionTypes()

  // 确保有列配置后再加载数据
  setTimeout(() => {
    ensureColumnConfig()

    // 尝试恢复之前的查询条件
    const hasRestoredConditions = restoreQueryConditions();

    if (hasRestoredConditions) {
      // 如果恢复了查询条件，执行查询但不重置页码
      handleQuery(false)
    } else {
      // 如果没有保存的查询条件，执行初始查询（设置默认的机构类型）
      handleQuery(true)
    }
  }, 100) // 给自定义表头组件一点时间加载配置
})

// 页面卸载时清理定时器
onUnmounted(() => {
  // 清理定时器
  if (queryTimer) {
    clearTimeout(queryTimer)
    queryTimer = null
  }
})

// 使用组件内路由守卫来处理离开页面时的逻辑
import { onBeforeRouteLeave } from 'vue-router'

onBeforeRouteLeave((to) => {
  // 检查是否跳转到详情页
  if (to.path !== '/ins_mdm_manage/ins_mdm/detail') {
    // 如果不是跳转到详情页，则清除持久化数据
    hcoList.clearQueryState()
    console.log('离开HCO页面，已清除持久化查询条件')
  } else {
    console.log('跳转到详情页，保持持久化查询条件')
  }
})
</script>

<style lang="scss" scoped>
.pagination-container {
  display: flex;
  justify-content: flex-end;
}

.quick-filter-section {
  .el-button {
    margin-right: 8px;
    margin-bottom: 4px;
  }
}

.filter-tags-section {
  .el-tag {
    margin-right: 8px;
    margin-bottom: 4px;
  }
}
</style>
