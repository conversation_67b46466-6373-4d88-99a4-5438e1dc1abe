<template>
  <el-table :data="departments" style="width: 30%; height: calc(100vh - 250px);">
    <el-table-column prop="insDeptName" label="科室名称" />
    <el-table-column label="所属机构">
      <template #default="{ row }">
        <span v-if="row.isCurrentIns === '1'">
          <el-icon style="color: #67c23a; margin-right: 4px;"><Check /></el-icon>
          本机构
        </span>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { ElIcon } from 'element-plus';
import { Check } from '@element-plus/icons-vue';
import { useHcoDetail } from '../composables/useHco';

const route = useRoute();
const hcoDetail = useHcoDetail();
const departments = ref([]);

// 获取科室数据
const getDepartments = async () => {
  try {
    const insCode = route.query.insCode || route.query.insDsSpecCode;
    if (insCode) {
      await hcoDetail.fetchInsDepartments(insCode);

      // 将API返回的科室数据转换为组件需要的格式
      if (hcoDetail.departments.value && hcoDetail.departments.value.length > 0) {
        departments.value = hcoDetail.departments.value;
      } else {
        // 如果没有数据，显示默认的模拟数据
        departments.value = [];
      }
    }
  } catch (error) {
    console.error('获取科室数据失败:', error);
    // 出错时显示默认数据
    departments.value = [
      { name: '数据加载失败', institution: '请稍后重试', isCurrentInstitution: false }
    ];
  }
};

onMounted(() => {
  getDepartments();
});
</script>

<style scoped>
.el-table__row.current-institution {
  background-color: #f4faf4 !important; /* 低饱和度绿色 */
}
</style>

<script>
export default {
  mounted() {
    // 高亮本机构行
    this.$nextTick(() => {
      const rows = document.querySelectorAll('.el-table__row');
      this.departments?.forEach((dept, idx) => {
        if (dept.isCurrentInstitution) {
          rows[idx]?.classList.add('current-institution');
        }
      });
    });
  },
};
</script>
