import request from '@/utils/request';
import { AxiosPromise } from 'axios';
export const getProductList = (params: any): AxiosPromise<any> => {
  return request({
    url: `/msr/product/crm-products`,
    method: 'get',
    params
  });
};
export const getLogsApi = (params: any): AxiosPromise<any> => {
  return request({
    url: `/msr/opr-log/query`,
    method: 'get',
    params
  });
};
export const getListApi = (params: any): AxiosPromise<any> => {
  return request({
    url: `/plt/sensitive/query`,
    method: 'get',
    params
  });
};
export const addApi = (data: any): AxiosPromise<any> => {
  return request({
    url: `/plt/sensitive/add`,
    method: 'post',
    data
  });
};
export const deleteApi = (ids: any): AxiosPromise<any> => {
  return request({
    url: `/plt/sensitive/${ids}`,
    method: 'delete'
  });
};
