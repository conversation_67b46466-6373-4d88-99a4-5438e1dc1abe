---
title: athena-cloud
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# athena-cloud

Base URLs:

# Authentication

# 金项链/athena-mdm/DTP 辖区

<a id="opIdupdate_1"></a>

## POST 编辑辖区信息

POST /mdm/dtp/jur/update

编辑辖区信息

> Body 请求参数

```json
{
  "pageSize": 0,
  "pageNum": 0,
  "orderByColumn": "string",
  "isAsc": "string",
  "jurCode": "string",
  "userName": "string",
  "userCode": "string",
  "postCode": "string",
  "postName": "string",
  "deptCode": "string",
  "deptName": "string",
  "jurType": "string",
  "productName": "string",
  "status": "string",
  "leaderName": "string",
  "leaderCode": "string",
  "productCodeList": [
    "string"
  ],
  "operateType": "string",
  "deptId": 0,
  "postId": 0,
  "dtpDrugCount": 0
}
```

### 请求参数

| 名称          | 位置   | 类型                        | 必选 | 说明 |
| ------------- | ------ | --------------------------- | ---- | ---- |
| Authorization | header | string                      | 否   | none |
| clientId      | header | string                      | 否   | none |
| body          | body   | [DtpJurBo](#schemadtpjurbo) | 否   | none |

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {}
}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型              |
| ------ | ------------------------------------------------------- | ---- | --------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | none | [RVoid](#schemarvoid) |

<a id="opIdgetPage"></a>

## POST 查询辖区管理列表

POST /mdm/dtp/jur/page

查询辖区管理列表

> Body 请求参数

```json
{
  "pageSize": 0,
  "pageNum": 0,
  "orderByColumn": "string",
  "isAsc": "string",
  "jurCode": "string",
  "userName": "string",
  "userCode": "string",
  "postCode": "string",
  "postName": "string",
  "deptCode": "string",
  "deptName": "string",
  "jurType": "string",
  "productName": "string",
  "status": "string",
  "leaderName": "string",
  "leaderCode": "string",
  "productCodeList": [
    "string"
  ],
  "operateType": "string",
  "deptId": 0,
  "postId": 0,
  "dtpDrugCount": 0
}
```

### 请求参数

| 名称          | 位置   | 类型                        | 必选 | 说明 |
| ------------- | ------ | --------------------------- | ---- | ---- |
| Authorization | header | string                      | 否   | none |
| clientId      | header | string                      | 否   | none |
| body          | body   | [DtpJurBo](#schemadtpjurbo) | 否   | none |

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "total": 0,
    "rows": [
      {
        "jurCode": "string",
        "userName": "string",
        "userCode": "string",
        "postCode": "string",
        "postName": "string",
        "deptCode": "string",
        "deptName": "string",
        "dtpDrugCount": 0,
        "jurType": "string",
        "productName": "string",
        "status": "string",
        "leaderName": "string",
        "leaderCode": "string",
        "productVoList": [
          {
            "mdmCode": null,
            "productCode": null,
            "productName": null,
            "specCode": null,
            "specName": null
          }
        ],
        "drugName": "string",
        "drugCode": "string",
        "province": "string",
        "city": "string",
        "district": "string",
        "specCode": "string",
        "specName": "string"
      }
    ],
    "code": 0,
    "msg": "string"
  }
}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型                                                |
| ------ | ------------------------------------------------------- | ---- | ------------------------------------------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | none | [RTableDataInfoDtpJurVo](#schemartabledatainfodtpjurvo) |

<a id="opIdlog"></a>

## POST 获取辖区操作记录

POST /mdm/dtp/jur/operate/log

获取辖区操作记录

> Body 请求参数

```json
{
  "pageSize": 0,
  "pageNum": 0,
  "orderByColumn": "string",
  "isAsc": "string",
  "jurCode": "string",
  "userName": "string",
  "userCode": "string",
  "postCode": "string",
  "postName": "string",
  "deptCode": "string",
  "deptName": "string",
  "jurType": "string",
  "productName": "string",
  "status": "string",
  "leaderName": "string",
  "leaderCode": "string",
  "productCodeList": [
    "string"
  ],
  "operateType": "string",
  "deptId": 0,
  "postId": 0,
  "dtpDrugCount": 0
}
```

### 请求参数

| 名称          | 位置   | 类型                        | 必选 | 说明 |
| ------------- | ------ | --------------------------- | ---- | ---- |
| Authorization | header | string                      | 否   | none |
| clientId      | header | string                      | 否   | none |
| body          | body   | [DtpJurBo](#schemadtpjurbo) | 否   | none |

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "total": 0,
    "list": [
      {}
    ],
    "pageNum": 0,
    "pageSize": 0,
    "size": 0,
    "startRow": 0,
    "endRow": 0,
    "pages": 0,
    "prePage": 0,
    "nextPage": 0,
    "hasPreviousPage": true,
    "hasNextPage": true,
    "navigatePages": 0,
    "navigatePageNums": [
      0
    ],
    "navigateFirstPage": 0,
    "navigateLastPage": 0,
    "firstPage": true,
    "lastPage": true
  }
}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型                                      |
| ------ | ------------------------------------------------------- | ---- | --------------------------------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | none | [REsPageInfoObject](#schemarespageinfoobject) |

<a id="opIdgetDrugPage"></a>

## POST 查询辖区药店管理列表

POST /mdm/dtp/jur/drug/page

查询辖区药店管理列表

> Body 请求参数

```json
{
  "pageSize": 0,
  "pageNum": 0,
  "orderByColumn": "string",
  "isAsc": "string",
  "jurCode": "string",
  "userName": "string",
  "userCode": "string",
  "postCode": "string",
  "postName": "string",
  "deptCode": "string",
  "deptName": "string",
  "jurType": "string",
  "productName": "string",
  "status": "string",
  "leaderName": "string",
  "leaderCode": "string",
  "productCodeList": [
    "string"
  ],
  "operateType": "string",
  "deptId": 0,
  "postId": 0,
  "drugName": "string",
  "drugCode": "string",
  "province": "string",
  "city": "string",
  "district": "string",
  "specCode": "string",
  "specName": "string",
  "dtpDrugCount": 0
}
```

### 请求参数

| 名称          | 位置   | 类型                                | 必选 | 说明 |
| ------------- | ------ | ----------------------------------- | ---- | ---- |
| Authorization | header | string                              | 否   | none |
| clientId      | header | string                              | 否   | none |
| body          | body   | [DtpJurDrugBo](#schemadtpjurdrugbo) | 否   | none |

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "total": 0,
    "rows": [
      {
        "jurCode": "string",
        "userName": "string",
        "userCode": "string",
        "postCode": "string",
        "postName": "string",
        "deptCode": "string",
        "deptName": "string",
        "dtpDrugCount": 0,
        "jurType": "string",
        "productName": "string",
        "status": "string",
        "leaderName": "string",
        "leaderCode": "string",
        "productVoList": [
          {
            "mdmCode": null,
            "productCode": null,
            "productName": null,
            "specCode": null,
            "specName": null
          }
        ],
        "drugName": "string",
        "drugCode": "string",
        "province": "string",
        "city": "string",
        "district": "string",
        "specCode": "string",
        "specName": "string"
      }
    ],
    "code": 0,
    "msg": "string"
  }
}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型                                                |
| ------ | ------------------------------------------------------- | ---- | ------------------------------------------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | none | [RTableDataInfoDtpJurVo](#schemartabledatainfodtpjurvo) |

<a id="opIdjurDetail"></a>

## GET 获取辖区详情

GET /mdm/dtp/jur/detail

获取辖区详情

### 请求参数

| 名称          | 位置   | 类型   | 必选 | 说明 |
| ------------- | ------ | ------ | ---- | ---- |
| jurCode       | query  | string | 是   | none |
| Authorization | header | string | 否   | none |
| clientId      | header | string | 否   | none |

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "jurCode": "string",
    "userName": "string",
    "userCode": "string",
    "postCode": "string",
    "postName": "string",
    "deptCode": "string",
    "deptName": "string",
    "dtpDrugCount": 0,
    "jurType": "string",
    "productName": "string",
    "status": "string",
    "leaderName": "string",
    "leaderCode": "string",
    "productVoList": [
      {
        "mdmCode": "string",
        "productCode": "string",
        "productName": "string",
        "specCode": "string",
        "specName": "string"
      }
    ],
    "drugName": "string",
    "drugCode": "string",
    "province": "string",
    "city": "string",
    "district": "string",
    "specCode": "string",
    "specName": "string"
  }
}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型                      |
| ------ | ------------------------------------------------------- | ---- | ----------------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | none | [RDtpJurVo](#schemardtpjurvo) |

<a id="opIdlist"></a>

## GET 获取部门列表

GET /mdm/dtp/jur/dept-list

获取部门列表

### 请求参数

| 名称           | 位置   | 类型           | 必选 | 说明                      |
| -------------- | ------ | -------------- | ---- | ------------------------- |
| deptId         | query  | integer(int64) | 否   | 部门 id                   |
| deptCodeList   | query  | array[string]  | 否   | 部门 id                   |
| parentId       | query  | integer(int64) | 否   | 父部门 ID                 |
| deptName       | query  | string         | 否   | 部门名称                  |
| deptType       | query  | string         | 否   | 部门类型                  |
| status         | query  | string         | 否   | 部门状态（0 正常 1 停用） |
| parentDeptCode | query  | string         | 否   | 父级部门编码              |
| deptCode       | query  | string         | 否   | 部门编号                  |
| deptLevel      | query  | string         | 否   | 部门类型                  |
| ancestors      | query  | string         | 否   | 祖籍                      |
| version        | query  | string         | 否   | none                      |
| mgr            | query  | boolean        | 否   | none                      |
| ancestorsList  | query  | array[integer] | 否   | 祖籍                      |
| appCode        | query  | string         | 是   | none                      |
| Authorization  | header | string         | 否   | none                      |
| clientId       | header | string         | 否   | none                      |

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "deptId": 0,
      "companyCode": "string",
      "parentId": 0,
      "parentDeptName": "string",
      "ancestors": "string",
      "deptName": "string",
      "deptType": "string",
      "orderNum": 0,
      "status": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "parentDeptCode": "string",
      "deptCode": "string",
      "deptLevel": "string",
      "mgrName": "string",
      "isChildren": true,
      "isJob": true
    }
  ]
}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型                                        |
| ------ | ------------------------------------------------------- | ---- | ----------------------------------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | none | [RListSysDeptUserBo](#schemarlistsysdeptuserbo) |

# 数据模型

<h2 id="tocS_RVoid">RVoid</h2>

<a id="schemarvoid"></a>
<a id="schema_RVoid"></a>
<a id="tocSrvoid"></a>
<a id="tocsrvoid"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {}
}

```

响应信息主体

### 属性

| 名称 | 类型           | 必选  | 约束 | 中文名 | 说明       |
| ---- | -------------- | ----- | ---- | ------ | ---------- |
| code | integer(int32) | false | none |        | 消息状态码 |
| msg  | string         | false | none |        | 消息内容   |
| data | object         | false | none |        | 数据对象   |

<h2 id="tocS_ProductVo">ProductVo</h2>

<a id="schemaproductvo"></a>
<a id="schema_ProductVo"></a>
<a id="tocSproductvo"></a>
<a id="tocsproductvo"></a>

```json
{
  "mdmCode": "string",
  "productCode": "string",
  "productName": "string",
  "specCode": "string",
  "specName": "string"
}

```

### 属性

| 名称        | 类型   | 必选  | 约束 | 中文名 | 说明 |
| ----------- | ------ | ----- | ---- | ------ | ---- |
| mdmCode     | string | false | none |        | none |
| productCode | string | false | none |        | none |
| productName | string | false | none |        | none |
| specCode    | string | false | none |        | none |
| specName    | string | false | none |        | none |

<h2 id="tocS_RListSysDeptUserBo">RListSysDeptUserBo</h2>

<a id="schemarlistsysdeptuserbo"></a>
<a id="schema_RListSysDeptUserBo"></a>
<a id="tocSrlistsysdeptuserbo"></a>
<a id="tocsrlistsysdeptuserbo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "deptId": 0,
      "companyCode": "string",
      "parentId": 0,
      "parentDeptName": "string",
      "ancestors": "string",
      "deptName": "string",
      "deptType": "string",
      "orderNum": 0,
      "status": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "parentDeptCode": "string",
      "deptCode": "string",
      "deptLevel": "string",
      "mgrName": "string",
      "isChildren": true,
      "isJob": true
    }
  ]
}

```

响应信息主体

### 属性

| 名称 | 类型                                    | 必选  | 约束 | 中文名 | 说明       |
| ---- | --------------------------------------- | ----- | ---- | ------ | ---------- |
| code | integer(int32)                          | false | none |        | 消息状态码 |
| msg  | string                                  | false | none |        | 消息内容   |
| data | [[SysDeptUserBo](#schemasysdeptuserbo)] | false | none |        | 数据对象   |

<h2 id="tocS_SysDeptUserBo">SysDeptUserBo</h2>

<a id="schemasysdeptuserbo"></a>
<a id="schema_SysDeptUserBo"></a>
<a id="tocSsysdeptuserbo"></a>
<a id="tocssysdeptuserbo"></a>

```json
{
  "deptId": 0,
  "companyCode": "string",
  "parentId": 0,
  "parentDeptName": "string",
  "ancestors": "string",
  "deptName": "string",
  "deptType": "string",
  "orderNum": 0,
  "status": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "parentDeptCode": "string",
  "deptCode": "string",
  "deptLevel": "string",
  "mgrName": "string",
  "isChildren": true,
  "isJob": true
}

```

部门视图对象

### 属性

| 名称           | 类型              | 必选  | 约束 | 中文名 | 说明                       |
| -------------- | ----------------- | ----- | ---- | ------ | -------------------------- |
| deptId         | integer(int64)    | false | none |        | 部门 id                    |
| companyCode    | string            | false | none |        | 公司编码                   |
| parentId       | integer(int64)    | false | none |        | 父部门 id                  |
| parentDeptName | string            | false | none |        | 父部门名称                 |
| ancestors      | string            | false | none |        | 祖级列表                   |
| deptName       | string            | false | none |        | 部门名称                   |
| deptType       | string            | false | none |        | 部门类型                   |
| orderNum       | integer(int32)    | false | none |        | 显示顺序                   |
| status         | string            | false | none |        | 部门状态（0 正常 1 停用）  |
| createTime     | string(date-time) | false | none |        | 创建时间                   |
| parentDeptCode | string            | false | none |        | 父级部门编码               |
| deptCode       | string            | false | none |        | 部门编号                   |
| deptLevel      | string            | false | none |        | 部门等级                   |
| mgrName        | string            | false | none |        | 部门负责人                 |
| isChildren     | boolean           | false | none |        | 是否有下级                 |
| isJob          | boolean           | false | none |        | 是否空岗 true:是，false 否 |

<h2 id="tocS_EsPageInfoObject">EsPageInfoObject</h2>

<a id="schemaespageinfoobject"></a>
<a id="schema_EsPageInfoObject"></a>
<a id="tocSespageinfoobject"></a>
<a id="tocsespageinfoobject"></a>

```json
{
  "total": 0,
  "list": [
    {}
  ],
  "pageNum": 0,
  "pageSize": 0,
  "size": 0,
  "startRow": 0,
  "endRow": 0,
  "pages": 0,
  "prePage": 0,
  "nextPage": 0,
  "hasPreviousPage": true,
  "hasNextPage": true,
  "navigatePages": 0,
  "navigatePageNums": [
    0
  ],
  "navigateFirstPage": 0,
  "navigateLastPage": 0,
  "firstPage": true,
  "lastPage": true
}

```

### 属性

| 名称              | 类型           | 必选  | 约束 | 中文名 | 说明 |
| ----------------- | -------------- | ----- | ---- | ------ | ---- |
| total             | integer(int64) | false | none |        | none |
| list              | [object]       | false | none |        | none |
| pageNum           | integer(int32) | false | none |        | none |
| pageSize          | integer(int32) | false | none |        | none |
| size              | integer(int32) | false | none |        | none |
| startRow          | integer(int32) | false | none |        | none |
| endRow            | integer(int32) | false | none |        | none |
| pages             | integer(int32) | false | none |        | none |
| prePage           | integer(int32) | false | none |        | none |
| nextPage          | integer(int32) | false | none |        | none |
| hasPreviousPage   | boolean        | false | none |        | none |
| hasNextPage       | boolean        | false | none |        | none |
| navigatePages     | integer(int32) | false | none |        | none |
| navigatePageNums  | [integer]      | false | none |        | none |
| navigateFirstPage | integer(int32) | false | none |        | none |
| navigateLastPage  | integer(int32) | false | none |        | none |
| firstPage         | boolean        | false | none |        | none |
| lastPage          | boolean        | false | none |        | none |

<h2 id="tocS_REsPageInfoObject">REsPageInfoObject</h2>

<a id="schemarespageinfoobject"></a>
<a id="schema_REsPageInfoObject"></a>
<a id="tocSrespageinfoobject"></a>
<a id="tocsrespageinfoobject"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "total": 0,
    "list": [
      {}
    ],
    "pageNum": 0,
    "pageSize": 0,
    "size": 0,
    "startRow": 0,
    "endRow": 0,
    "pages": 0,
    "prePage": 0,
    "nextPage": 0,
    "hasPreviousPage": true,
    "hasNextPage": true,
    "navigatePages": 0,
    "navigatePageNums": [
      0
    ],
    "navigateFirstPage": 0,
    "navigateLastPage": 0,
    "firstPage": true,
    "lastPage": true
  }
}

```

响应信息主体

### 属性

| 名称 | 类型                                        | 必选  | 约束 | 中文名 | 说明       |
| ---- | ------------------------------------------- | ----- | ---- | ------ | ---------- |
| code | integer(int32)                              | false | none |        | 消息状态码 |
| msg  | string                                      | false | none |        | 消息内容   |
| data | [EsPageInfoObject](#schemaespageinfoobject) | false | none |        | none       |

<h2 id="tocS_DtpJurBo">DtpJurBo</h2>

<a id="schemadtpjurbo"></a>
<a id="schema_DtpJurBo"></a>
<a id="tocSdtpjurbo"></a>
<a id="tocsdtpjurbo"></a>

```json
{
  "pageSize": 0,
  "pageNum": 0,
  "orderByColumn": "string",
  "isAsc": "string",
  "jurCode": "string",
  "userName": "string",
  "userCode": "string",
  "postCode": "string",
  "postName": "string",
  "deptCode": "string",
  "deptName": "string",
  "jurType": "string",
  "productName": "string",
  "status": "string",
  "leaderName": "string",
  "leaderCode": "string",
  "productCodeList": [
    "string"
  ],
  "operateType": "string",
  "deptId": 0,
  "postId": 0,
  "dtpDrugCount": 0
}

```

### 属性

| 名称            | 类型           | 必选  | 约束 | 中文名 | 说明                                                         |
| --------------- | -------------- | ----- | ---- | ------ | ------------------------------------------------------------ |
| pageSize        | integer(int32) | false | none |        | 分页大小                                                     |
| pageNum         | integer(int32) | false | none |        | 当前页数                                                     |
| orderByColumn   | string         | false | none |        | 排序列                                                       |
| isAsc           | string         | false | none |        | 排序的方向 desc 或者 asc                                     |
| jurCode         | string         | false | none |        | 辖区编码                                                     |
| userName        | string         | false | none |        | 辖区负责人                                                   |
| userCode        | string         | false | none |        | 辖区负责人编码                                               |
| postCode        | string         | false | none |        | 岗位编码                                                     |
| postName        | string         | false | none |        | 岗位名称                                                     |
| deptCode        | string         | false | none |        | 部门编码                                                     |
| deptName        | string         | false | none |        | 部门名称                                                     |
| jurType         | string         | false | none |        | 辖区类型 sales:销售 dtp:DTP 专员                             |
| productName     | string         | false | none |        | 产品名称                                                     |
| status          | string         | false | none |        | 辖区状态                                                     |
| leaderName      | string         | false | none |        | 上级姓名                                                     |
| leaderCode      | string         | false | none |        | 上级工号                                                     |
| productCodeList | [string]       | false | none |        | 产品编码集合（操作辖区使用）                                 |
| operateType     | string         | false | none |        | 操作类型(操作辖区使用) insert:新增，update:更新，delete:删除 |
| deptId          | integer(int64) | false | none |        | 部门 ID（新增辖区使用）                                      |
| postId          | integer(int64) | false | none |        | 岗位 ID（新增辖区使用）                                      |
| dtpDrugCount    | integer(int64) | false | none |        | 辖区药店数量                                                 |

<h2 id="tocS_DtpJurDrugBo">DtpJurDrugBo</h2>

<a id="schemadtpjurdrugbo"></a>
<a id="schema_DtpJurDrugBo"></a>
<a id="tocSdtpjurdrugbo"></a>
<a id="tocsdtpjurdrugbo"></a>

```json
{
  "pageSize": 0,
  "pageNum": 0,
  "orderByColumn": "string",
  "isAsc": "string",
  "jurCode": "string",
  "userName": "string",
  "userCode": "string",
  "postCode": "string",
  "postName": "string",
  "deptCode": "string",
  "deptName": "string",
  "jurType": "string",
  "productName": "string",
  "status": "string",
  "leaderName": "string",
  "leaderCode": "string",
  "productCodeList": [
    "string"
  ],
  "operateType": "string",
  "deptId": 0,
  "postId": 0,
  "drugName": "string",
  "drugCode": "string",
  "province": "string",
  "city": "string",
  "district": "string",
  "specCode": "string",
  "specName": "string",
  "dtpDrugCount": 0
}

```

### 属性

| 名称            | 类型           | 必选  | 约束 | 中文名 | 说明                                                         |
| --------------- | -------------- | ----- | ---- | ------ | ------------------------------------------------------------ |
| pageSize        | integer(int32) | false | none |        | 分页大小                                                     |
| pageNum         | integer(int32) | false | none |        | 当前页数                                                     |
| orderByColumn   | string         | false | none |        | 排序列                                                       |
| isAsc           | string         | false | none |        | 排序的方向 desc 或者 asc                                     |
| jurCode         | string         | false | none |        | 辖区编码                                                     |
| userName        | string         | false | none |        | 辖区负责人                                                   |
| userCode        | string         | false | none |        | 辖区负责人编码                                               |
| postCode        | string         | false | none |        | 岗位编码                                                     |
| postName        | string         | false | none |        | 岗位名称                                                     |
| deptCode        | string         | false | none |        | 部门编码                                                     |
| deptName        | string         | false | none |        | 部门名称                                                     |
| jurType         | string         | false | none |        | 辖区类型 sales:销售 dtp:DTP 专员                             |
| productName     | string         | false | none |        | 产品名称                                                     |
| status          | string         | false | none |        | 辖区状态                                                     |
| leaderName      | string         | false | none |        | 上级姓名                                                     |
| leaderCode      | string         | false | none |        | 上级工号                                                     |
| productCodeList | [string]       | false | none |        | 产品编码集合（操作辖区使用）                                 |
| operateType     | string         | false | none |        | 操作类型(操作辖区使用) insert:新增，update:更新，delete:删除 |
| deptId          | integer(int64) | false | none |        | 部门 ID（新增辖区使用）                                      |
| postId          | integer(int64) | false | none |        | 岗位 ID（新增辖区使用）                                      |
| drugName        | string         | false | none |        | 药店名称                                                     |
| drugCode        | string         | false | none |        | 药店编码                                                     |
| province        | string         | false | none |        | 省份                                                         |
| city            | string         | false | none |        | 城市                                                         |
| district        | string         | false | none |        | 区县                                                         |
| specCode        | string         | false | none |        | 品规编码                                                     |
| specName        | string         | false | none |        | 品规名称                                                     |
| dtpDrugCount    | integer(int64) | false | none |        | 辖区药店数量                                                 |

<h2 id="tocS_DtpJurVo">DtpJurVo</h2>

<a id="schemadtpjurvo"></a>
<a id="schema_DtpJurVo"></a>
<a id="tocSdtpjurvo"></a>
<a id="tocsdtpjurvo"></a>

```json
{
  "jurCode": "string",
  "userName": "string",
  "userCode": "string",
  "postCode": "string",
  "postName": "string",
  "deptCode": "string",
  "deptName": "string",
  "dtpDrugCount": 0,
  "jurType": "string",
  "productName": "string",
  "status": "string",
  "leaderName": "string",
  "leaderCode": "string",
  "productVoList": [
    {
      "mdmCode": "string",
      "productCode": "string",
      "productName": "string",
      "specCode": "string",
      "specName": "string"
    }
  ],
  "drugName": "string",
  "drugCode": "string",
  "province": "string",
  "city": "string",
  "district": "string",
  "specCode": "string",
  "specName": "string"
}

```

### 属性

| 名称          | 类型                            | 必选  | 约束 | 中文名 | 说明                     |
| ------------- | ------------------------------- | ----- | ---- | ------ | ------------------------ |
| jurCode       | string                          | false | none |        | 辖区编码                 |
| userName      | string                          | false | none |        | 辖区负责人               |
| userCode      | string                          | false | none |        | 辖区负责人编码           |
| postCode      | string                          | false | none |        | 岗位编码                 |
| postName      | string                          | false | none |        | 岗位名称                 |
| deptCode      | string                          | false | none |        | 部门编码                 |
| deptName      | string                          | false | none |        | 部门名称                 |
| dtpDrugCount  | integer(int64)                  | false | none |        | 辖区药店数量             |
| jurType       | string                          | false | none |        | 辖区类型                 |
| productName   | string                          | false | none |        | 产品名称                 |
| status        | string                          | false | none |        | 辖区状态                 |
| leaderName    | string                          | false | none |        | 上级姓名                 |
| leaderCode    | string                          | false | none |        | 上级工号                 |
| productVoList | [[ProductVo](#schemaproductvo)] | false | none |        | 产品列表（辖区详情使用） |
| drugName      | string                          | false | none |        | 药店名称                 |
| drugCode      | string                          | false | none |        | 药店编码                 |
| province      | string                          | false | none |        | 省份                     |
| city          | string                          | false | none |        | 城市                     |
| district      | string                          | false | none |        | 区县                     |
| specCode      | string                          | false | none |        | 品规编码                 |
| specName      | string                          | false | none |        | 品规名称                 |

<h2 id="tocS_RTableDataInfoDtpJurVo">RTableDataInfoDtpJurVo</h2>

<a id="schemartabledatainfodtpjurvo"></a>
<a id="schema_RTableDataInfoDtpJurVo"></a>
<a id="tocSrtabledatainfodtpjurvo"></a>
<a id="tocsrtabledatainfodtpjurvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "total": 0,
    "rows": [
      {
        "jurCode": "string",
        "userName": "string",
        "userCode": "string",
        "postCode": "string",
        "postName": "string",
        "deptCode": "string",
        "deptName": "string",
        "dtpDrugCount": 0,
        "jurType": "string",
        "productName": "string",
        "status": "string",
        "leaderName": "string",
        "leaderCode": "string",
        "productVoList": [
          {
            "mdmCode": null,
            "productCode": null,
            "productName": null,
            "specCode": null,
            "specName": null
          }
        ],
        "drugName": "string",
        "drugCode": "string",
        "province": "string",
        "city": "string",
        "district": "string",
        "specCode": "string",
        "specName": "string"
      }
    ],
    "code": 0,
    "msg": "string"
  }
}

```

响应信息主体

### 属性

| 名称 | 类型                                                  | 必选  | 约束 | 中文名 | 说明             |
| ---- | ----------------------------------------------------- | ----- | ---- | ------ | ---------------- |
| code | integer(int32)                                        | false | none |        | 消息状态码       |
| msg  | string                                                | false | none |        | 消息内容         |
| data | [TableDataInfoDtpJurVo](#schematabledatainfodtpjurvo) | false | none |        | 表格分页数据对象 |

<h2 id="tocS_TableDataInfoDtpJurVo">TableDataInfoDtpJurVo</h2>

<a id="schematabledatainfodtpjurvo"></a>
<a id="schema_TableDataInfoDtpJurVo"></a>
<a id="tocStabledatainfodtpjurvo"></a>
<a id="tocstabledatainfodtpjurvo"></a>

```json
{
  "total": 0,
  "rows": [
    {
      "jurCode": "string",
      "userName": "string",
      "userCode": "string",
      "postCode": "string",
      "postName": "string",
      "deptCode": "string",
      "deptName": "string",
      "dtpDrugCount": 0,
      "jurType": "string",
      "productName": "string",
      "status": "string",
      "leaderName": "string",
      "leaderCode": "string",
      "productVoList": [
        {
          "mdmCode": "string",
          "productCode": "string",
          "productName": "string",
          "specCode": "string",
          "specName": "string"
        }
      ],
      "drugName": "string",
      "drugCode": "string",
      "province": "string",
      "city": "string",
      "district": "string",
      "specCode": "string",
      "specName": "string"
    }
  ],
  "code": 0,
  "msg": "string"
}

```

表格分页数据对象

### 属性

| 名称  | 类型                          | 必选  | 约束 | 中文名 | 说明       |
| ----- | ----------------------------- | ----- | ---- | ------ | ---------- |
| total | integer(int64)                | false | none |        | 总记录数   |
| rows  | [[DtpJurVo](#schemadtpjurvo)] | false | none |        | 列表数据   |
| code  | integer(int32)                | false | none |        | 消息状态码 |
| msg   | string                        | false | none |        | 消息内容   |

<h2 id="tocS_RDtpJurVo">RDtpJurVo</h2>

<a id="schemardtpjurvo"></a>
<a id="schema_RDtpJurVo"></a>
<a id="tocSrdtpjurvo"></a>
<a id="tocsrdtpjurvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "jurCode": "string",
    "userName": "string",
    "userCode": "string",
    "postCode": "string",
    "postName": "string",
    "deptCode": "string",
    "deptName": "string",
    "dtpDrugCount": 0,
    "jurType": "string",
    "productName": "string",
    "status": "string",
    "leaderName": "string",
    "leaderCode": "string",
    "productVoList": [
      {
        "mdmCode": "string",
        "productCode": "string",
        "productName": "string",
        "specCode": "string",
        "specName": "string"
      }
    ],
    "drugName": "string",
    "drugCode": "string",
    "province": "string",
    "city": "string",
    "district": "string",
    "specCode": "string",
    "specName": "string"
  }
}

```

响应信息主体

### 属性

| 名称 | 类型                        | 必选  | 约束 | 中文名 | 说明       |
| ---- | --------------------------- | ----- | ---- | ------ | ---------- |
| code | integer(int32)              | false | none |        | 消息状态码 |
| msg  | string                      | false | none |        | 消息内容   |
| data | [DtpJurVo](#schemadtpjurvo) | false | none |        | none       |
