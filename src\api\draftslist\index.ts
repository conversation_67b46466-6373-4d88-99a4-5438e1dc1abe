import request from '@/utils/request';
import { AxiosPromise } from 'axios';

export const getManagePage = (query: any) => {
  return request({
    url: `/lib/doc/manage/page`,
    method: 'get',
    params: query
  });
};

export const disableManage = (query: any) => {
  return request({
    url: `/lib/doc/manage/disable/${query.id}`,
    method: 'post',
    data: { remark: query.remark }
  });
};
export const manageStart = (id: any) => {
  return request({
    url: `/lib/doc/manage/start/${id}`,
    method: 'post'
  });
};
export const getroleList = () => {
  return request({
    url: `/lib/role/list`,
    method: 'get'
  });
};
export const addDocManage = (data: any) => {
  return request({
    url: `/lib/doc/manage`,
    method: 'post',
    data
  });
};

export const listDept = (query?: any) => {
  return request({
    url: '/plt/dept/lib/list',
    method: 'get',
    params: query
  });
};
export const docManageDetail = (id?: any) => {
  return request({
    url: `/lib/doc/manage/${id}`,
    method: 'get'
  });
};

export const editDocManage = (data: any) => {
  return request({
    url: `/lib/doc/manage`,
    method: 'put',
    data
  });
};
