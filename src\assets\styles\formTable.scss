.p-2,
.p-3 {
  padding: 0 16px 16px;

  .card-block {
    border-radius: 4px;
    padding: 16px;
    background-color: #ffffff;
  }
  .content-form {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    .form {
      flex: 1;
      padding-left: 24px;
    }
    .button-form {
      margin-left: 68px;
    }
  }
  .title-form {
    color: #1d2129;
    padding: 0 16px 16px 0;
    // padding-left: 0;
    font-weight: 700;
    display: flex;
    align-items: center;
    &::before {
      display: block;
      content: '';
      width: 4px;
      height: 18px;
      margin-right: 8px;
      border-radius: 0px 9px 9px 0px;
      background: var(--brand-color, linear-gradient(144deg, #597dff 2.72%, #2551f2 64.55%));
    }
    .text-btn {
      position: absolute;
      right: 24px;
    }
  }
  .no-tag {
    &::before {
      display: block;
      content: '';
      width: 0;
      height: 0;
    }
  }
  .query-form-ref {
    padding-left: 30px;
  }
  .query-form-tag {
    .el-tag {
      padding: 0 8px;
      margin: 0;
      border-radius: 2px;
      color: #606266;
      font-size: 14px !important;
      background-color: #fff;
      margin: 2px;
      border: 1px solid var(--neutral-color-3, #e3e4e9);
    }
    .el-select .el-select__tags-text {
      // width:45px;
      text-align: center;
    }
  }
  .hover-form {
    .el-form-item {
      margin-bottom: 0;
    }
    .el-form-item__label {
      color: var(--Neutral-Color--3, #86909c);
      font-weight: 400;
    }
  }
}

.p-3 {
  display: flex;
  width: 100%;
  .card-block {
    width: calc(100% - 256px);
  }

  .access-right {
    width: 15%;
    min-width: 240px;
    margin-left: 16px;
    background-color: #fff;
  }
  .form-title {
    color: var(--unnamed, #2551f2);
    font-size: 24px;
    font-style: normal;
    font-weight: 500;
    line-height: 32px;
  }
}
.title-form {
  color: #1d2129;
  padding: 0 16px 16px 0;
  // padding-left: 0;
  font-weight: 700;
  display: flex;
  align-items: center;
  &::before {
    display: block;
    content: '';
    width: 4px;
    height: 18px;
    margin-right: 8px;
    border-radius: 0px 9px 9px 0px;
    background: var(--brand-color, linear-gradient(144deg, #597dff 2.72%, #2551f2 64.55%));
  }
  .text-btn {
    position: absolute;
    right: 24px;
  }
}
