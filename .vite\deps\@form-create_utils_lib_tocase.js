import "./chunk-DFKQJ226.js";

// node_modules/@form-create/utils/lib/tocase.js
function toCase(str) {
  const to = str.replace(/(-[a-z])/g, function(v) {
    return v.replace("-", "").toLocaleUpperCase();
  });
  return lower(to);
}
function lower(str) {
  return str.replace(str[0], str[0].toLowerCase());
}
export {
  toCase as default,
  lower
};
//# sourceMappingURL=@form-create_utils_lib_tocase.js.map
