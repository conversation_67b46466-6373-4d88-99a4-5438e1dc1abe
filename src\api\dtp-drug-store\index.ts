import request from '@/utils/request';
import type {
  ApiResponse,
  TableDataInfo,
  EsPageInfo,
  DtpJurDrugVo,
  DtpJurDrugProductVo,
  DtpJurProductVo,
  DtpApplyVo,
  DtpAssistantAdminVo,
  DtpAssistantWriteRecordVo,
  JurisdictionDtpDrugstoreTypeEnum,
  GsJurDrugBo,
  GsJurDrugProductBo,
  DtpEmpOperateBo,
  GsApplyBpmJurDsContext,
  GsDtpDrugApplyAssignEmpBo,
  DtpDrugApplyPageBo,
  ProductQueryParams
} from './types';
import { pa } from 'element-plus/es/locale';

const BASE_PATH = '/mdm/dtp/jur-ds';

/**
 * 检查DTP药店进入授权
 */
export function checkDtpDrugstoreEntryAuth(): Promise<ApiResponse<Record<string, string>>> {
  return request({
    url: `${BASE_PATH}/auth/entry`,
    method: 'get'
  });
}

/**
 * 初始化辖区数据
 */
export function initJurisdictionData(): Promise<ApiResponse<any>> {
  return request({
    url: `${BASE_PATH}/initJurData`,
    method: 'post'
  });
}

/**
 * 根据审批流ID获取操作状态
 * @param flowId 审批流ID
 */
export function getOperationStatusApi(flowId: number): Promise<ApiResponse<JurisdictionDtpDrugstoreTypeEnum>> {
  return request({
    url: `${BASE_PATH}/operation/status?flowId=${flowId}`,
    method: 'post'
  });
}

/**
 * 获取DTP药店列表
 * @param params 查询参数
 */
export function getDtpDrugstoreList(params: GsJurDrugBo): Promise<TableDataInfo<DtpJurDrugVo>> {
  return request({
    url: `${BASE_PATH}/drug/page`,
    method: 'post',
    data: params
  });
}

/**
 * 根据编码获取DTP药店详情
 * @param jurDsCode 辖区药店编码
 */
export function getDtpDrugstoreDetail(jurDsCode: string): Promise<ApiResponse<DtpJurDrugVo>> {
  return request({
    url: `${BASE_PATH}/drug/detail`,
    method: 'post',
    params: { jurDsCode }
  });
}

/**
 * 获取操作日志
 * @param params 分页参数
 */
export function getOperationLogs(params: {
  pageSize: number;
  pageNum: number;
  orderByColumn?: string;
  isAsc?: string;
}): Promise<ApiResponse<EsPageInfo<any>>> {
  return request({
    url: `${BASE_PATH}/drug/operate/log`,
    method: 'post',
    data: params
  });
}

/**
 * 获取药店产品
 * @param params 查询参数
 */
export function getDrugstoreProducts(params: GsJurDrugProductBo): Promise<ApiResponse<DtpJurDrugProductVo[]>> {
  return request({
    url: `${BASE_PATH}/drug/product`,
    method: 'post',
    data: params
  });
}

/**
 * 获取当前用户的辖区产品
 */
export function getJurisdictionProducts(): Promise<ApiResponse<DtpJurProductVo[]>> {
  return request({
    url: `${BASE_PATH}/jur/product`,
    method: 'post'
  });
}

/**
 * 添加新的药店申请
 * @param data 申请数据
 */
export function addDrugstoreApplication(data: GsApplyBpmJurDsContext): Promise<ApiResponse<any>> {
  return request({
    url: `${BASE_PATH}/add/apply`,
    method: 'post',
    data
  });
}

/**
 * 删除药店申请
 * @param data 申请数据
 */
export function deleteDrugstoreApplication(data: GsApplyBpmJurDsContext): Promise<ApiResponse<any>> {
  return request({
    url: `${BASE_PATH}/delete/drug/apply`,
    method: 'post',
    data
  });
}

/**
 * 向药店添加产品
 * @param data 包含产品详情的申请数据
 */
export function addProductApplication(data: GsApplyBpmJurDsContext): Promise<ApiResponse<any>> {
  return request({
    url: `${BASE_PATH}/insert/product/apply`,
    method: 'post',
    data
  });
}

/**
 * 从药店删除产品
 * @param data 包含产品详情的申请数据
 */
export function deleteProductApplication(data: GsApplyBpmJurDsContext): Promise<ApiResponse<any>> {
  return request({
    url: `${BASE_PATH}/delete/product/apply`,
    method: 'post',
    data
  });
}

/**
 * DTP员工接受或拒绝任务的操作
 * @param data 操作数据
 */
export function dtpEmployeeOperation(data: DtpEmpOperateBo): Promise<ApiResponse<boolean>> {
  return request({
    url: `${BASE_PATH}/dtp/update/status`,
    method: 'post',
    data
  });
}

/**
 * DTP员工提供补充材料
 * @param data 补充数据
 */
export function dtpEmployeeWrite(data: DtpEmpOperateBo): Promise<ApiResponse<any>> {
  return request({
    url: `${BASE_PATH}/assistant/write`,
    method: 'post',
    data
  });
}

/**
 * 获取DTP助理自己的记录
 * @param flowId 审批流ID
 */
export function getAssistantRecord(flowId: number): Promise<ApiResponse<DtpAssistantWriteRecordVo>> {
  return request({
    url: `${BASE_PATH}/assign/record?flowId=${flowId}`,
    method: 'post'
  });
}

/**
 * 获取药店的所有DTP助理记录
 * @param dsCode 药店编码
 */
export function getAssistantHistory(dsCode: string): Promise<ApiResponse<DtpAssistantAdminVo[]>> {
  return request({
    url: `${BASE_PATH}/assign/history`,
    method: 'post',
    params: { dsCode }
  });
}

/**
 * 获取审批流的历史记录
 * @param dsCode string
 */
export function getAssignHistory(dsCode: string): Promise<ApiResponse<DtpAssistantAdminVo>> {
  return request({
    url: `${BASE_PATH}/assign/history`,
    method: 'post',
    params: { dsCode }
  });
}

/**
 * 获取特定审批流的所有记录
 * @param flowId 审批流ID
 */
export function getAllAssistantRecords(flowId: number): Promise<ApiResponse<DtpAssistantAdminVo>> {
  return request({
    url: `${BASE_PATH}/assign/all/record?flowId=${flowId}`,
    method: 'post'
  });
}

/**
 * 分配DTP员工到药店
 * @param data 分配数据
 */
export function assignDtpEmployee(data: GsDtpDrugApplyAssignEmpBo): Promise<ApiResponse<any>> {
  return request({
    url: `${BASE_PATH}/assign/emp`,
    method: 'post',
    data
  });
}

/**
 * 渠道经理补充标签信息
 * @param data 补充标签数据
 */
export function channelManagerSupplementTag(data: DtpEmpOperateBo): Promise<ApiResponse<boolean>> {
  return request({
    url: `${BASE_PATH}/mgr/supplement/tag`,
    method: 'post',
    data
  });
}

/**
 * 获取DTP药店申请列表
 * @param params 查询参数
 */
export function getDtpApplicationList(params: DtpDrugApplyPageBo): Promise<ApiResponse<TableDataInfo<DtpApplyVo>>> {
  return request({
    url: `${BASE_PATH}/apply/page`,
    method: 'post',
    data: params
  });
}

/**
 * 获取药店主数据列表
 */
export function getDrugstoreList(params: any): Promise<ApiResponse<TableDataInfo<any>>> {
  return request({
    url: `${BASE_PATH}/drugstore/list`,
    method: 'get',
    params
  });
}

/**
 * 获取药店主数据详情
 */
export function getDrugstoreDetail(params: any): Promise<ApiResponse<any>> {
  return request({
    url: `${BASE_PATH}/drugstore/detail`,
    method: 'get',
    params
  });
}

/**
 * 通过productCode获取品规列表
 */
export function getSpecsByProductApi(params: any): Promise<ApiResponse<any>> {
  return request({
    url: `/msr/product/list`,
    method: 'get',
    params
  });
}

/**
 * 根据审批流id获取DTP药店申请单展示状态
 * @param params 查询参数
 */
export function getApplyStatusApi(params: any): Promise<ApiResponse<any>> {
  return request({
    url: `${BASE_PATH}/apply/detail`,
    method: 'post',
    params
  });
}

/**
 * 获取当前登录人在dtp审批流里的角色
 */
export function bpmRoleApi(flowId: string): Promise<ApiResponse<any>> {
  return request({
    url: `${BASE_PATH}/bpm/role?flowId=${flowId}`,
    method: 'get'
  });
}
