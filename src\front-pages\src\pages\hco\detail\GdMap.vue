<template>
  <div class="gd-map">
    <!-- 定位信息展示区域 -->
    <div class="location-info-section">
      <el-card shadow="hover" class="location-info-card">
        <div class="location-info-header">
          <span class="location-info-title">定位信息</span>
          <el-button type="primary" size="small" @click="handleLocationCalibration"> 立即校准 </el-button>
        </div>
        <el-row :gutter="20" class="location-info-content">
          <el-col :span="6">
            <div class="info-item">
              <span class="info-label">经度：</span>
              <span class="info-value">{{ locationInfo.longitude || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <span class="info-label">纬度：</span>
              <span class="info-value">{{ locationInfo.latitude || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <span class="info-label">校验后经度：</span>
              <span class="info-value">{{ locationInfo.calibrateLongitude || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <span class="info-label">校验后纬度：</span>
              <span class="info-value">{{ locationInfo.calibrateLatitude || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>

    <!-- 地图区域 -->
    <div id="container" class="map-container"></div>
    <div id="panel"></div>
    <div v-if="searchResults.length" class="search-results">
      <el-radio-group v-model="selectedPoiId" @change="handlePoiSelect">
        <div v-for="poi in searchResults" :key="poi.id" class="result-item">
          <el-radio :label="poi.id">
            <div class="poi-info">
              <div class="poi-name">{{ poi.name }}</div>
              <div class="poi-address">{{ poi.address }}</div>
            </div>
          </el-radio>
        </div>
      </el-radio-group>
    </div>

    <!-- 定位校准弹窗 -->
    <SetAddressDialog v-model:dialogVisible="calibrationDialogVisible" @close="handleCalibrationClose" ref="calibrationDialogRef" />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, watch } from 'vue';
import SetAddressDialog from '../components/setAdressDialog.vue';

const emit = defineEmits(['select-location', 'location-updated']);

const state = reactive({
  map: null,
  placeSearch: null,
  currentMarker: null
});

const searchValue = ref('');
const searchResults = ref([]);
const selectedPoiId = ref('');
const calibrationDialogVisible = ref(false);
const calibrationDialogRef = ref(null);

// 定位信息数据
const locationInfo = ref({
  longitude: '',
  latitude: '',
  calibrateLongitude: '',
  calibrateLatitude: ''
});

const props = defineProps({
  defaultCenter: {
    type: Array,
    default: () => [116.397, 39.918]
  },
  defaultZoom: {
    type: Number,
    default: 15
  },
  search: {
    type: String,
    default: ''
  },
  // 接收机构信息
  institutionInfo: {
    type: Object,
    default: () => ({})
  }
});

// 初始化地图
const initMap = () => {
  state.map = new AMap.Map("container", {
    resizeEnable: true
  });

  // 监听地图点击事件
  // state.map.on('click', handleMapClick);

  // 初始化搜索服务
  AMap.plugin(['AMap.PlaceSearch'], () => {
    state.placeSearch = new AMap.PlaceSearch({
      pageSize: 5, // 单页显示结果条数
      pageIndex: 1, // 页码
      citylimit: true,  //是否强制限制在设置的城市内搜索
      map: state.map, // 展现结果的地图实例
      panel: "panel", // 结果列表将在此容器中进行展示。
      autoFitView: true // 是否自动调整地图视野使绘制的 Marker点都处于视口的可见范围
    });

     state.placeSearch.search(props.search);
  });
};

// 处理地图点击
const handleMapClick = (e) => {
  const lnglat = e.lnglat;
  updateMarker([lnglat.lng, lnglat.lat]);
  // 反向地理编码查询地址
  searchByLocation([lnglat.lng, lnglat.lat]);
};

// 更新标记点
const updateMarker = (position) => {
  if (state.currentMarker) {
    state.currentMarker.setPosition(position);
  } else {
    state.currentMarker = new AMap.Marker({
      map: state.map,
      position: position,
      animation: 'AMAP_ANIMATION_DROP'
    });
  }
  state.map.setCenter(position);
};

// 处理搜索
const handleSearch = () => {
  if (!searchValue.value) return;

  state.placeSearch.search(searchValue.value, (status, result) => {
    if (status === 'complete') {
      searchResults.value = result.poiList.pois;
      if (searchResults.value.length > 0) {
        const firstPoi = searchResults.value[0];
        selectedPoiId.value = firstPoi.id;
        handlePoiSelect(firstPoi.id);
      }
    } else {
      searchResults.value = [];
      ElMessage.warning('没有找到相关地点');
    }
  });
};

// 处理POI选择
const handlePoiSelect = (poiId) => {
  const selectedPoi = searchResults.value.find(poi => poi.id === poiId);
  if (selectedPoi) {
    const position = [selectedPoi.location.lng, selectedPoi.location.lat];
    updateMarker(position);
    emit('select-location', {
      id: selectedPoi.id,
      name: selectedPoi.name,
      address: selectedPoi.address,
      location: selectedPoi.location
    });
  }
};

// 根据经纬度搜索地址
const searchByLocation = (position) => {
  AMap.plugin('AMap.Geocoder', () => {
    const geocoder = new AMap.Geocoder();
    geocoder.getAddress(position, (status, result) => {
      if (status === 'complete') {
        const address = result.regeocode;
        emit('select-location', {
          name: address.formattedAddress,
          address: address.formattedAddress,
          location: {
            lng: position[0],
            lat: position[1]
          }
        });
      }
    });
  });
};

// 处理立即校准按钮点击
const handleLocationCalibration = () => {
  calibrationDialogVisible.value = true;
  // 设置机构信息到校准组件
  if (calibrationDialogRef.value && props.institutionInfo) {
    calibrationDialogRef.value.setInfo(({
      ...props.institutionInfo,
      name: props.institutionInfo?.insName
    }));
  }
};

// 处理校准弹窗关闭
const handleCalibrationClose = () => {
  calibrationDialogVisible.value = false;
  // 校准完成后，触发父组件更新数据
  emit('location-updated');
};

// 初始化定位信息
const initLocationInfo = () => {
  if (props.institutionInfo) {
    locationInfo.value = {
      longitude: props.institutionInfo.longitude || '',
      latitude: props.institutionInfo.latitude || '',
      calibrateLongitude: props.institutionInfo.calibrateLongitude || '',
      calibrateLatitude: props.institutionInfo.calibrateLatitude || ''
    };
  }
};

// 监听机构信息变化
watch(() => props.institutionInfo, (newInfo) => {
  if (newInfo) {
    initLocationInfo();
  }
}, { immediate: true, deep: true });

// 组件挂载时初始化地图
onMounted(() => {
  if (window.AMap) {
    initMap();
    initLocationInfo();
  } else {
    console.error('AMap is not loaded');
  }
});

// 组件卸载时销毁地图实例
onUnmounted(() => {
  if (state.map) {
    state.map.destroy();
  }
});
</script>

<style lang="scss" scoped>
.gd-map {
  width: 100%;
  position: relative;

  .location-info-section {
    margin-bottom: 16px;

    .location-info-card {
      :deep(.el-card__body) {
        padding: 16px 20px;
      }

      .location-info-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        .location-info-title {
          font-size: 16px;
          font-weight: 600;
          color: #1d212b;
        }
      }

      .location-info-content {
        .info-item {
          display: flex;
          align-items: center;
          margin-bottom: 8px;

          .info-label {
            color: #86909c;
            font-weight: 400;
            min-width: 80px;
          }

          .info-value {
            color: #1d212b;
            font-weight: 400;
            flex: 1;
          }
        }
      }
    }
  }

  .search-box {
    position: absolute;
    top: 10px;
    left: 10px;
    right: 10px;
    z-index: 1;

    .search-input {
      width: 100%;
      max-width: 400px;
    }
  }

  .map-container {
    width: 100%;
    height: calc(100vh - 500px);
    min-height: 300px;
  }

  .search-results {
    position: absolute;
    top: 60px;
    left: 10px;
    background: white;
    max-width: 400px;
    max-height: calc(100% - 80px);
    overflow-y: auto;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .result-item {
      padding: 12px;
      border-bottom: 1px solid #eee;

      &:last-child {
        border-bottom: none;
      }

      .poi-info {
        .poi-name {
          font-weight: bold;
          margin-bottom: 4px;
        }

        .poi-address {
          font-size: 12px;
          color: #666;
        }
      }
    }
  }
}
</style>
<style type="text/css">
#panel {
  position: absolute;
    background-color: white;
    max-height: 90%;
    overflow-y: auto;
    top: 80px;
    right: 10px;
    width: 280px;
}
</style>
