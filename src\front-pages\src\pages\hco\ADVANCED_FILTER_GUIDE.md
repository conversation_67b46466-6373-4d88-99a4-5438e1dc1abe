# HCO 高级筛选功能使用指南

## 📋 功能概述

HCO列表页面提供了强大的高级筛选功能，支持多种筛选条件的组合查询，帮助用户快速找到目标机构。

## 🎯 筛选方式

### 1. 基础筛选
在页面顶部的表单中提供基础筛选功能：
- **BU归属**: 下拉选择BU类型
- **机构名称**: 输入机构名称进行模糊搜索

### 2. 快捷筛选
提供常用的预设筛选条件，一键应用：
- **三甲医院**: 筛选机构级别为"三甲"的医院
- **二甲医院**: 筛选机构级别为"二甲"的医院
- **北京地区**: 筛选省份为"北京"的机构
- **上海地区**: 筛选省份为"上海"的机构
- **广东地区**: 筛选省份为"广东"的机构

### 3. 高级筛选
点击筛选图标 🔍 打开高级筛选面板，支持复杂的多条件组合查询。

## 🔧 高级筛选详细说明

### 支持的筛选字段

| 字段名称 | 字段说明 | 数据类型 |
|---------|----------|----------|
| 机构ID | wbId | 文本 |
| 主数据编码 | mdmCode | 文本 |
| 机构名称 | insName | 文本 |
| 机构类别 | insType | 文本 |
| 机构级别 | insGrade | 文本 |
| 机构等次 | insLevel | 文本 |
| 省 | provinceName | 文本 |
| 市 | cityName | 文本 |
| 区 | districtName | 文本 |
| 地址 | address | 文本 |
| 倍通编码 | btCode | 文本 |
| SAP编码 | sapCode | 文本 |
| U8编码 | u8Code | 文本 |
| 标准科室 | insDept | 文本 |
| 合作门诊 | hezuomenzhen | 布尔值 |
| 优质机构 | youzhijigou | 布尔值 |

### 支持的操作符

| 操作符 | 说明 | 适用字段类型 |
|--------|------|-------------|
| 包含 | 字段值包含指定内容 | 文本字段 |
| 不包含 | 字段值不包含指定内容 | 文本字段 |
| 等于 | 字段值等于指定值 | 所有字段 |
| 不等于 | 字段值不等于指定值 | 所有字段 |
| 为空 | 字段值为空 | 所有字段 |
| 不为空 | 字段值不为空 | 所有字段 |

### 使用步骤

1. **打开高级筛选面板**
   - 点击搜索框右侧的筛选图标

2. **添加筛选条件**
   - 选择要筛选的字段
   - 选择操作符
   - 输入筛选值
   - 点击"添加条件"可以添加更多筛选条件

3. **多条件组合**
   - 多个筛选条件之间是"且"的关系
   - 所有条件都必须满足才会显示结果

4. **应用筛选**
   - 点击"确定"按钮应用筛选条件
   - 点击"取消"放弃当前设置

5. **管理筛选条件**
   - 点击条件右侧的"×"删除单个条件
   - 点击"清空条件"删除所有条件

## 📊 筛选状态显示

### 当前筛选标签
页面会显示当前生效的所有筛选条件：
- 基础筛选条件以蓝色标签显示
- 高级筛选条件以灰色标签显示
- 点击标签右侧的"×"可以移除单个筛选条件
- 点击"清空全部"可以移除所有筛选条件

### 筛选计数
高级筛选按钮上会显示当前生效的高级筛选条件数量。

## 💡 使用技巧

### 1. 常用筛选组合
- **查找特定地区的三甲医院**:
  - 省份 = "北京" 且 机构级别 = "三甲"
  
- **查找包含特定科室的医院**:
  - 标准科室 包含 "心内科"
  
- **查找特定编码范围的机构**:
  - SAP编码 包含 "BJ" (查找北京地区编码)

### 2. 性能优化建议
- 优先使用精确匹配条件
- 避免过多的"包含"操作
- 合理使用快捷筛选减少复杂查询

### 3. 筛选条件保存
- 当前筛选条件会在页面刷新后保持
- 切换页面后筛选条件会重置

## ⚠️ 注意事项

1. **数据类型匹配**
   - 布尔字段只能选择"是"或"否"
   - 文本字段支持模糊匹配

2. **操作符限制**
   - 部分复杂操作符（如"不包含"、"不等于"等）可能需要前端额外处理
   - 建议优先使用"包含"和"等于"操作符

3. **性能考虑**
   - 过多的筛选条件可能影响查询性能
   - 建议合理组合筛选条件

## 🔄 与其他功能的配合

### 分页功能
- 应用筛选后会自动重置到第一页
- 分页状态会根据筛选结果更新

### 排序功能
- 筛选和排序可以同时使用
- 排序不会影响筛选条件

### 表头自定义
- 筛选功能与自定义表头功能独立
- 可以根据筛选结果调整显示列

## 📞 技术支持

如果在使用高级筛选功能时遇到问题：
1. 检查筛选条件是否正确设置
2. 确认操作符和字段类型匹配
3. 查看浏览器控制台是否有错误信息
4. 联系开发团队获取技术支持
