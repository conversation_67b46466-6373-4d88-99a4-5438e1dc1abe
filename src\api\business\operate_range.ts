import request from '@/utils/request';
import { AxiosPromise } from 'axios';

export const businessScopeListApi = (query: any) => {
  return request({
    url: `/mdm/business-scope/list`,
    method: 'get',
    params: query
  });
};
export const businessScopeApi = () => {
  return request({
    url: `/plt/dict/data/list?dictType=business_scope`,
    method: 'get'
  });
};
export const deleteBusinessScopeApi = (ids: string) => {
  return request({
    url: `/mdm/business-scope/${ids} `,
    method: 'delete'
  });
};
