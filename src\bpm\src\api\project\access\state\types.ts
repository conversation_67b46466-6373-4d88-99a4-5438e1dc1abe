export interface countVo {
  firstDirNum: number;
  naturalDirNum: number;
  temporaryNum: number;
  formalNum: number;
  emptyFdNum: number;
  rejectNum: number;
  approvalNum: number;
  insNum: number;
  yearNum: number;
  monthNum: number;
}

export interface AccessStateQuery {
  economics?: string[];
  accessStatus?: string;
  manager?: string;
  insType?: string;
  insCode?: string;
  spec?: string;
  insName?: string;
  taskStatus?: any;
  addType?: any;
  deptIds?: string[];
  accessStatuss?: string[];
  managers?: string[];
  insTypes?: string[];
  insCodes?: string[];
  specs?: string[];
  provinceCode?: string;
  cityCode?: string;
  districtCode?: string;
  projectId?: string;
  specName?: string;
  applicationTime?: any;
  statusTime?: any; // 状态生效时间
  accessContent?: string; //准入说明
  attUrl?: any;
  userId?: string;
  pageSize?: number;
  pageNum?: number;
  orderByColumn?: string;
  isAsc?: string;
  td?: string;
  originalProjectStatus?: string;
  projectStatus?: string;
  query?: {
    pageSize?: number;
    pageNum?: number;
  };
}

export interface AccessStateVO {
  projectId?: string;
  parentId?: string;
  taskId?: string;
  insId?: string;
  bpmId?: string;
  subStatus?: string;
  insCode?: string;
  insName?: string;
  insType?: string;
  provinceCode?: string;
  provinceValue?: string;
  cityCode?: string;
  cityValue?: string;
  districtCode?: string;
  districtValue?: string;
  productName?: string;
  specName?: string;
  lastFlowTime?: string;
  accessStatus?: string;
  accessStatusTime?: string;
  processStatus?: string;
  manager?: string;
}

export interface MembersVO {
  data?: {
    regionList?: [{ code: string; value: string }];
    manager?: [{ code: string; value: string }];
    roleVoList?: [{ code: string; value: string }];
  };
  regionList?: [{ code: string; value: string }];
  manager?: [{ code: string; value: string }];
  roleVoList?: [{ code: string; value: string }];
}
