import request from '@/utils/request';

export const taskTemplate = () => {
  return request({
    url: `/bdp/tmplate`,
    method: 'get'
  });
};

export const userListApi = (nickName: string) => {
  return request({
    url: `/plt/user/list?pageNum=1&pageSize=10&nickName=${nickName}`,
    method: 'get'
  });
};

export const crateProjectApi = (data: any) => {
  return request({
    url: `/bdp/prj`,
    method: 'post',
    data: data
  });
};

export const projectListApi = (data: any) => {
  return request({
    url: `/bdp/prj`,
    method: 'get',
    params: data
  });
};

export const projectMemberApi = (projectId: string) => {
  return request({
    url: `/bdp/menber/${projectId}`,
    method: 'get'
  });
};

export const startApi = (projectId: string) => {
  return request({
    url: `/bdp/prj/start/${projectId}`,
    method: 'post'
  });
};

export const taskMemberApi = (projectId: string) => {
  return request({
    url: `/bdp/menber/task/${projectId}`,
    method: 'get'
  });
};

export const projectInfoApi = (projectId: string) => {
  return request({
    url: `/bdp/prj/${projectId}`,
    method: 'get'
  });
};

export const taskProjectMemberApi = (projectId: string) => {
  return request({
    url: `/bdp/menber/task/${projectId}`,
    method: 'get'
  });
};

export const updateProjectApi = (data: any) => {
  return request({
    url: `/bdp/prj/updata`,
    method: 'put',
    data
  });
};

export const delMemberApi = (id: any) => {
  return request({
    url: `/bdp/menber/${id}`,
    method: 'put'
  });
};

export const addMemberApi = (data: any) => {
  return request({
    url: `/bdp/menber`,
    method: 'post',
    data
  });
};

export const riskApi = (data: any) => {
  return request({
    url: `/bdp/prj/risk`,
    method: 'post',
    data
  });
};

export const statisticsApi = (projectId: string) => {
  return request({
    url: `/bdp/prj/statistics/${projectId}`,
    method: 'get'
  });
};

export const endProjectApi = (data: string) => {
  return request({
    url: `/bdp/prj/end`,
    method: 'put',
    data
  });
};

export const addAttribute = (data: any) => {
  return request({
    url: `/lib/attribute`,
    method: 'post',
    data: data
  });
};
export const attributevaluePage = (query: any) => {
  return request({
    url: `/lib/attributevalue/page`,
    method: 'get',
    params: query
  });
};

export const addAttributevalue = (data: any) => {
  return request({
    url: `/lib/attributevalue`,
    method: 'post',
    data: data
  });
};

export const updateAttributevalue = (data: any) => {
  return request({
    url: `/lib/attributevalue`,
    method: 'put',
    data: data
  });
};

export const deleteAttributevalue = (id: any) => {
  return request({
    url: `/lib/attributevalue/${id}`,
    method: 'delete'
  });
};

export const getAttributeDict = () => {
  return request({
    url: `/lib/attributevalue/dict`,
    method: 'get'
  });
};

export const getGroupList = (projectId) => {
  return request({
    url: `/bdp/chat/${projectId}`,
    method: 'get'
  });
};

export const groupChat = (data: any) => {
  return request({
    url: `/bdp/chat/group/list`,
    method: 'post',
    data: data
  });
};
export const bdpUpdateChat = (data: any) => {
  return request({
    url: `/bdp/chat`,
    method: 'post',
    data: data
  });
};

// 表单提交
export const formPostApi = (data: any) => {
  return request({
    url: `/bdp/request/form`,
    method: 'post',
    data: data
  });
};

export const getAllDeptApi = () => {
  return request({
    url: `/plt/dept/all/list?parentId=0&status=0`,
    method: 'get'
  });
};

export const getAllUserApi = (nickName: string) => {
  return request({
    url: `/plt/user/all/list?pageNum=1&pageSize=20&nickName=${nickName || ''}`,
    method: 'get'
  });
};

// 获取用户创建表单列表详情流程列表（当前登入用户看到权限）:权限必须是项目负责人或者bu审核用户
export const requestDetailApi = (requestCode: string) => {
  return request({
    url: `/bdp/request/details?requestCode=${requestCode}`,
    method: 'get'
  });
};

// 提交项目表单:bu初审
export const requestBuApi = (data: any) => {
  return request({
    url: `/bdp/request/bu`,
    method: 'post',
    data
  });
};

// 提交项目表单:PMO相似性判断
export const requestSimilarityApi = (data: any) => {
  return request({
    url: `/bdp/request/similarity`,
    method: 'post',
    data
  });
};

// 提交项目表单: 系统重复性判断
export const repeatabilityApi = (data: any) => {
  return request({
    url: `/bdp/request/repeatability`,
    method: 'post',
    data
  });
};

// 提交项目表单:上传隐私文件
export const requestSecrecyApi = (data: any) => {
  return request({
    url: `/bdp/request/secrecy`,
    method: 'post',
    data
  });
};

// 获取用户创建中项目表单列表（创建中项目列表）:权限必须是项目负责人或者bu审核用户
export const requestListApi = (data: any) => {
  return request({
    url: `/bdp/request/list`,
    method: 'get',
    params: data
  });
};

// 获取项目任务角色模板列表
export const templateApi = () => {
  return request({
    url: `/bdp/tmplate`,
    method: 'get'
  });
};

// 提交项目表单:项目规划添加任务创建项目
export const requestTaskApi = (data: any) => {
  return request({
    url: `/bdp/request/task`,
    method: 'post',
    data
  });
};

// 提交项目表单:项目规划添加任务创建项目
export const tmplateHendApi = () => {
  return request({
    url: `/bdp/tmplate/hend`,
    method: 'get'
  });
};

// 项目提交隐私协议
export const prjSecrecyApi = (data: any) => {
  return request({
    url: `/bdp/prj/secrecy`,
    method: 'post',
    data
  });
};

// 获取项目成员列表
export const prjMemberApi = (projectId: string) => {
  return request({
    url: `/bdp/menber/${projectId}`,
    method: 'get'
  });
};
// 新增项目角色部门负责人
export const responsibleApi = (data: any) => {
  return request({
    url: `/bdp/responsible`,
    method: 'post',
    data
  });
};
// 新增项目角色任务负责人
export const addTaskMemberApi = (data: any) => {
  return request({
    url: `/bdp/menber`,
    method: 'post',
    data
  });
};
// 删除项目角色任务负责人
export const delTaskMember = (id: string) => {
  return request({
    url: `/bdp/menber/${id}`,
    method: 'put'
  });
};
// 项目角色任务负责人 更换
export const updateTaskMemberApi = (data: any) => {
  return request({
    url: `/bdp/menber/update`,
    method: 'post',
    data
  });
};

// 项目角色部门负责人 更换
export const updateDeptMember = (data: any) => {
  return request({
    url: `/bdp/responsible/update`,
    method: 'post',
    data
  });
};

// 项目部门角色
export const delDeptMember = (id: string) => {
  return request({
    url: `/bdp/responsible/${id}`,
    method: 'put'
  });
};

// 获取模版任务
export const getTempTaskApi = (id: string) => {
  return request({
    url: `/bdp/task/template/${id}`,
    method: 'get'
  });
};
// 获取全局字典
export const getAllDic = (params: any) => {
  return request({
    url: `/plt/dict/data/all/list`,
    method: 'get',
    params
  });
};
export const requestLogAddApi = (params: any) => {
  return request({
    url: `/bdp/log/add`,
    method: 'get',
    params
  });
};
// 判断是否需要上传保密协议
export const isProjectSecrecyApi = (params: any) => {
  return request({
    url: `/bdp/prj/isProjectSecrecy`,
    method: 'get',
    params
  });
};
// 判断跳转链接
export const linkApi = (params: any) => {
  return request({
    url: `/bdp/prj/link`,
    method: 'get',
    params
  });
};
