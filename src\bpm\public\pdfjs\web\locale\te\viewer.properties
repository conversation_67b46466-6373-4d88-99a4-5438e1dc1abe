# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=మునుపటి పేజీ
previous_label=క్రితం
next.title=తరువాత పేజీ
next_label=తరువాత

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=పేజీ
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=మొత్తం {{pagesCount}} లో
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=(మొత్తం {{pagesCount}} లో {{pageNumber}}వది)

zoom_out.title=జూమ్ తగ్గించు
zoom_out_label=జూమ్ తగ్గించు
zoom_in.title=జూమ్ చేయి
zoom_in_label=జూమ్ చేయి
zoom.title=జూమ్
presentation_mode.title=ప్రదర్శనా రీతికి మారు
presentation_mode_label=ప్రదర్శనా రీతి
open_file.title=ఫైల్ తెరువు
open_file_label=తెరువు
print.title=ముద్రించు
print_label=ముద్రించు
download.title=దింపుకోళ్ళు
download_label=దింపుకోళ్ళు
bookmark.title=ప్రస్తుత దర్శనం (కాపీ చేయి లేదా కొత్త విండోలో తెరువు)
bookmark_label=ప్రస్తుత దర్శనం

# Secondary toolbar and context menu
tools.title=పనిముట్లు
tools_label=పనిముట్లు
first_page.title=మొదటి పేజీకి వెళ్ళు
first_page.label=మొదటి పేజీకి వెళ్ళు
first_page_label=మొదటి పేజీకి వెళ్ళు
last_page.title=చివరి పేజీకి వెళ్ళు
last_page.label=చివరి పేజీకి వెళ్ళు
last_page_label=చివరి పేజీకి వెళ్ళు
page_rotate_cw.title=సవ్యదిశలో తిప్పు
page_rotate_cw.label=సవ్యదిశలో తిప్పు
page_rotate_cw_label=సవ్యదిశలో తిప్పు
page_rotate_ccw.title=అపసవ్యదిశలో తిప్పు
page_rotate_ccw.label=అపసవ్యదిశలో తిప్పు
page_rotate_ccw_label=అపసవ్యదిశలో తిప్పు

cursor_text_select_tool.title=టెక్స్ట్ ఎంపిక సాధనాన్ని ప్రారంభించండి
cursor_text_select_tool_label=టెక్స్ట్ ఎంపిక సాధనం
cursor_hand_tool.title=చేతి సాధనం చేతనించు
cursor_hand_tool_label=చేతి సాధనం

scroll_vertical_label=నిలువు స్క్రోలింగు


# Document properties dialog box
document_properties.title=పత్రము లక్షణాలు...
document_properties_label=పత్రము లక్షణాలు...
document_properties_file_name=దస్త్రం పేరు:
document_properties_file_size=దస్త్రం పరిమాణం:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KB ({{size_b}} bytes)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MB ({{size_b}} bytes)
document_properties_title=శీర్షిక:
document_properties_author=మూలకర్త:
document_properties_subject=విషయం:
document_properties_keywords=కీ పదాలు:
document_properties_creation_date=సృష్టించిన తేదీ:
document_properties_modification_date=సవరించిన తేదీ:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=సృష్టికర్త:
document_properties_producer=PDF ఉత్పాదకి:
document_properties_version=PDF వర్షన్:
document_properties_page_count=పేజీల సంఖ్య:
document_properties_page_size=కాగితం పరిమాణం:
document_properties_page_size_unit_inches=లో
document_properties_page_size_unit_millimeters=mm
document_properties_page_size_orientation_portrait=నిలువుచిత్రం
document_properties_page_size_orientation_landscape=అడ్డచిత్రం
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=లేఖ
document_properties_page_size_name_legal=చట్టపరమైన
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} × {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} × {{height}} {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized_yes=అవును
document_properties_linearized_no=కాదు
document_properties_close=మూసివేయి

print_progress_message=ముద్రించడానికి పత్రము సిద్ధమవుతున్నది…
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=రద్దుచేయి

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=పక్కపట్టీ మార్చు
toggle_sidebar_label=పక్కపట్టీ మార్చు
document_outline.title=పత్రము రూపము చూపించు (డబుల్ క్లిక్ చేసి అన్ని అంశాలను విస్తరించు/కూల్చు)
document_outline_label=పత్రము అవుట్‌లైన్
attachments.title=అనుబంధాలు చూపు
attachments_label=అనుబంధాలు
layers_label=పొరలు
thumbs.title=థంబ్‌నైల్స్ చూపు
thumbs_label=థంబ్‌నైల్స్
findbar.title=పత్రములో కనుగొనుము
findbar_label=కనుగొను

# LOCALIZATION NOTE (page_canvas): "{{page}}" will be replaced by the page number.
page_canvas=పేజి {{page}}

additional_layers=అదనపు పొరలు
# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=పేజీ {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas={{page}} పేజీ నఖచిత్రం

# Find panel button title and messages
find_input.title=కనుగొను
find_input.placeholder=పత్రములో కనుగొను…
find_previous.title=పదం యొక్క ముందు సంభవాన్ని కనుగొను
find_previous_label=మునుపటి
find_next.title=పదం యొక్క తర్వాతి సంభవాన్ని కనుగొను
find_next_label=తరువాత
find_highlight=అన్నిటిని ఉద్దీపనం చేయుము
find_match_case_label=అక్షరముల తేడాతో పోల్చు
find_entire_word_label=పూర్తి పదాలు
find_reached_top=పేజీ పైకి చేరుకున్నది, క్రింది నుండి కొనసాగించండి
find_reached_bottom=పేజీ చివరకు చేరుకున్నది, పైనుండి కొనసాగించండి
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ plural(total) ]}
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ plural(limit) ]}
find_not_found=పదబంధం కనబడలేదు

# Error panel labels
error_more_info=మరింత సమాచారం
error_less_info=తక్కువ సమాచారం
error_close=మూసివేయి
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (build: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=సందేశం: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=స్టాక్: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=ఫైలు: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=వరుస: {{line}}
rendering_error=పేజీను రెండర్ చేయుటలో ఒక దోషం ఎదురైంది.

# Predefined zoom values
page_scale_width=పేజీ వెడల్పు
page_scale_fit=పేజీ అమర్పు
page_scale_auto=స్వయంచాలక జూమ్
page_scale_actual=యథార్ధ పరిమాణం
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error_indicator=దోషం

loading_error=PDF లోడవుచున్నప్పుడు ఒక దోషం ఎదురైంది.
invalid_file_error=చెల్లని లేదా పాడైన PDF ఫైలు.
missing_file_error=దొరకని PDF ఫైలు.
unexpected_response_error=అనుకోని సర్వర్ స్పందన.

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.
annotation_date_string={{date}}, {{time}}

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 – Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} టీకా]
password_label=ఈ PDF ఫైల్ తెరుచుటకు సంకేతపదం ప్రవేశపెట్టుము.
password_invalid=సంకేతపదం చెల్లదు. దయచేసి మళ్ళీ ప్రయత్నించండి.
password_ok=సరే
password_cancel=రద్దుచేయి

printing_not_supported=హెచ్చరిక: ఈ విహారిణి చేత ముద్రణ పూర్తిగా తోడ్పాటు లేదు.
printing_not_ready=హెచ్చరిక: ముద్రణ కొరకు ఈ PDF పూర్తిగా లోడవలేదు.
web_fonts_disabled=వెబ్ ఫాంట్లు అచేతనించబడెను: ఎంబెడెడ్ PDF ఫాంట్లు ఉపయోగించలేక పోయింది.
