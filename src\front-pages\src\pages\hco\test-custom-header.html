<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自定义表头功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
        }
        .api-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .field-mapping {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 15px 0;
        }
        .field-list {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
        }
        .field-item {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .field-item:last-child {
            border-bottom: none;
        }
        .status {
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 12px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>HCO数据查询列表 - 自定义表头功能联调测试</h1>
        
        <div class="test-section">
            <div class="test-title">1. 字段映射检查</div>
            <div class="api-info">
                <strong>API接口：</strong> /data/institution/header-query (GET) 和 /data/institution/customize-header (POST)
            </div>
            
            <div class="field-mapping">
                <div class="field-list">
                    <h4>组件中定义的字段</h4>
                    <div class="field-item">
                        <span>机构ID</span>
                        <span>wbId</span>
                        <span class="status success">✓ 匹配</span>
                    </div>
                    <div class="field-item">
                        <span>机构名称</span>
                        <span>insName</span>
                        <span class="status success">✓ 匹配</span>
                    </div>
                    <div class="field-item">
                        <span>机构类别</span>
                        <span>insType</span>
                        <span class="status success">✓ 匹配</span>
                    </div>
                    <div class="field-item">
                        <span>省份</span>
                        <span>provinceName</span>
                        <span class="status success">✓ 匹配</span>
                    </div>
                    <div class="field-item">
                        <span>城市</span>
                        <span>cityName</span>
                        <span class="status success">✓ 匹配</span>
                    </div>
                    <div class="field-item">
                        <span>区县</span>
                        <span>districtName</span>
                        <span class="status success">✓ 匹配</span>
                    </div>
                    <div class="field-item">
                        <span>BU归属</span>
                        <span>institutionTagVos</span>
                        <span class="status success">✓ 匹配</span>
                    </div>
                </div>
                
                <div class="field-list">
                    <h4>API返回的MdmInstitutionVo字段</h4>
                    <div class="field-item">
                        <span>wbId</span>
                        <span>机构id</span>
                        <span class="status success">✓ 已映射</span>
                    </div>
                    <div class="field-item">
                        <span>insName</span>
                        <span>机构名称</span>
                        <span class="status success">✓ 已映射</span>
                    </div>
                    <div class="field-item">
                        <span>insType</span>
                        <span>机构类别</span>
                        <span class="status success">✓ 已映射</span>
                    </div>
                    <div class="field-item">
                        <span>provinceName</span>
                        <span>省</span>
                        <span class="status success">✓ 已映射</span>
                    </div>
                    <div class="field-item">
                        <span>cityName</span>
                        <span>市</span>
                        <span class="status success">✓ 已映射</span>
                    </div>
                    <div class="field-item">
                        <span>districtName</span>
                        <span>区</span>
                        <span class="status success">✓ 已映射</span>
                    </div>
                    <div class="field-item">
                        <span>institutionTagVos</span>
                        <span>bu归属标签</span>
                        <span class="status success">✓ 已映射</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">2. 接口集成状态</div>
            <div class="api-info">
                <div style="margin-bottom: 10px;">
                    <strong>获取表头配置：</strong> 
                    <span class="status success">✓ 已集成</span>
                    <code>useCustomHeader().fetchHeaders()</code>
                </div>
                <div style="margin-bottom: 10px;">
                    <strong>保存表头配置：</strong> 
                    <span class="status success">✓ 已集成</span>
                    <code>useCustomHeader().saveHeaders()</code>
                </div>
                <div>
                    <strong>组件初始化：</strong> 
                    <span class="status success">✓ 已集成</span>
                    <code>onMounted() -> loadHeaderConfiguration()</code>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">3. 功能改进点</div>
            <ul>
                <li><span class="status success">✓</span> 修正了字段映射，使组件字段与API字段保持一致</li>
                <li><span class="status success">✓</span> 集成了表头配置的加载和保存接口</li>
                <li><span class="status success">✓</span> 添加了组件初始化时自动加载配置的逻辑</li>
                <li><span class="status success">✓</span> 改进了提交和取消操作的处理逻辑</li>
                <li><span class="status success">✓</span> 添加了错误处理和用户反馈</li>
            </ul>
        </div>
        
        <div class="test-section">
            <div class="test-title">4. 测试步骤</div>
            <ol>
                <li>打开HCO数据查询页面</li>
                <li>点击自定义表头按钮（设置图标）</li>
                <li>调整字段选择和排序</li>
                <li>点击提交保存配置</li>
                <li>刷新页面验证配置是否保存成功</li>
                <li>测试恢复默认功能</li>
            </ol>
        </div>
        
        <div class="test-section">
            <div class="test-title">5. API数据格式</div>
            <div class="api-info">
                <strong>CustomizeHeaderVo格式：</strong>
                <pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 10px 0;">
{
  "header": "字段名称(如: wbId, insName)",
  "index": "排序索引(字符串格式)",
  "isFrozen": "是否冻结('true'或'false')"
}
                </pre>
            </div>
        </div>
    </div>
</body>
</html>
