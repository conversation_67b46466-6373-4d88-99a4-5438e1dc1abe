<template>
  <div>
    <el-popover placement="bottom" width="600" trigger="click" v-model:visible="isPopoverOpen" popper-class="custom-table-header-popover">
      <template #reference>
        <el-button plain class="!p-2">
          <el-icon><Setting /></el-icon>
        </el-button>
      </template>
      <div class="flex flex-row h-[650px] w-full bg-white">
        <!-- 左侧：搜索、全选、字段选择 -->
        <div class="w-2/5 flex flex-col pr-4 border-r h-full">
          <div class="font-bold text-base mb-3">自定义表头</div>
          <div class="flex items-center mb-2 gap-2">
            <el-input v-model="search" placeholder="搜索" clearable prefix-icon="el-icon-search" size="small" class="flex-1" />
          </div>
          <div class="flex items-center mb-2 text-xs text-gray-500">
            <el-checkbox
              v-model="allChecked"
              @change="toggleAll"
              :indeterminate="selectedFields.length > 0 && selectedFields.length < allSelectableFields.length"
              >全部选项 ({{ selectedFields.length }}/{{ allSelectableFields.length }})</el-checkbox
            >
          </div>
          <el-scrollbar class="flex-1 mb-2 max-h-[520px]">
            <el-checkbox-group v-model="selectedFields">
              <div v-for="item in filteredOptionalFields" :key="item.value" class="flex items-center py-1 px-2 rounded hover:bg-gray-100">
                <el-checkbox :label="item.value" :disabled="item.disabled">
                  <span v-html="highlightKeyword(item.label)"></span>
                </el-checkbox>
              </div>
            </el-checkbox-group>
          </el-scrollbar>
        </div>
        <!-- 右侧：冻结列/非冻结列上下排列 -->
        <div class="w-3/5 flex flex-col pl-4 h-full">
          <div class="flex items-center justify-between mb-1 mt-1">
            <div class="text-xs text-gray-600">
              已选字段：<span class="font-bold text-primary">{{ selectedFields.length }}</span>
            </div>
            <el-link type="primary" @click="resetToDefault" class="text-xs">恢复默认</el-link>
          </div>
          <div class="bg-white rounded p-2 mb-2 min-h-[32px]">
            <div class="font-bold text-xs mb-1 pt-2">冻结列</div>
            <draggable v-model="frozenFields" group="fields" item-key="value" :animation="200">
              <template #item="{element}">
                <div class="flex items-center justify-between rounded px-2 py-1 mb-1 cursor-move hover:bg-blue-50">
                  <span class="truncate text-gray-800 font-medium">:: {{ getFieldLabel(element) }}</span>
                  <el-icon class="text-gray-400 cursor-pointer ml-2" @click.stop="removeField(element)"><Close /></el-icon>
                </div>
              </template>
              <template #footer>
                <div v-if="!frozenFields.length" class="text-gray-400 text-xs text-center py-2 drag-placeholder">将字段拖动到这里</div>
              </template>
            </draggable>
          </div>
          <div class="bg-white rounded p-2 min-h-[60px] flex-1">
            <div class="font-bold text-xs mb-1 pt-2">非冻结列</div>
            <draggable v-model="nonFrozenFields" group="fields" item-key="value" :animation="200" class="flex-1">
              <template #item="{element}">
                <div class="flex items-center justify-between rounded px-2 py-1 mb-1 cursor-move hover:bg-blue-50">
                  <span class="truncate text-gray-800 font-medium">:: {{ getFieldLabel(element) }}</span>
                  <el-icon class="text-gray-400 cursor-pointer ml-2" @click.stop="removeField(element)"><Close /></el-icon>
                </div>
              </template>
              <template #footer>
                <div v-if="!nonFrozenFields.length" class="text-gray-400 text-xs text-center py-2">暂无非冻结字段</div>
              </template>
            </draggable>
          </div>
          <div class="flex justify-end gap-2 mt-4">
            <el-button size="small" @click="onCancel">取消</el-button>
            <el-button size="small" type="primary" @click="onSubmit">提交</el-button>
          </div>
        </div>
      </div>
    </el-popover>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { Close, Setting } from '@element-plus/icons-vue'
import draggable from 'vuedraggable'
import { useCustomHeader } from '@/front-pages/src/pages/hco/composables/useHco'
import { ElMessage } from 'element-plus'

// Define emits
const emit = defineEmits(['update:columns'])

// 使用自定义表头管理
const customHeaderManager = useCustomHeader()

const isPopoverOpen = ref(false)
const search = ref('')
const allChecked = ref(true)

// 字段定义 - 与API返回的MdmInstitutionVo字段保持一致
const defaultFields = [
  { label: '机构ID', value: 'wbId' },
  { label: '机构名称', value: 'insName' },
  { label: '机构类别', value: 'insType' },
  { label: '省份', value: 'provinceName' },
  { label: '城市', value: 'cityName' },
  { label: '区县', value: 'districtName' },
  { label: '地址', value: 'address' },
  { label: 'BU归属', value: 'BU归属' },
]
const optionalFields = [
  ...defaultFields,
  // { label: '机构级别', value: 'insGrade' },
  // { label: '机构等次', value: 'insLevel' },
  // { label: '倍通编码', value: 'btCode' },
  // { label: 'SAP编码', value: 'sapCode' },
  // { label: 'U8编码', value: 'u8Code' },
  { label: '标准科室', value: 'insDept' },
  { label: '合作门诊', value: '合作门诊' },
  { label: '优质机构', value: '优质机构' },
]

const selectedFields = ref(defaultFields.map(f => f.value))
const frozenFields = ref([])
const nonFrozenFields = ref(selectedFields.value.filter(f => !frozenFields.value.includes(f)))

const allSelectableFields = computed(() => optionalFields.filter(f => !f.disabled))

watch(selectedFields, (val) => {
  allChecked.value = val.length === allSelectableFields.value.length
  // 保持冻结/非冻结列和已选字段同步
  frozenFields.value = frozenFields.value.filter(f => val.includes(f))
  nonFrozenFields.value = val.filter(f => !frozenFields.value.includes(f))
})

function getFieldLabel(value) {
  const found = optionalFields.find(f => f.value === value)
  return found ? found.label : value
}

function toggleAll(val) {
  if (val) {
    selectedFields.value = allSelectableFields.value.map(f => f.value)
  } else {
    selectedFields.value = []
  }
}

function removeField(value) {
  selectedFields.value = selectedFields.value.filter(f => f !== value)
}

function resetToDefault() {
  selectedFields.value = defaultFields.map(f => f.value)
  frozenFields.value = []
  nonFrozenFields.value = selectedFields.value.filter(f => !frozenFields.value.includes(f))

  // 发送默认配置给父组件
  const columnConfig = {
    frozen: frozenFields.value.map(f => ({
      field: f,
      label: getFieldLabel(f)
    })),
    nonFrozen: nonFrozenFields.value.map(f => ({
      field: f,
      label: getFieldLabel(f)
    }))
  }
  emit('update:columns', columnConfig)
}

// 加载表头配置
async function loadHeaderConfiguration() {
  try {
    await customHeaderManager.fetchHeaders()
    const headers = customHeaderManager.headers.value

    if (headers && headers.length > 0) {
      // 按index排序
      const sortedHeaders = headers.sort((a, b) => parseInt(a.index) - parseInt(b.index))

      // 分离冻结和非冻结字段
      const frozen = sortedHeaders.filter(h => h.isFrozen === 'true').map(h => h.header)
      const nonFrozen = sortedHeaders.filter(h => h.isFrozen === 'false').map(h => h.header)

      // 更新状态
      frozenFields.value = frozen
      nonFrozenFields.value = nonFrozen
      selectedFields.value = [...frozen, ...nonFrozen]

      // 发送配置给父组件
      const columnConfig = {
        frozen: frozen.map(f => ({
          field: f,
          label: getFieldLabel(f)
        })),
        nonFrozen: nonFrozen.map(f => ({
          field: f,
          label: getFieldLabel(f)
        }))
      }
      emit('update:columns', columnConfig)
    } else {
      // 如果没有保存的配置，使用默认配置
      resetToDefault()
    }
  } catch (error) {
    console.error('加载表头配置失败:', error)
    // 出错时使用默认配置
    resetToDefault()
  }
}

// 组件挂载时加载配置
onMounted(() => {
  loadHeaderConfiguration()
})

defineExpose({ resetToDefault, loadHeaderConfiguration })

async function onSubmit() {
  try {
    // 构建自定义表头数据
    const headerData = []

    // 添加冻结列
    frozenFields.value.forEach((field, index) => {
      headerData.push({
        header: field,
        index: String(index + 1),
        isFrozen: 'true'
      })
    })

    // 添加非冻结列
    nonFrozenFields.value.forEach((field, index) => {
      headerData.push({
        header: field,
        index: String(frozenFields.value.length + index + 1),
        isFrozen: 'false'
      })
    })

    // 保存到后端
    const success = await customHeaderManager.saveHeaders(headerData)

    if (success) {
      // 创建列配置并发送给父组件
      const columnConfig = {
        frozen: frozenFields.value.map(f => ({
          field: f,
          label: getFieldLabel(f)
        })),
        nonFrozen: nonFrozenFields.value.map(f => ({
          field: f,
          label: getFieldLabel(f)
        }))
      }

      // Emit to parent component
      emit('update:columns', columnConfig)
      isPopoverOpen.value = false
    }
  } catch (error) {
    console.error('保存自定义表头失败:', error)
    ElMessage.error('保存失败，请稍后重试')
  }
}

function onCancel() {
  // 恢复到上次保存的状态或默认状态
  loadHeaderConfiguration()
  isPopoverOpen.value = false
}
const filteredOptionalFields = computed(() => {
  return allSelectableFields.value.filter(f => {
    if (!search.value) return true
    return f.label.includes(search.value)
  })
})
function highlightKeyword(label) {
  if (!search.value) return label
  return label.replace(
    new RegExp(search.value, 'gi'),
    match => `<span class='text-blue-600 font-bold'>${match}</span>`
  )
}
</script>

<style scoped>
.custom-table-header-popover {
  padding: 0 !important;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.08);
}
.el-checkbox__label {
  font-size: 14px;
}
.el-checkbox {
  margin-right: 0;
}
.drag-placeholder {
  border: 1.5px dashed #bdbdbd;
  border-radius: 6px;
  background: #fafbfc;
  margin: 8px 0;
}
</style>
