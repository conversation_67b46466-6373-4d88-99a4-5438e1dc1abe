<template>
  <div class="p-4">
    <!-- 搜索表单 -->
    <div class="bg-white p-4 mb-4 rounded shadow-sm">
      <el-form :model="queryParams" inline>
        <el-form-item label="操作申请单编号">
          <el-input v-model="queryParams.requestNumber" placeholder="请输入操作申请单编号" clearable />
        </el-form-item>
        <el-form-item label="操作人">
          <el-input v-model="queryParams.operator" placeholder="请输入操作人或操作人HRCode" clearable />
        </el-form-item>
        <el-form-item label="操作类型">
          <el-select v-model="queryParams.operationType" placeholder="请选择" clearable>
            <el-option label="DTP关联药店新增" value="DTP关联药店新增" />
            <el-option label="DTP关联药店删除" value="DTP关联药店删除" />
            <el-option label="DTP药店产品新增/删除" value="DTP药店产品新增/删除" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :loading="loading">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="bg-white p-4 rounded shadow-sm">
      <el-table v-loading="loading" :data="tableData" border stripe style="width: 100%">
        <el-table-column label="操作申请单编号" prop="unionId" width="140" />
        <el-table-column label="操作人" prop="empName" width="120" />
        <el-table-column label="操作人HRCode" prop="empCode" width="140" />
        <el-table-column label="操作类型" prop="logType" width="160" />
        <el-table-column label="操作时间" prop="logTime" width="180" />
        <el-table-column label="操作内容" prop="content" min-width="400" show-overflow-tooltip />
      </el-table>

      <!-- 分页器 -->
      <div class="flex justify-end mt-4">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { getOperationLogs } from '@/api/dtp-drug-store';

// 路由
const router = useRouter();

// 数据加载状态
const loading = ref(false);

// 总记录数
const total = ref(0);

// 查询参数
const queryParams = reactive({
  requestNumber: '',
  operator: '',
  operationType: '',
  operatorBu: '',
  pageNum: 1,
  pageSize: 10,
  orderByColumn: 'operationTime',
  isAsc: 'desc'
});

// 表格数据
const tableData = ref<any[]>([]);

// 加载操作日志数据
const loadData = async () => {
  loading.value = true;
  try {
    // 构建请求参数，添加关键字搜索字段
    const params: any = {
      pageNum: queryParams.pageNum,
      pageSize: queryParams.pageSize,
      orderByColumn: queryParams.orderByColumn,
      isAsc: queryParams.isAsc
    };

    // 添加其他查询参数
    if (queryParams.requestNumber) {
      params.requestNumber = queryParams.requestNumber;
    }
    if (queryParams.operator) {
      params.operator = queryParams.operator;
    }
    if (queryParams.operationType) {
      params.operationType = queryParams.operationType;
    }
    if (queryParams.operatorBu) {
      params.operatorBu = queryParams.operatorBu;
    }

    const res = await getOperationLogs(params);
    if (res.code === 200 && res.data) {
      // 转换API返回的数据为表格所需格式
      tableData.value = res.data.list;
      total.value = res.data.total;
    } else {
      ElMessage.error(res.msg || '获取操作日志失败');
    }
  } catch (error) {
    console.error('获取操作日志失败', error);
    ElMessage.error('获取操作日志失败');
  } finally {
    loading.value = false;
  }
};

// 查询按钮点击事件
const handleSearch = () => {
  queryParams.pageNum = 1;
  loadData();
};

// 重置查询条件
const resetQuery = () => {
  // 保留分页设置
  const { pageSize } = queryParams;
  Object.assign(queryParams, {
    requestNumber: '',
    operator: '',
    operationType: '',
    operatorBu: '',
    pageNum: 1,
    pageSize,
    orderByColumn: 'operationTime',
    isAsc: 'desc'
  });
  loadData();
};

// 分页大小变化
const handleSizeChange = (newSize: number) => {
  queryParams.pageSize = newSize;
  loadData();
};

// 页码变化
const handleCurrentChange = (newPage: number) => {
  queryParams.pageNum = newPage;
  loadData();
};

// 返回上一页
const goBack = () => {
  router.go(-1);
};

// 初始化
onMounted(() => {
  loadData();
});
</script>

<style scoped>
.el-form-item {
  margin-bottom: 10px;
}
</style>
