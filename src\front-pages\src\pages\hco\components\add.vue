<template>
  <div class="insDetail">
    <div class="p-2">
      <div class="card-block">
        <el-card shadow="hover">
          <div style="padding-bottom: 100px">
            <DetailInformation type="add" ref="detailInformationRef" @updateButton="updateButton" @butLoading="butLoading" />
          </div>

          <el-row style="position: absolute;bottom: 0;right: 0;background-color: #fff;width: 100%;height: 70px">
            <el-divider style="margin: 0;margin-top: 2px;" />

            <div class="top-right-btn">
              <el-row>
                <el-col :span="1.5" @click="canceButton">
                  <el-button type="info" plain> 取消 </el-button>
                </el-col>
                <el-col :span="1.5" style="margin-right: 20px;margin-left:10px">
                  <el-button type="primary" @click="insUpdate" :loading="state.btnLoading"> 确认 </el-button>
                </el-col>
              </el-row>
            </div>
          </el-row>
        </el-card>
      </div>
    </div>
  </div>
</template>
<script setup>
import DetailInformation from './detailInformation';
import { usedDstrictManagementStore } from '@/store/modules/districtManagement';
const { proxy } = getCurrentInstance();
const emits = defineEmits(['close']);

const dstrictManagement = usedDstrictManagementStore();
const router = useRouter();
const detailInformationRef = ref(null);
const state = reactive({
  activeName: '1',
  edit: false,
  id: dstrictManagement.insId,
  info: {},
  btnLoading: false
});
// const getInstitutionDetail = async () => {
//   const res = await institutionDetail(state.id);
//   state.info = res.data;
// };
const editJur = () => {
  detailInformationRef.value.editStatus();
};
const canceButton = () => {
  detailInformationRef.value.setInfo({});

  emits('close');
  // state.activeName = '1';
  // detailInformationRef.value.handelCancel();
};
const resetFields = () => {
  detailInformationRef.value.resetFields();
};
const insUpdate = () => {
  detailInformationRef.value.insDetailUpdate();
};
const updateButton = () => {
  emits('close');

  // getInstitutionDetail();
};
const butLoading = (val) => {
  state.btnLoading = val;
};
onMounted(() => {
  editJur();
  // getInstitutionDetail();
});
defineExpose({
  resetFields
});
</script>
<style lang="scss" scoped>
.p-2 {
  padding: 0;
}
.top-right-btn {
  margin-top: 14px;
		margin-bottom: 16px;
}
.insDetail {
  font-size: 14px;
  // overflow: auto;
  .card-block {
    padding: 0;
  }
  .top {
    display: flex;
    .icons {
      display: inline-block;
      vertical-align: middle;
      width: 58px;
      height: 58px;
      margin-right: 8px;
    }
  }
  .top-right {
    .hospital {
      font-size: 18px;
      font-weight: 600;
    }
  }
  :deep(.el-tag) {
    border-width: 0;
  }
  :deep(.el-form-item__label) {
    color: #86909c;
  }
}
</style>
