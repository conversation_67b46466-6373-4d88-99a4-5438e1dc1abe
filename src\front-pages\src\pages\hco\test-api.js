/**
 * HCO自定义表头API测试脚本
 * 用于验证接口调用是否正常
 */

// 模拟测试数据
const testHeaderData = [
  {
    header: 'wbId',
    index: '1',
    isFrozen: 'true'
  },
  {
    header: 'insName',
    index: '2',
    isFrozen: 'false'
  },
  {
    header: 'insType',
    index: '3',
    isFrozen: 'false'
  },
  {
    header: 'provinceName',
    index: '4',
    isFrozen: 'false'
  },
  {
    header: 'cityName',
    index: '5',
    isFrozen: 'false'
  }
];

/**
 * 测试保存自定义表头配置
 */
async function testSaveHeaders() {
  console.log('🧪 测试保存自定义表头配置...');
  
  try {
    const response = await fetch('/data/institution/customize-header', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testHeaderData)
    });
    
    const result = await response.json();
    
    if (result.code === 0) {
      console.log('✅ 保存成功:', result);
      return true;
    } else {
      console.log('❌ 保存失败:', result.msg);
      return false;
    }
  } catch (error) {
    console.error('❌ 保存请求失败:', error);
    return false;
  }
}

/**
 * 测试获取自定义表头配置
 */
async function testFetchHeaders() {
  console.log('🧪 测试获取自定义表头配置...');
  
  try {
    const response = await fetch('/data/institution/header-query', {
      method: 'GET'
    });
    
    const result = await response.json();
    
    if (result.code === 0) {
      console.log('✅ 获取成功:', result.data);
      return result.data;
    } else {
      console.log('❌ 获取失败:', result.msg);
      return null;
    }
  } catch (error) {
    console.error('❌ 获取请求失败:', error);
    return null;
  }
}

/**
 * 测试HCO列表查询
 */
async function testHcoQuery() {
  console.log('🧪 测试HCO列表查询...');
  
  const queryData = {
    conditions: [
      {
        field: 'insName',
        operator: 'contains',
        value: '医院'
      }
    ],
    logic: 'and',
    pageNum: 1,
    pageSize: 10
  };
  
  try {
    const response = await fetch('/data/institution/page', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(queryData)
    });
    
    const result = await response.json();
    
    if (result.code === 200) {
      console.log('✅ 查询成功，返回', result.total, '条记录');
      console.log('📋 数据字段:', result.rows.length > 0 ? Object.keys(result.rows[0]) : '无数据');
      return result;
    } else {
      console.log('❌ 查询失败:', result.msg);
      return null;
    }
  } catch (error) {
    console.error('❌ 查询请求失败:', error);
    return null;
  }
}

/**
 * 运行完整测试
 */
async function runFullTest() {
  console.log('🚀 开始HCO自定义表头功能测试...\n');
  
  // 1. 测试HCO数据查询
  console.log('=== 步骤1: 测试HCO数据查询 ===');
  const hcoData = await testHcoQuery();
  console.log('');
  
  // 2. 测试保存表头配置
  console.log('=== 步骤2: 测试保存表头配置 ===');
  const saveSuccess = await testSaveHeaders();
  console.log('');
  
  // 3. 测试获取表头配置
  console.log('=== 步骤3: 测试获取表头配置 ===');
  const headerConfig = await testFetchHeaders();
  console.log('');
  
  // 4. 验证配置一致性
  console.log('=== 步骤4: 验证配置一致性 ===');
  if (saveSuccess && headerConfig) {
    const isConsistent = JSON.stringify(testHeaderData) === JSON.stringify(headerConfig);
    if (isConsistent) {
      console.log('✅ 配置一致性验证通过');
    } else {
      console.log('❌ 配置一致性验证失败');
      console.log('发送的数据:', testHeaderData);
      console.log('返回的数据:', headerConfig);
    }
  } else {
    console.log('⚠️  无法验证配置一致性（保存或获取失败）');
  }
  
  console.log('\n🏁 测试完成！');
}

// 导出测试函数
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testSaveHeaders,
    testFetchHeaders,
    testHcoQuery,
    runFullTest
  };
}

// 如果在浏览器环境中直接运行
if (typeof window !== 'undefined') {
  window.hcoApiTest = {
    testSaveHeaders,
    testFetchHeaders,
    testHcoQuery,
    runFullTest
  };
  
  console.log('🔧 HCO API测试工具已加载');
  console.log('使用方法: window.hcoApiTest.runFullTest()');
}
