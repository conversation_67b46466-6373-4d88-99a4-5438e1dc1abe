---
title: athena-cloud
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# athena-cloud

Base URLs:

# Authentication

# 金项链/athena-mdm/DTP 药店关联渠道

<a id="opIdoperationStatus"></a>

## POST 根据审批流 id 查询二审需要展示的操作状态

POST /mdm/dtp/jur-ds/operation/status

根据审批流 id 查询二审需要展示的操作状态

### 请求参数

| 名称          | 位置   | 类型           | 必选 | 说明 |
| ------------- | ------ | -------------- | ---- | ---- |
| flowId        | query  | integer(int64) | 是   | none |
| Authorization | header | string         | 否   | none |
| clientId      | header | string         | 否   | none |

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": "JURISDICTION_DTP_DRUGSTORE_INSERT"
}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型                                                                      |
| ------ | ------------------------------------------------------- | ---- | ----------------------------------------------------------------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | none | [RJurisdictionDtpDrugstoreTypeEnum](#schemarjurisdictiondtpdrugstoretypeenum) |

<a id="opIdchannelMgrSupplementTag"></a>

## POST 渠道负责人再次补充审核资料的标签

POST /mdm/dtp/jur-ds/mgr/supplement/tag

渠道负责人再次补充审核资料的标签

> Body 请求参数

```json
{
  "flowId": 0,
  "status": "string",
  "refuseReason": "string",
  "ossIdList": [
    "string"
  ],
  "tagList": [
    {}
  ],
  "drugSocreList": [
    {
      "scoreItemName": "string",
      "scoreItem": [
        {
          "scorePointsTitle": "string",
          "score": "string",
          "inputScore": "string"
        }
      ]
    }
  ]
}
```

### 请求参数

| 名称          | 位置   | 类型                                      | 必选 | 说明 |
| ------------- | ------ | ----------------------------------------- | ---- | ---- |
| Authorization | header | string                                    | 否   | none |
| clientId      | header | string                                    | 否   | none |
| body          | body   | [DtpEmpOperateBo](#schemadtpempoperatebo) | 否   | none |

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": true
}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型                    |
| ------ | ------------------------------------------------------- | ---- | --------------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | none | [RBoolean](#schemarboolean) |

<a id="opIdinsertProductApply"></a>

## POST PC 药店新增产品

POST /mdm/dtp/jur-ds/insert/product/apply

PC 药店新增产品

> Body 请求参数

```json
{
  "appCode": "string",
  "tenantId": "string",
  "postIdList": [
    0
  ],
  "enableWorkflow": true,
  "applyType": "string",
  "processId": "string",
  "applyTime": "2019-08-24T14:15:22Z",
  "applicant": "string",
  "applicantCode": "string",
  "postCode": "string",
  "postName": "string",
  "deptCode": "string",
  "deptName": "string",
  "ancestors": "string",
  "applyContent": {
    "createBy": 0,
    "createTime": "2019-08-24T14:15:22Z",
    "updateBy": 0,
    "updateTime": "2019-08-24T14:15:22Z",
    "params": {
      "property1": {},
      "property2": {}
    },
    "appName": "string",
    "dsCode": "string",
    "dsName": "string",
    "dsMdmCode": "string",
    "province": "string",
    "provinceCode": "string",
    "city": "string",
    "cityCode": "string",
    "district": "string",
    "districtCode": "string",
    "specList": [
      {
        "mdmCode": "string",
        "productCode": "string",
        "productName": "string",
        "specCode": "string",
        "specName": "string"
      }
    ],
    "createdEmpName": "string",
    "fileList": [
      {
        "id": "string",
        "ossId": "string",
        "url": "string",
        "aeFileId": "string",
        "size": "string",
        "mime_type": "string",
        "name": "string"
      }
    ],
    "jurCode": "string",
    "jurType": "string",
    "bu": "string",
    "address": "string"
  },
  "appUrl": "string",
  "pcUrl": "string",
  "applyNodeInfo": [
    {
      "bpmNode": {
        "property1": "string",
        "property2": "string"
      }
    }
  ],
  "jurCode": "string",
  "dsCode": "string",
  "dsName": "string",
  "bu": "string",
  "jurDsCode": "string",
  "dsMdmCode": "string"
}
```

### 请求参数

| 名称          | 位置   | 类型                                                                                      | 必选 | 说明 |
| ------------- | ------ | ----------------------------------------------------------------------------------------- | ---- | ---- |
| Authorization | header | string                                                                                    | 否   | none |
| clientId      | header | string                                                                                    | 否   | none |
| body          | body   | [GsApplyBpmJurDsContextGsApplyBpmJurDsBo](#schemagsapplybpmjurdscontextgsapplybpmjurdsbo) | 否   | none |

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {}
}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型      |
| ------ | ------------------------------------------------------- | ---- | ------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | none | [R](#schemar) |

<a id="opIdinitJurData"></a>

## POST 初始化辖区

POST /mdm/dtp/jur-ds/initJurData

初始化辖区

### 请求参数

| 名称          | 位置   | 类型   | 必选 | 说明 |
| ------------- | ------ | ------ | ---- | ---- |
| Authorization | header | string | 否   | none |
| clientId      | header | string | 否   | none |

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {}
}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型      |
| ------ | ------------------------------------------------------- | ---- | ------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | none | [R](#schemar) |

<a id="opIddtpEmpOperateStatus"></a>

## POST dtp 专员接受或者拒绝

POST /mdm/dtp/jur-ds/dtp/update/status

dtp 专员接受或者拒绝

> Body 请求参数

```json
{
  "flowId": 0,
  "status": "string",
  "refuseReason": "string",
  "ossIdList": [
    "string"
  ],
  "tagList": [
    {}
  ],
  "drugSocreList": [
    {
      "scoreItemName": "string",
      "scoreItem": [
        {
          "scorePointsTitle": "string",
          "score": "string",
          "inputScore": "string"
        }
      ]
    }
  ]
}
```

### 请求参数

| 名称          | 位置   | 类型                                      | 必选 | 说明 |
| ------------- | ------ | ----------------------------------------- | ---- | ---- |
| Authorization | header | string                                    | 否   | none |
| clientId      | header | string                                    | 否   | none |
| body          | body   | [DtpEmpOperateBo](#schemadtpempoperatebo) | 否   | none |

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": true
}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型                    |
| ------ | ------------------------------------------------------- | ---- | --------------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | none | [RBoolean](#schemarboolean) |

<a id="opIdgetJurDsProduct"></a>

## POST 获取 DTP 药店绑定的产品数据

POST /mdm/dtp/jur-ds/drug/product

获取 DTP 药店绑定的产品数据

> Body 请求参数

```json
{
  "jurDsCode": "string",
  "productCode": "string",
  "specCode": "string",
  "bu": "string",
  "applicant": "string"
}
```

### 请求参数

| 名称          | 位置   | 类型                                            | 必选 | 说明 |
| ------------- | ------ | ----------------------------------------------- | ---- | ---- |
| Authorization | header | string                                          | 否   | none |
| clientId      | header | string                                          | 否   | none |
| body          | body   | [GsJurDrugProductBo](#schemagsjurdrugproductbo) | 否   | none |

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "jurDsCode": "string",
      "jurDsSpecCode": "string",
      "productCode": "string",
      "productName": "string",
      "specCode": "string",
      "specName": "string",
      "applicant": "string",
      "applicantCode": "string",
      "applicantDept": "string",
      "bu": "string",
      "ancestors": "string",
      "deptName": "string"
    }
  ]
}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型                                                    |
| ------ | ------------------------------------------------------- | ---- | ----------------------------------------------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | none | [RListDtpJurDrugProductVo](#schemarlistdtpjurdrugproductvo) |

<a id="opIdpage"></a>

## POST DTP 药店管理列表

POST /mdm/dtp/jur-ds/drug/page

DTP 药店管理列表

> Body 请求参数

```json
{
  "pageSize": 0,
  "pageNum": 0,
  "orderByColumn": "string",
  "isAsc": "string",
  "keyword": "string",
  "viewAdmin": true,
  "userId": 0
}
```

### 请求参数

| 名称          | 位置   | 类型                              | 必选 | 说明 |
| ------------- | ------ | --------------------------------- | ---- | ---- |
| Authorization | header | string                            | 否   | none |
| clientId      | header | string                            | 否   | none |
| body          | body   | [GsJurDrugBo](#schemagsjurdrugbo) | 否   | none |

> 返回示例

> 200 Response

```json
{
  "total": 0,
  "rows": [
    {
      "id": "string",
      "jurDsCode": "string",
      "drugMdmCode": "string",
      "drugName": "string",
      "drugProvince": "string",
      "drugCity": "string",
      "drugDistrict": "string",
      "lastTime": "2019-08-24T14:15:22Z",
      "fileList": [
        {
          "id": "string",
          "ossId": "string",
          "url": "string",
          "aeFileId": "string",
          "size": "string",
          "mime_type": "string",
          "name": "string"
        }
      ],
      "drugCode": "string"
    }
  ],
  "code": 0,
  "msg": "string"
}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型                                                      |
| ------ | ------------------------------------------------------- | ---- | ------------------------------------------------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | none | [TableDataInfoDtpJurDrugVo](#schematabledatainfodtpjurdrugvo) |

<a id="opIdlog"></a>

## POST 获取 DTP 药店操作记录

POST /mdm/dtp/jur-ds/drug/operate/log

获取 DTP 药店操作记录

### 请求参数

| 名称          | 位置   | 类型           | 必选 | 说明                     |
| ------------- | ------ | -------------- | ---- | ------------------------ |
| pageSize      | query  | integer(int32) | 否   | 分页大小                 |
| pageNum       | query  | integer(int32) | 否   | 当前页数                 |
| orderByColumn | query  | string         | 否   | 排序列                   |
| isAsc         | query  | string         | 否   | 排序的方向 desc 或者 asc |
| Authorization | header | string         | 否   | none                     |
| clientId      | header | string         | 否   | none                     |

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "total": 0,
    "list": [
      {}
    ],
    "pageNum": 0,
    "pageSize": 0,
    "size": 0,
    "startRow": 0,
    "endRow": 0,
    "pages": 0,
    "prePage": 0,
    "nextPage": 0,
    "hasPreviousPage": true,
    "hasNextPage": true,
    "navigatePages": 0,
    "navigatePageNums": [
      0
    ],
    "navigateFirstPage": 0,
    "navigateLastPage": 0,
    "firstPage": true,
    "lastPage": true
  }
}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型                                      |
| ------ | ------------------------------------------------------- | ---- | --------------------------------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | none | [REsPageInfoObject](#schemarespageinfoobject) |

<a id="opIdgetJurDsDetail"></a>

## POST DTP 药店详情

POST /mdm/dtp/jur-ds/drug/detail

DTP 药店详情

### 请求参数

| 名称          | 位置   | 类型   | 必选 | 说明 |
| ------------- | ------ | ------ | ---- | ---- |
| jurDsCode     | query  | string | 是   | none |
| Authorization | header | string | 否   | none |
| clientId      | header | string | 否   | none |

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "id": "string",
    "jurDsCode": "string",
    "drugMdmCode": "string",
    "drugName": "string",
    "drugProvince": "string",
    "drugCity": "string",
    "drugDistrict": "string",
    "lastTime": "2019-08-24T14:15:22Z",
    "fileList": [
      {
        "id": "string",
        "ossId": "string",
        "url": "string",
        "aeFileId": "string",
        "size": "string",
        "mime_type": "string",
        "name": "string"
      }
    ],
    "drugCode": "string"
  }
}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型                              |
| ------ | ------------------------------------------------------- | ---- | ------------------------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | none | [RDtpJurDrugVo](#schemardtpjurdrugvo) |

<a id="opIddeleteProductApply"></a>

## POST PC 药店删除产品

POST /mdm/dtp/jur-ds/delete/product/apply

PC 药店删除产品

> Body 请求参数

```json
{
  "appCode": "string",
  "tenantId": "string",
  "postIdList": [
    0
  ],
  "enableWorkflow": true,
  "applyType": "string",
  "processId": "string",
  "applyTime": "2019-08-24T14:15:22Z",
  "applicant": "string",
  "applicantCode": "string",
  "postCode": "string",
  "postName": "string",
  "deptCode": "string",
  "deptName": "string",
  "ancestors": "string",
  "applyContent": {
    "createBy": 0,
    "createTime": "2019-08-24T14:15:22Z",
    "updateBy": 0,
    "updateTime": "2019-08-24T14:15:22Z",
    "params": {
      "property1": {},
      "property2": {}
    },
    "appName": "string",
    "dsCode": "string",
    "dsName": "string",
    "dsMdmCode": "string",
    "province": "string",
    "provinceCode": "string",
    "city": "string",
    "cityCode": "string",
    "district": "string",
    "districtCode": "string",
    "specList": [
      {
        "mdmCode": "string",
        "productCode": "string",
        "productName": "string",
        "specCode": "string",
        "specName": "string"
      }
    ],
    "createdEmpName": "string",
    "fileList": [
      {
        "id": "string",
        "ossId": "string",
        "url": "string",
        "aeFileId": "string",
        "size": "string",
        "mime_type": "string",
        "name": "string"
      }
    ],
    "jurCode": "string",
    "jurType": "string",
    "bu": "string",
    "address": "string"
  },
  "appUrl": "string",
  "pcUrl": "string",
  "applyNodeInfo": [
    {
      "bpmNode": {
        "property1": "string",
        "property2": "string"
      }
    }
  ],
  "jurCode": "string",
  "dsCode": "string",
  "dsName": "string",
  "bu": "string",
  "jurDsCode": "string",
  "dsMdmCode": "string"
}
```

### 请求参数

| 名称          | 位置   | 类型                                                                                      | 必选 | 说明 |
| ------------- | ------ | ----------------------------------------------------------------------------------------- | ---- | ---- |
| Authorization | header | string                                                                                    | 否   | none |
| clientId      | header | string                                                                                    | 否   | none |
| body          | body   | [GsApplyBpmJurDsContextGsApplyBpmJurDsBo](#schemagsapplybpmjurdscontextgsapplybpmjurdsbo) | 否   | none |

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {}
}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型      |
| ------ | ------------------------------------------------------- | ---- | ------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | none | [R](#schemar) |

<a id="opIddeleteDrugApply"></a>

## POST PC 删除药店申请

POST /mdm/dtp/jur-ds/delete/drug/apply

PC 删除药店申请

> Body 请求参数

```json
{
  "appCode": "string",
  "tenantId": "string",
  "postIdList": [
    0
  ],
  "enableWorkflow": true,
  "applyType": "string",
  "processId": "string",
  "applyTime": "2019-08-24T14:15:22Z",
  "applicant": "string",
  "applicantCode": "string",
  "postCode": "string",
  "postName": "string",
  "deptCode": "string",
  "deptName": "string",
  "ancestors": "string",
  "applyContent": {
    "createBy": 0,
    "createTime": "2019-08-24T14:15:22Z",
    "updateBy": 0,
    "updateTime": "2019-08-24T14:15:22Z",
    "params": {
      "property1": {},
      "property2": {}
    },
    "appName": "string",
    "dsCode": "string",
    "dsName": "string",
    "dsMdmCode": "string",
    "province": "string",
    "provinceCode": "string",
    "city": "string",
    "cityCode": "string",
    "district": "string",
    "districtCode": "string",
    "specList": [
      {
        "mdmCode": "string",
        "productCode": "string",
        "productName": "string",
        "specCode": "string",
        "specName": "string"
      }
    ],
    "createdEmpName": "string",
    "fileList": [
      {
        "id": "string",
        "ossId": "string",
        "url": "string",
        "aeFileId": "string",
        "size": "string",
        "mime_type": "string",
        "name": "string"
      }
    ],
    "jurCode": "string",
    "jurType": "string",
    "bu": "string",
    "address": "string"
  },
  "appUrl": "string",
  "pcUrl": "string",
  "applyNodeInfo": [
    {
      "bpmNode": {
        "property1": "string",
        "property2": "string"
      }
    }
  ],
  "jurCode": "string",
  "dsCode": "string",
  "dsName": "string",
  "bu": "string",
  "jurDsCode": "string",
  "dsMdmCode": "string"
}
```

### 请求参数

| 名称          | 位置   | 类型                                                                                      | 必选 | 说明 |
| ------------- | ------ | ----------------------------------------------------------------------------------------- | ---- | ---- |
| Authorization | header | string                                                                                    | 否   | none |
| clientId      | header | string                                                                                    | 否   | none |
| body          | body   | [GsApplyBpmJurDsContextGsApplyBpmJurDsBo](#schemagsapplybpmjurdscontextgsapplybpmjurdsbo) | 否   | none |

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {}
}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型      |
| ------ | ------------------------------------------------------- | ---- | ------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | none | [R](#schemar) |

<a id="opIddtpEmpWrite"></a>

## POST dtp 专员填写补充资料

POST /mdm/dtp/jur-ds/assistant/write

dtp 专员填写补充资料

> Body 请求参数

```json
{
  "flowId": 0,
  "status": "string",
  "refuseReason": "string",
  "ossIdList": [
    "string"
  ],
  "tagList": [
    {}
  ],
  "drugSocreList": [
    {
      "scoreItemName": "string",
      "scoreItem": [
        {
          "scorePointsTitle": "string",
          "score": "string",
          "inputScore": "string"
        }
      ]
    }
  ]
}
```

### 请求参数

| 名称          | 位置   | 类型                                      | 必选 | 说明 |
| ------------- | ------ | ----------------------------------------- | ---- | ---- |
| Authorization | header | string                                    | 否   | none |
| clientId      | header | string                                    | 否   | none |
| body          | body   | [DtpEmpOperateBo](#schemadtpempoperatebo) | 否   | none |

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {}
}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型      |
| ------ | ------------------------------------------------------- | ---- | ------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | none | [R](#schemar) |

<a id="opIdgetAssistantRecord"></a>

## POST dtp 专员查看自己填写的资料信息

POST /mdm/dtp/jur-ds/assign/record

dtp 专员查看自己填写的资料信息

### 请求参数

| 名称          | 位置   | 类型           | 必选 | 说明 |
| ------------- | ------ | -------------- | ---- | ---- |
| flowId        | query  | integer(int64) | 是   | none |
| Authorization | header | string         | 否   | none |
| clientId      | header | string         | 否   | none |

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "flowId": 0,
    "status": "string",
    "refuseReason": "string",
    "ossIdList": [
      "string"
    ],
    "tagList": [
      {}
    ],
    "drugSocreList": [
      {
        "scoreItemName": "string",
        "scoreItem": [
          {
            "scorePointsTitle": null,
            "score": null,
            "inputScore": null
          }
        ]
      }
    ]
  }
}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型                                                        |
| ------ | ------------------------------------------------------- | ---- | --------------------------------------------------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | none | [RDtpAssistantWriteRecordVo](#schemardtpassistantwriterecordvo) |

<a id="opIdgetAssistantHistory"></a>

## POST 根据药店编码查询 dtp 填写的资料历史记录 商务和渠道负责人查看

POST /mdm/dtp/jur-ds/assign/history

根据药店编码查询 dtp 填写的资料历史记录 商务和渠道负责人查看

### 请求参数

| 名称          | 位置   | 类型   | 必选 | 说明 |
| ------------- | ------ | ------ | ---- | ---- |
| dsCode        | query  | string | 是   | none |
| Authorization | header | string | 否   | none |
| clientId      | header | string | 否   | none |

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "flowId": 0,
      "tagList": [
        {}
      ],
      "scoreAvg": 0,
      "assistantWriteList": [
        {
          "empCode": "string",
          "empName": "string",
          "ossIdList": [
            "string"
          ],
          "drugScore": [
            {}
          ]
        }
      ]
    }
  ]
}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型                                                    |
| ------ | ------------------------------------------------------- | ---- | ----------------------------------------------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | none | [RListDtpAssistantAdminVo](#schemarlistdtpassistantadminvo) |

<a id="opIdassignDtpEmp"></a>

## POST 渠道负责人申请药店指派专员

POST /mdm/dtp/jur-ds/assign/emp

渠道负责人申请药店指派专员

> Body 请求参数

```json
{
  "flowId": 0,
  "assignEmpBoList": [
    {
      "empCode": "string",
      "empName": "string"
    }
  ]
}
```

### 请求参数

| 名称          | 位置   | 类型                                                          | 必选 | 说明 |
| ------------- | ------ | ------------------------------------------------------------- | ---- | ---- |
| Authorization | header | string                                                        | 否   | none |
| clientId      | header | string                                                        | 否   | none |
| body          | body   | [GsDtpDrugApplyAssignEmpBo](#schemagsdtpdrugapplyassignempbo) | 否   | none |

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {}
}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型      |
| ------ | ------------------------------------------------------- | ---- | ------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | none | [R](#schemar) |

<a id="opIdgetAssistantAllRecord"></a>

## POST 根据审批流查看专员上传的资料信息，商务和渠道负责人查看

POST /mdm/dtp/jur-ds/assign/all/record

根据审批流查看专员上传的资料信息，商务和渠道负责人查看

### 请求参数

| 名称          | 位置   | 类型           | 必选 | 说明 |
| ------------- | ------ | -------------- | ---- | ---- |
| flowId        | query  | integer(int64) | 是   | none |
| Authorization | header | string         | 否   | none |
| clientId      | header | string         | 否   | none |

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "flowId": 0,
    "tagList": [
      {}
    ],
    "scoreAvg": 0,
    "assistantWriteList": [
      {
        "empCode": "string",
        "empName": "string",
        "ossIdList": [
          "string"
        ],
        "drugScore": [
          {
            "scoreItemName": null,
            "scoreItem": null
          }
        ]
      }
    ]
  }
}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型                                            |
| ------ | ------------------------------------------------------- | ---- | --------------------------------------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | none | [RDtpAssistantAdminVo](#schemardtpassistantadminvo) |

<a id="opIdadd_2"></a>

## POST PC 新增关联药店

POST /mdm/dtp/jur-ds/add/apply

PC 新增关联药店

> Body 请求参数

```json
{
  "appCode": "string",
  "tenantId": "string",
  "postIdList": [
    0
  ],
  "enableWorkflow": true,
  "applyType": "string",
  "processId": "string",
  "applyTime": "2019-08-24T14:15:22Z",
  "applicant": "string",
  "applicantCode": "string",
  "postCode": "string",
  "postName": "string",
  "deptCode": "string",
  "deptName": "string",
  "ancestors": "string",
  "applyContent": {
    "createBy": 0,
    "createTime": "2019-08-24T14:15:22Z",
    "updateBy": 0,
    "updateTime": "2019-08-24T14:15:22Z",
    "params": {
      "property1": {},
      "property2": {}
    },
    "appName": "string",
    "dsCode": "string",
    "dsName": "string",
    "dsMdmCode": "string",
    "province": "string",
    "provinceCode": "string",
    "city": "string",
    "cityCode": "string",
    "district": "string",
    "districtCode": "string",
    "specList": [
      {
        "mdmCode": "string",
        "productCode": "string",
        "productName": "string",
        "specCode": "string",
        "specName": "string"
      }
    ],
    "createdEmpName": "string",
    "fileList": [
      {
        "id": "string",
        "ossId": "string",
        "url": "string",
        "aeFileId": "string",
        "size": "string",
        "mime_type": "string",
        "name": "string"
      }
    ],
    "jurCode": "string",
    "jurType": "string",
    "bu": "string",
    "address": "string"
  },
  "appUrl": "string",
  "pcUrl": "string",
  "applyNodeInfo": [
    {
      "bpmNode": {
        "property1": "string",
        "property2": "string"
      }
    }
  ],
  "jurCode": "string",
  "dsCode": "string",
  "dsName": "string",
  "bu": "string",
  "jurDsCode": "string",
  "dsMdmCode": "string"
}
```

### 请求参数

| 名称          | 位置   | 类型                                                                                      | 必选 | 说明 |
| ------------- | ------ | ----------------------------------------------------------------------------------------- | ---- | ---- |
| Authorization | header | string                                                                                    | 否   | none |
| clientId      | header | string                                                                                    | 否   | none |
| body          | body   | [GsApplyBpmJurDsContextGsApplyBpmJurDsBo](#schemagsapplybpmjurdscontextgsapplybpmjurdsbo) | 否   | none |

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {}
}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型      |
| ------ | ------------------------------------------------------- | ---- | ------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | none | [R](#schemar) |

<a id="opIdcheckEntryAuth_1"></a>

## GET DTP 药店申请权限

GET /mdm/dtp/jur-ds/auth/entry

DTP 药店申请权限

### 请求参数

| 名称          | 位置   | 类型   | 必选 | 说明 |
| ------------- | ------ | ------ | ---- | ---- |
| Authorization | header | string | 否   | none |
| clientId      | header | string | 否   | none |

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "property1": "string",
    "property2": "string"
  }
}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型                                    |
| ------ | ------------------------------------------------------- | ---- | ------------------------------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | none | [RMapStringString](#schemarmapstringstring) |

<a id="opIdjurProduct"></a>

## POST 当前登录人查询辖区产品

POST /mdm/dtp/jur-ds/jur/product

当前登录人查询辖区产品

### 请求参数

| 名称          | 位置   | 类型   | 必选 | 说明 |
| ------------- | ------ | ------ | ---- | ---- |
| Authorization | header | string | 否   | none |
| clientId      | header | string | 否   | none |

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "productCode": "string",
      "productName": "string"
    }
  ]
}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型                                            |
| ------ | ------------------------------------------------------- | ---- | --------------------------------------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | none | [RListDtpJurProductVo](#schemarlistdtpjurproductvo) |

<a id="opIdapplyPage"></a>

## POST DTP 药店申请列表

POST /mdm/dtp/jur-ds/apply/page

DTP 药店申请列表

> Body 请求参数

```json
{
  "pageSize": 0,
  "pageNum": 0,
  "orderByColumn": "string",
  "isAsc": "string",
  "status": "string",
  "applyType": "string",
  "aeFlowId": 0,
  "drugName": "string",
  "source": "string",
  "instanceStates": [
    "string"
  ],
  "createBy": 0,
  "createCode": "string"
}
```

### 请求参数

| 名称          | 位置   | 类型                                            | 必选 | 说明 |
| ------------- | ------ | ----------------------------------------------- | ---- | ---- |
| Authorization | header | string                                          | 否   | none |
| clientId      | header | string                                          | 否   | none |
| body          | body   | [DtpDrugApplyPageBo](#schemadtpdrugapplypagebo) | 否   | none |

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "total": 0,
    "rows": [
      {
        "createBy": 0,
        "createTime": "2019-08-24T14:15:22Z",
        "updateBy": 0,
        "updateTime": "2019-08-24T14:15:22Z",
        "params": {
          "property1": {},
          "property2": {}
        },
        "id": 0,
        "applyType": "string",
        "status": "string",
        "jurCode": "string",
        "dsCode": "string",
        "dsName": "string",
        "applyTime": "2019-08-24T14:15:22Z",
        "applicant": "string",
        "applicantCode": "string",
        "postCode": "string",
        "postName": "string",
        "deptCode": "string",
        "deptName": "string",
        "ancestors": "string",
        "applyContent": "string",
        "applyNodeInfo": "string",
        "aeFlowId": 0,
        "cancelFlag": "string",
        "cancelTime": "2019-08-24T14:15:22Z",
        "completeFlag": "string",
        "completeTime": "2019-08-24T14:15:22Z",
        "bu": "string",
        "delFlag": "string",
        "remark": "string",
        "extendInfo": "string",
        "applyId": "string",
        "flowId": "string",
        "dsMdmCode": "string",
        "dsProvinceName": "string",
        "dsCityName": "string",
        "dsAreaName": "string",
        "actorId": 0,
        "dtpCode": "string",
        "dtpStatus": "string",
        "applyButtonStatus": "string",
        "instanceState": "string"
      }
    ],
    "code": 0,
    "msg": "string"
  }
}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型                                                    |
| ------ | ------------------------------------------------------- | ---- | ----------------------------------------------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | none | [RTableDataInfoDtpApplyVo](#schemartabledatainfodtpapplyvo) |

<a id="opIdqueryDrugstoreList_1"></a>

## GET 获取药店主数据列表

GET /mdm/dtp/jur-ds/drugstore/list

获取药店主数据列表

### 请求参数

| 名称               | 位置   | 类型              | 必选 | 说明                            |
| ------------------ | ------ | ----------------- | ---- | ------------------------------- |
| createBy           | query  | integer(int64)    | 否   | 创建者                          |
| createTime         | query  | string(date-time) | 否   | 创建时间                        |
| updateBy           | query  | integer(int64)    | 否   | 更新者                          |
| updateTime         | query  | string(date-time) | 否   | none                            |
| params             | query  | object            | 否   | 请求参数                        |
| id                 | query  | integer(int64)    | 否   | none                            |
| fileListStr        | query  | string            | 否   | none                            |
| fileList           | query  | array[object]     | 否   | none                            |
| dsCode             | query  | string            | 否   | 药店编码                        |
| dsMdmCode          | query  | string            | 否   | 药店 MDM 编码                   |
| dsName             | query  | string            | 否   | 药店名称                        |
| dsNameAlias        | query  | string            | 否   | 药店别名                        |
| dsType             | query  | string            | 否   | 药店类型                        |
| insLevel           | query  | string            | 否   | 药店级别                        |
| insGrade           | query  | string            | 否   | 药店等次                        |
| dsAreaType         | query  | string            | 否   | 药店区域类型                    |
| fixedPoint         | query  | string            | 否   | 是否医保定点                    |
| dsNature           | query  | object            | 否   | 药店性质                        |
| dsNatureStr        | query  | string            | 否   | none                            |
| province           | query  | string            | 否   | 省                              |
| provinceCode       | query  | string            | 否   | none                            |
| city               | query  | string            | 否   | 市                              |
| cityCode           | query  | string            | 否   | none                            |
| district           | query  | string            | 否   | 区                              |
| districtCode       | query  | string            | 否   | none                            |
| address            | query  | string            | 否   | 地址                            |
| legalPerson        | query  | string            | 否   | 法人                            |
| socialCreditCode   | query  | string            | 否   | 统一社会信用代码                |
| businessTerm       | query  | string            | 否   | 经营期限                        |
| dsEconomic         | query  | object            | 否   | 药店支付类型                    |
| dsEconomicStr      | query  | string            | 否   | none                            |
| docNum             | query  | string            | 否   | 执业药师数量                    |
| openingTime        | query  | string            | 否   | 营业时间                        |
| weekOpeningTime    | query  | string            | 否   | 周六日(含节假日)营业时间        |
| dsContactTel       | query  | string            | 否   | 药店电话                        |
| docContactTel      | query  | string            | 否   | 专用药师手机号                  |
| ddiStatus          | query  | string            | 否   | DDI 直连状态                    |
| businessScope      | query  | object            | 否   | 经营范围                        |
| businessScopeStr   | query  | string            | 否   | none                            |
| longitude          | query  | number(float)     | 否   | 经度                            |
| latitude           | query  | number(float)     | 否   | 纬度                            |
| status             | query  | string            | 否   | 是否有效[0 正常，1 停用]        |
| businessContact    | query  | string            | 否   | 是否商务开发                    |
| parentDealer       | query  | string            | 否   | 上级经销商                      |
| startDate          | query  | string            | 否   | 开始时间                        |
| endDate            | query  | string            | 否   | 结束时间                        |
| superiorDsCode     | query  | string            | 否   | 上级药店编码                    |
| superiorDsMdmCode  | query  | string            | 否   | none                            |
| superiorDsName     | query  | string            | 否   | 上级药店名称                    |
| ossId              | query  | object            | 否   | 附件 ID                         |
| bpmId              | query  | integer(int64)    | 否   | none                            |
| btCode             | query  | string            | 否   | none                            |
| sapCode            | query  | string            | 否   | none                            |
| mergeCode          | query  | string            | 否   | none                            |
| existMergeDs       | query  | string            | 否   | none                            |
| mergeBeforeName    | query  | string            | 否   | 合并前药店名称                  |
| mergeBeforeCode    | query  | string            | 否   | 合并前药店编码                  |
| dsHeadquarters     | query  | string            | 否   | 药店连锁总部                    |
| clodSales          | query  | string            | 否   | 是否具备 2-8 度冷链存储销售条件 |
| hisDocking         | query  | string            | 否   | 是否 HIS 对接                   |
| propertyDs         | query  | string            | 否   | 是否产权药店                    |
| conformInsDs       | query  | string            | 否   | 是否符合特药关联药店            |
| dealerChain        | query  | string            | 否   | 是否经销商旗下连锁              |
| dsSource           | query  | string            | 否   | 数据采集方式                    |
| dsLabelStr         | query  | string            | 否   | none                            |
| dsLabel            | query  | object            | 否   | 药店标签                        |
| supplyMode         | query  | string            | 否   | 供货方式                        |
| calibrateLongitude | query  | number(float)     | 否   | 校准后经度                      |
| calibrateLatitude  | query  | number(float)     | 否   | 校准后纬度                      |
| extendInfo         | query  | string            | 否   | none                            |
| orderStr           | query  | string            | 否   | none                            |
| pageSize           | query  | integer(int32)    | 否   | 分页大小                        |
| pageNum            | query  | integer(int32)    | 否   | 当前页数                        |
| orderByColumn      | query  | string            | 否   | 排序列                          |
| isAsc              | query  | string            | 否   | 排序的方向 desc 或者 asc        |
| Authorization      | header | string            | 否   | none                            |
| clientId           | header | string            | 否   | none                            |

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "total": 0,
    "rows": [
      {
        "createBy": 0,
        "createTime": "2019-08-24T14:15:22Z",
        "updateBy": 0,
        "updateTime": "2019-08-24T14:15:22Z",
        "params": {
          "property1": {},
          "property2": {}
        },
        "id": 0,
        "fileListStr": "string",
        "fileList": [
          {
            "id": null,
            "ossId": null,
            "url": null,
            "aeFileId": null,
            "size": null,
            "mime_type": null,
            "name": null
          }
        ],
        "dsCode": "string",
        "dsMdmCode": "string",
        "dsName": "string",
        "dsNameAlias": "string",
        "dsType": "string",
        "insLevel": "string",
        "insGrade": "string",
        "dsAreaType": "string",
        "fixedPoint": "string",
        "dsNature": {},
        "dsNatureStr": "string",
        "province": "string",
        "provinceCode": "string",
        "city": "string",
        "cityCode": "string",
        "district": "string",
        "districtCode": "string",
        "address": "string",
        "legalPerson": "string",
        "socialCreditCode": "string",
        "businessTerm": "string",
        "dsEconomic": {},
        "dsEconomicStr": "string",
        "docNum": "string",
        "openingTime": "string",
        "weekOpeningTime": "string",
        "dsContactTel": "string",
        "docContactTel": "string",
        "ddiStatus": "string",
        "businessScope": {},
        "businessScopeStr": "string",
        "longitude": 0.1,
        "latitude": 0.1,
        "status": "string",
        "businessContact": "string",
        "parentDealer": "string",
        "startDate": "string",
        "endDate": "string",
        "superiorDsCode": "string",
        "superiorDsMdmCode": "string",
        "superiorDsName": "string",
        "ossId": {},
        "bpmId": 0,
        "btCode": "string",
        "sapCode": "string",
        "mergeCode": "string",
        "existMergeDs": "string",
        "mergeBeforeName": "string",
        "mergeBeforeCode": "string",
        "dsHeadquarters": "string",
        "clodSales": "string",
        "hisDocking": "string",
        "propertyDs": "string",
        "conformInsDs": "string",
        "dealerChain": "string",
        "dsSource": "string",
        "dsLabelStr": "string",
        "dsLabel": {},
        "supplyMode": "string",
        "calibrateLongitude": 0.1,
        "calibrateLatitude": 0.1,
        "extendInfo": "string",
        "orderStr": "string"
      }
    ],
    "code": 0,
    "msg": "string"
  }
}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型                                                      |
| ------ | ------------------------------------------------------- | ---- | ------------------------------------------------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | none | [RTableDataInfoDrugstoreBo](#schemartabledatainfodrugstorebo) |

<a id="opIdgetDrugstoreDetail_1"></a>

## GET 药店详情

GET /mdm/dtp/jur-ds/drugstore/detail

药店详情

### 请求参数

| 名称               | 位置   | 类型              | 必选 | 说明                            |
| ------------------ | ------ | ----------------- | ---- | ------------------------------- |
| createBy           | query  | integer(int64)    | 否   | 创建者                          |
| createTime         | query  | string(date-time) | 否   | 创建时间                        |
| updateBy           | query  | integer(int64)    | 否   | 更新者                          |
| updateTime         | query  | string(date-time) | 否   | none                            |
| params             | query  | object            | 否   | 请求参数                        |
| id                 | query  | integer(int64)    | 否   | none                            |
| fileListStr        | query  | string            | 否   | none                            |
| fileList           | query  | array[object]     | 否   | none                            |
| dsCode             | query  | string            | 否   | 药店编码                        |
| dsMdmCode          | query  | string            | 否   | 药店 MDM 编码                   |
| dsName             | query  | string            | 否   | 药店名称                        |
| dsNameAlias        | query  | string            | 否   | 药店别名                        |
| dsType             | query  | string            | 否   | 药店类型                        |
| insLevel           | query  | string            | 否   | 药店级别                        |
| insGrade           | query  | string            | 否   | 药店等次                        |
| dsAreaType         | query  | string            | 否   | 药店区域类型                    |
| fixedPoint         | query  | string            | 否   | 是否医保定点                    |
| dsNature           | query  | object            | 否   | 药店性质                        |
| dsNatureStr        | query  | string            | 否   | none                            |
| province           | query  | string            | 否   | 省                              |
| provinceCode       | query  | string            | 否   | none                            |
| city               | query  | string            | 否   | 市                              |
| cityCode           | query  | string            | 否   | none                            |
| district           | query  | string            | 否   | 区                              |
| districtCode       | query  | string            | 否   | none                            |
| address            | query  | string            | 否   | 地址                            |
| legalPerson        | query  | string            | 否   | 法人                            |
| socialCreditCode   | query  | string            | 否   | 统一社会信用代码                |
| businessTerm       | query  | string            | 否   | 经营期限                        |
| dsEconomic         | query  | object            | 否   | 药店支付类型                    |
| dsEconomicStr      | query  | string            | 否   | none                            |
| docNum             | query  | string            | 否   | 执业药师数量                    |
| openingTime        | query  | string            | 否   | 营业时间                        |
| weekOpeningTime    | query  | string            | 否   | 周六日(含节假日)营业时间        |
| dsContactTel       | query  | string            | 否   | 药店电话                        |
| docContactTel      | query  | string            | 否   | 专用药师手机号                  |
| ddiStatus          | query  | string            | 否   | DDI 直连状态                    |
| businessScope      | query  | object            | 否   | 经营范围                        |
| businessScopeStr   | query  | string            | 否   | none                            |
| longitude          | query  | number(float)     | 否   | 经度                            |
| latitude           | query  | number(float)     | 否   | 纬度                            |
| status             | query  | string            | 否   | 是否有效[0 正常，1 停用]        |
| businessContact    | query  | string            | 否   | 是否商务开发                    |
| parentDealer       | query  | string            | 否   | 上级经销商                      |
| startDate          | query  | string            | 否   | 开始时间                        |
| endDate            | query  | string            | 否   | 结束时间                        |
| superiorDsCode     | query  | string            | 否   | 上级药店编码                    |
| superiorDsMdmCode  | query  | string            | 否   | none                            |
| superiorDsName     | query  | string            | 否   | 上级药店名称                    |
| ossId              | query  | object            | 否   | 附件 ID                         |
| bpmId              | query  | integer(int64)    | 否   | none                            |
| btCode             | query  | string            | 否   | none                            |
| sapCode            | query  | string            | 否   | none                            |
| mergeCode          | query  | string            | 否   | none                            |
| existMergeDs       | query  | string            | 否   | none                            |
| mergeBeforeName    | query  | string            | 否   | 合并前药店名称                  |
| mergeBeforeCode    | query  | string            | 否   | 合并前药店编码                  |
| dsHeadquarters     | query  | string            | 否   | 药店连锁总部                    |
| clodSales          | query  | string            | 否   | 是否具备 2-8 度冷链存储销售条件 |
| hisDocking         | query  | string            | 否   | 是否 HIS 对接                   |
| propertyDs         | query  | string            | 否   | 是否产权药店                    |
| conformInsDs       | query  | string            | 否   | 是否符合特药关联药店            |
| dealerChain        | query  | string            | 否   | 是否经销商旗下连锁              |
| dsSource           | query  | string            | 否   | 数据采集方式                    |
| dsLabelStr         | query  | string            | 否   | none                            |
| dsLabel            | query  | object            | 否   | 药店标签                        |
| supplyMode         | query  | string            | 否   | 供货方式                        |
| calibrateLongitude | query  | number(float)     | 否   | 校准后经度                      |
| calibrateLatitude  | query  | number(float)     | 否   | 校准后纬度                      |
| extendInfo         | query  | string            | 否   | none                            |
| orderStr           | query  | string            | 否   | none                            |
| Authorization      | header | string            | 否   | none                            |
| clientId           | header | string            | 否   | none                            |

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "createBy": 0,
    "createTime": "2019-08-24T14:15:22Z",
    "updateBy": 0,
    "updateTime": "2019-08-24T14:15:22Z",
    "params": {
      "property1": {},
      "property2": {}
    },
    "id": 0,
    "fileListStr": "string",
    "fileList": [
      {
        "id": "string",
        "ossId": "string",
        "url": "string",
        "aeFileId": "string",
        "size": "string",
        "mime_type": "string",
        "name": "string"
      }
    ],
    "dsCode": "string",
    "dsMdmCode": "string",
    "dsName": "string",
    "dsNameAlias": "string",
    "dsType": "string",
    "insLevel": "string",
    "insGrade": "string",
    "dsAreaType": "string",
    "fixedPoint": "string",
    "dsNature": {},
    "dsNatureStr": "string",
    "province": "string",
    "provinceCode": "string",
    "city": "string",
    "cityCode": "string",
    "district": "string",
    "districtCode": "string",
    "address": "string",
    "legalPerson": "string",
    "socialCreditCode": "string",
    "businessTerm": "string",
    "dsEconomic": {},
    "dsEconomicStr": "string",
    "docNum": "string",
    "openingTime": "string",
    "weekOpeningTime": "string",
    "dsContactTel": "string",
    "docContactTel": "string",
    "ddiStatus": "string",
    "businessScope": {},
    "businessScopeStr": "string",
    "longitude": 0.1,
    "latitude": 0.1,
    "status": "string",
    "businessContact": "string",
    "parentDealer": "string",
    "startDate": "string",
    "endDate": "string",
    "superiorDsCode": "string",
    "superiorDsMdmCode": "string",
    "superiorDsName": "string",
    "ossId": {},
    "bpmId": 0,
    "btCode": "string",
    "sapCode": "string",
    "mergeCode": "string",
    "existMergeDs": "string",
    "mergeBeforeName": "string",
    "mergeBeforeCode": "string",
    "dsHeadquarters": "string",
    "clodSales": "string",
    "hisDocking": "string",
    "propertyDs": "string",
    "conformInsDs": "string",
    "dealerChain": "string",
    "dsSource": "string",
    "dsLabelStr": "string",
    "dsLabel": {},
    "supplyMode": "string",
    "calibrateLongitude": 0.1,
    "calibrateLatitude": 0.1,
    "extendInfo": "string",
    "orderStr": "string"
  }
}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型                            |
| ------ | ------------------------------------------------------- | ---- | ----------------------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | none | [RDrugstoreBo](#schemardrugstorebo) |

<a id="opIdfindApplyDetail"></a>

## POST 根据审批流 id 获取 DTP 药店申请单展示状态

POST /mdm/dtp/jur-ds/apply/detail

根据审批流 id 获取 DTP 药店申请单展示状态

### 请求参数

| 名称          | 位置   | 类型           | 必选 | 说明 |
| ------------- | ------ | -------------- | ---- | ---- |
| flowId        | query  | integer(int64) | 是   | none |
| Authorization | header | string         | 否   | none |
| clientId      | header | string         | 否   | none |

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "createBy": 0,
    "createTime": "2019-08-24T14:15:22Z",
    "updateBy": 0,
    "updateTime": "2019-08-24T14:15:22Z",
    "params": {
      "property1": {},
      "property2": {}
    },
    "id": 0,
    "applyType": "string",
    "status": "string",
    "jurCode": "string",
    "dsCode": "string",
    "dsName": "string",
    "applyTime": "2019-08-24T14:15:22Z",
    "applicant": "string",
    "applicantCode": "string",
    "postCode": "string",
    "postName": "string",
    "deptCode": "string",
    "deptName": "string",
    "ancestors": "string",
    "applyContent": "string",
    "applyNodeInfo": "string",
    "aeFlowId": 0,
    "cancelFlag": "string",
    "cancelTime": "2019-08-24T14:15:22Z",
    "completeFlag": "string",
    "completeTime": "2019-08-24T14:15:22Z",
    "bu": "string",
    "delFlag": "string",
    "remark": "string",
    "extendInfo": "string",
    "applyId": "string",
    "flowId": "string",
    "dsMdmCode": "string",
    "dsProvinceName": "string",
    "dsCityName": "string",
    "dsAreaName": "string",
    "actorId": 0,
    "dtpCode": "string",
    "dtpStatus": "string",
    "applyButtonStatus": "string",
    "instanceState": "string"
  }
}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型                          |
| ------ | ------------------------------------------------------- | ---- | --------------------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | none | [RDtpApplyVo](#schemardtpapplyvo) |

# 数据模型

<h2 id="tocS_R">R</h2>

<a id="schemar"></a>
<a id="schema_R"></a>
<a id="tocSr"></a>
<a id="tocsr"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {}
}

```

响应信息主体

### 属性

| 名称 | 类型           | 必选  | 约束 | 中文名 | 说明       |
| ---- | -------------- | ----- | ---- | ------ | ---------- |
| code | integer(int32) | false | none |        | 消息状态码 |
| msg  | string         | false | none |        | 消息内容   |
| data | object         | false | none |        | 数据对象   |

<h2 id="tocS_RBoolean">RBoolean</h2>

<a id="schemarboolean"></a>
<a id="schema_RBoolean"></a>
<a id="tocSrboolean"></a>
<a id="tocsrboolean"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": true
}

```

响应信息主体

### 属性

| 名称 | 类型           | 必选  | 约束 | 中文名 | 说明       |
| ---- | -------------- | ----- | ---- | ------ | ---------- |
| code | integer(int32) | false | none |        | 消息状态码 |
| msg  | string         | false | none |        | 消息内容   |
| data | boolean        | false | none |        | 数据对象   |

<h2 id="tocS_ProductVo">ProductVo</h2>

<a id="schemaproductvo"></a>
<a id="schema_ProductVo"></a>
<a id="tocSproductvo"></a>
<a id="tocsproductvo"></a>

```json
{
  "mdmCode": "string",
  "productCode": "string",
  "productName": "string",
  "specCode": "string",
  "specName": "string"
}

```

### 属性

| 名称        | 类型   | 必选  | 约束 | 中文名 | 说明 |
| ----------- | ------ | ----- | ---- | ------ | ---- |
| mdmCode     | string | false | none |        | none |
| productCode | string | false | none |        | none |
| productName | string | false | none |        | none |
| specCode    | string | false | none |        | none |
| specName    | string | false | none |        | none |

<h2 id="tocS_AeTaskAttachmentBo">AeTaskAttachmentBo</h2>

<a id="schemaaetaskattachmentbo"></a>
<a id="schema_AeTaskAttachmentBo"></a>
<a id="tocSaetaskattachmentbo"></a>
<a id="tocsaetaskattachmentbo"></a>

```json
{
  "id": "string",
  "ossId": "string",
  "url": "string",
  "aeFileId": "string",
  "size": "string",
  "mime_type": "string",
  "name": "string"
}

```

### 属性

| 名称      | 类型   | 必选  | 约束 | 中文名 | 说明                     |
| --------- | ------ | ----- | ---- | ------ | ------------------------ |
| id        | string | false | none |        | 「附件」，文本字段，必填 |
| ossId     | string | false | none |        | none                     |
| url       | string | false | none |        | none                     |
| aeFileId  | string | false | none |        | none                     |
| size      | string | false | none |        | none                     |
| mime_type | string | false | none |        | none                     |
| name      | string | false | none |        | none                     |

<h2 id="tocS_CrmApplyBpmNodeContext">CrmApplyBpmNodeContext</h2>

<a id="schemacrmapplybpmnodecontext"></a>
<a id="schema_CrmApplyBpmNodeContext"></a>
<a id="tocScrmapplybpmnodecontext"></a>
<a id="tocscrmapplybpmnodecontext"></a>

```json
{
  "bpmNode": {
    "property1": "string",
    "property2": "string"
  }
}

```

### 属性

| 名称                       | 类型   | 必选  | 约束 | 中文名 | 说明 |
| -------------------------- | ------ | ----- | ---- | ------ | ---- |
| bpmNode                    | object | false | none |        | none |
| » **additionalProperties** | string | false | none |        | none |

<h2 id="tocS_DrugstoreBo">DrugstoreBo</h2>

<a id="schemadrugstorebo"></a>
<a id="schema_DrugstoreBo"></a>
<a id="tocSdrugstorebo"></a>
<a id="tocsdrugstorebo"></a>

```json
{
  "createBy": 0,
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": 0,
  "updateTime": "2019-08-24T14:15:22Z",
  "params": {
    "property1": {},
    "property2": {}
  },
  "id": 0,
  "fileListStr": "string",
  "fileList": [
    {
      "id": "string",
      "ossId": "string",
      "url": "string",
      "aeFileId": "string",
      "size": "string",
      "mime_type": "string",
      "name": "string"
    }
  ],
  "dsCode": "string",
  "dsMdmCode": "string",
  "dsName": "string",
  "dsNameAlias": "string",
  "dsType": "string",
  "insLevel": "string",
  "insGrade": "string",
  "dsAreaType": "string",
  "fixedPoint": "string",
  "dsNature": {},
  "dsNatureStr": "string",
  "province": "string",
  "provinceCode": "string",
  "city": "string",
  "cityCode": "string",
  "district": "string",
  "districtCode": "string",
  "address": "string",
  "legalPerson": "string",
  "socialCreditCode": "string",
  "businessTerm": "string",
  "dsEconomic": {},
  "dsEconomicStr": "string",
  "docNum": "string",
  "openingTime": "string",
  "weekOpeningTime": "string",
  "dsContactTel": "string",
  "docContactTel": "string",
  "ddiStatus": "string",
  "businessScope": {},
  "businessScopeStr": "string",
  "longitude": 0.1,
  "latitude": 0.1,
  "status": "string",
  "businessContact": "string",
  "parentDealer": "string",
  "startDate": "string",
  "endDate": "string",
  "superiorDsCode": "string",
  "superiorDsMdmCode": "string",
  "superiorDsName": "string",
  "ossId": {},
  "bpmId": 0,
  "btCode": "string",
  "sapCode": "string",
  "mergeCode": "string",
  "existMergeDs": "string",
  "mergeBeforeName": "string",
  "mergeBeforeCode": "string",
  "dsHeadquarters": "string",
  "clodSales": "string",
  "hisDocking": "string",
  "propertyDs": "string",
  "conformInsDs": "string",
  "dealerChain": "string",
  "dsSource": "string",
  "dsLabelStr": "string",
  "dsLabel": {},
  "supplyMode": "string",
  "calibrateLongitude": 0.1,
  "calibrateLatitude": 0.1,
  "extendInfo": "string",
  "orderStr": "string"
}

```

### 属性

| 名称                       | 类型                                              | 必选  | 约束 | 中文名 | 说明                            |
| -------------------------- | ------------------------------------------------- | ----- | ---- | ------ | ------------------------------- |
| createBy                   | integer(int64)                                    | false | none |        | 创建者                          |
| createTime                 | string(date-time)                                 | false | none |        | 创建时间                        |
| updateBy                   | integer(int64)                                    | false | none |        | 更新者                          |
| updateTime                 | string(date-time)                                 | false | none |        | none                            |
| params                     | object                                            | false | none |        | 请求参数                        |
| » **additionalProperties** | object                                            | false | none |        | none                            |
| id                         | integer(int64)                                    | false | none |        | none                            |
| fileListStr                | string                                            | false | none |        | none                            |
| fileList                   | [[AeTaskAttachmentBo](#schemaaetaskattachmentbo)] | false | none |        | none                            |
| dsCode                     | string                                            | false | none |        | 药店编码                        |
| dsMdmCode                  | string                                            | false | none |        | 药店 MDM 编码                   |
| dsName                     | string                                            | false | none |        | 药店名称                        |
| dsNameAlias                | string                                            | false | none |        | 药店别名                        |
| dsType                     | string                                            | false | none |        | 药店类型                        |
| insLevel                   | string                                            | false | none |        | 药店级别                        |
| insGrade                   | string                                            | false | none |        | 药店等次                        |
| dsAreaType                 | string                                            | false | none |        | 药店区域类型                    |
| fixedPoint                 | string                                            | false | none |        | 是否医保定点                    |
| dsNature                   | object                                            | false | none |        | 药店性质                        |
| dsNatureStr                | string                                            | false | none |        | none                            |
| province                   | string                                            | false | none |        | 省                              |
| provinceCode               | string                                            | false | none |        | none                            |
| city                       | string                                            | false | none |        | 市                              |
| cityCode                   | string                                            | false | none |        | none                            |
| district                   | string                                            | false | none |        | 区                              |
| districtCode               | string                                            | false | none |        | none                            |
| address                    | string                                            | false | none |        | 地址                            |
| legalPerson                | string                                            | false | none |        | 法人                            |
| socialCreditCode           | string                                            | false | none |        | 统一社会信用代码                |
| businessTerm               | string                                            | false | none |        | 经营期限                        |
| dsEconomic                 | object                                            | false | none |        | 药店支付类型                    |
| dsEconomicStr              | string                                            | false | none |        | none                            |
| docNum                     | string                                            | false | none |        | 执业药师数量                    |
| openingTime                | string                                            | false | none |        | 营业时间                        |
| weekOpeningTime            | string                                            | false | none |        | 周六日(含节假日)营业时间        |
| dsContactTel               | string                                            | false | none |        | 药店电话                        |
| docContactTel              | string                                            | false | none |        | 专用药师手机号                  |
| ddiStatus                  | string                                            | false | none |        | DDI 直连状态                    |
| businessScope              | object                                            | false | none |        | 经营范围                        |
| businessScopeStr           | string                                            | false | none |        | none                            |
| longitude                  | number(float)                                     | false | none |        | 经度                            |
| latitude                   | number(float)                                     | false | none |        | 纬度                            |
| status                     | string                                            | false | none |        | 是否有效[0 正常，1 停用]        |
| businessContact            | string                                            | false | none |        | 是否商务开发                    |
| parentDealer               | string                                            | false | none |        | 上级经销商                      |
| startDate                  | string                                            | false | none |        | 开始时间                        |
| endDate                    | string                                            | false | none |        | 结束时间                        |
| superiorDsCode             | string                                            | false | none |        | 上级药店编码                    |
| superiorDsMdmCode          | string                                            | false | none |        | none                            |
| superiorDsName             | string                                            | false | none |        | 上级药店名称                    |
| ossId                      | object                                            | false | none |        | 附件 ID                         |
| bpmId                      | integer(int64)                                    | false | none |        | none                            |
| btCode                     | string                                            | false | none |        | none                            |
| sapCode                    | string                                            | false | none |        | none                            |
| mergeCode                  | string                                            | false | none |        | none                            |
| existMergeDs               | string                                            | false | none |        | none                            |
| mergeBeforeName            | string                                            | false | none |        | 合并前药店名称                  |
| mergeBeforeCode            | string                                            | false | none |        | 合并前药店编码                  |
| dsHeadquarters             | string                                            | false | none |        | 药店连锁总部                    |
| clodSales                  | string                                            | false | none |        | 是否具备 2-8 度冷链存储销售条件 |
| hisDocking                 | string                                            | false | none |        | 是否 HIS 对接                   |
| propertyDs                 | string                                            | false | none |        | 是否产权药店                    |
| conformInsDs               | string                                            | false | none |        | 是否符合特药关联药店            |
| dealerChain                | string                                            | false | none |        | 是否经销商旗下连锁              |
| dsSource                   | string                                            | false | none |        | 数据采集方式                    |
| dsLabelStr                 | string                                            | false | none |        | none                            |
| dsLabel                    | object                                            | false | none |        | 药店标签                        |
| supplyMode                 | string                                            | false | none |        | 供货方式                        |
| calibrateLongitude         | number(float)                                     | false | none |        | 校准后经度                      |
| calibrateLatitude          | number(float)                                     | false | none |        | 校准后纬度                      |
| extendInfo                 | string                                            | false | none |        | none                            |
| orderStr                   | string                                            | false | none |        | none                            |

<h2 id="tocS_RTableDataInfoDrugstoreBo">RTableDataInfoDrugstoreBo</h2>

<a id="schemartabledatainfodrugstorebo"></a>
<a id="schema_RTableDataInfoDrugstoreBo"></a>
<a id="tocSrtabledatainfodrugstorebo"></a>
<a id="tocsrtabledatainfodrugstorebo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "total": 0,
    "rows": [
      {
        "createBy": 0,
        "createTime": "2019-08-24T14:15:22Z",
        "updateBy": 0,
        "updateTime": "2019-08-24T14:15:22Z",
        "params": {
          "property1": {},
          "property2": {}
        },
        "id": 0,
        "fileListStr": "string",
        "fileList": [
          {
            "id": null,
            "ossId": null,
            "url": null,
            "aeFileId": null,
            "size": null,
            "mime_type": null,
            "name": null
          }
        ],
        "dsCode": "string",
        "dsMdmCode": "string",
        "dsName": "string",
        "dsNameAlias": "string",
        "dsType": "string",
        "insLevel": "string",
        "insGrade": "string",
        "dsAreaType": "string",
        "fixedPoint": "string",
        "dsNature": {},
        "dsNatureStr": "string",
        "province": "string",
        "provinceCode": "string",
        "city": "string",
        "cityCode": "string",
        "district": "string",
        "districtCode": "string",
        "address": "string",
        "legalPerson": "string",
        "socialCreditCode": "string",
        "businessTerm": "string",
        "dsEconomic": {},
        "dsEconomicStr": "string",
        "docNum": "string",
        "openingTime": "string",
        "weekOpeningTime": "string",
        "dsContactTel": "string",
        "docContactTel": "string",
        "ddiStatus": "string",
        "businessScope": {},
        "businessScopeStr": "string",
        "longitude": 0.1,
        "latitude": 0.1,
        "status": "string",
        "businessContact": "string",
        "parentDealer": "string",
        "startDate": "string",
        "endDate": "string",
        "superiorDsCode": "string",
        "superiorDsMdmCode": "string",
        "superiorDsName": "string",
        "ossId": {},
        "bpmId": 0,
        "btCode": "string",
        "sapCode": "string",
        "mergeCode": "string",
        "existMergeDs": "string",
        "mergeBeforeName": "string",
        "mergeBeforeCode": "string",
        "dsHeadquarters": "string",
        "clodSales": "string",
        "hisDocking": "string",
        "propertyDs": "string",
        "conformInsDs": "string",
        "dealerChain": "string",
        "dsSource": "string",
        "dsLabelStr": "string",
        "dsLabel": {},
        "supplyMode": "string",
        "calibrateLongitude": 0.1,
        "calibrateLatitude": 0.1,
        "extendInfo": "string",
        "orderStr": "string"
      }
    ],
    "code": 0,
    "msg": "string"
  }
}

```

响应信息主体

### 属性

| 名称 | 类型                                                        | 必选  | 约束 | 中文名 | 说明             |
| ---- | ----------------------------------------------------------- | ----- | ---- | ------ | ---------------- |
| code | integer(int32)                                              | false | none |        | 消息状态码       |
| msg  | string                                                      | false | none |        | 消息内容         |
| data | [TableDataInfoDrugstoreBo](#schematabledatainfodrugstorebo) | false | none |        | 表格分页数据对象 |

<h2 id="tocS_TableDataInfoDrugstoreBo">TableDataInfoDrugstoreBo</h2>

<a id="schematabledatainfodrugstorebo"></a>
<a id="schema_TableDataInfoDrugstoreBo"></a>
<a id="tocStabledatainfodrugstorebo"></a>
<a id="tocstabledatainfodrugstorebo"></a>

```json
{
  "total": 0,
  "rows": [
    {
      "createBy": 0,
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": 0,
      "updateTime": "2019-08-24T14:15:22Z",
      "params": {
        "property1": {},
        "property2": {}
      },
      "id": 0,
      "fileListStr": "string",
      "fileList": [
        {
          "id": "string",
          "ossId": "string",
          "url": "string",
          "aeFileId": "string",
          "size": "string",
          "mime_type": "string",
          "name": "string"
        }
      ],
      "dsCode": "string",
      "dsMdmCode": "string",
      "dsName": "string",
      "dsNameAlias": "string",
      "dsType": "string",
      "insLevel": "string",
      "insGrade": "string",
      "dsAreaType": "string",
      "fixedPoint": "string",
      "dsNature": {},
      "dsNatureStr": "string",
      "province": "string",
      "provinceCode": "string",
      "city": "string",
      "cityCode": "string",
      "district": "string",
      "districtCode": "string",
      "address": "string",
      "legalPerson": "string",
      "socialCreditCode": "string",
      "businessTerm": "string",
      "dsEconomic": {},
      "dsEconomicStr": "string",
      "docNum": "string",
      "openingTime": "string",
      "weekOpeningTime": "string",
      "dsContactTel": "string",
      "docContactTel": "string",
      "ddiStatus": "string",
      "businessScope": {},
      "businessScopeStr": "string",
      "longitude": 0.1,
      "latitude": 0.1,
      "status": "string",
      "businessContact": "string",
      "parentDealer": "string",
      "startDate": "string",
      "endDate": "string",
      "superiorDsCode": "string",
      "superiorDsMdmCode": "string",
      "superiorDsName": "string",
      "ossId": {},
      "bpmId": 0,
      "btCode": "string",
      "sapCode": "string",
      "mergeCode": "string",
      "existMergeDs": "string",
      "mergeBeforeName": "string",
      "mergeBeforeCode": "string",
      "dsHeadquarters": "string",
      "clodSales": "string",
      "hisDocking": "string",
      "propertyDs": "string",
      "conformInsDs": "string",
      "dealerChain": "string",
      "dsSource": "string",
      "dsLabelStr": "string",
      "dsLabel": {},
      "supplyMode": "string",
      "calibrateLongitude": 0.1,
      "calibrateLatitude": 0.1,
      "extendInfo": "string",
      "orderStr": "string"
    }
  ],
  "code": 0,
  "msg": "string"
}

```

表格分页数据对象

### 属性

| 名称  | 类型                                | 必选  | 约束 | 中文名 | 说明       |
| ----- | ----------------------------------- | ----- | ---- | ------ | ---------- |
| total | integer(int64)                      | false | none |        | 总记录数   |
| rows  | [[DrugstoreBo](#schemadrugstorebo)] | false | none |        | 列表数据   |
| code  | integer(int32)                      | false | none |        | 消息状态码 |
| msg   | string                              | false | none |        | 消息内容   |

<h2 id="tocS_RDrugstoreBo">RDrugstoreBo</h2>

<a id="schemardrugstorebo"></a>
<a id="schema_RDrugstoreBo"></a>
<a id="tocSrdrugstorebo"></a>
<a id="tocsrdrugstorebo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "createBy": 0,
    "createTime": "2019-08-24T14:15:22Z",
    "updateBy": 0,
    "updateTime": "2019-08-24T14:15:22Z",
    "params": {
      "property1": {},
      "property2": {}
    },
    "id": 0,
    "fileListStr": "string",
    "fileList": [
      {
        "id": "string",
        "ossId": "string",
        "url": "string",
        "aeFileId": "string",
        "size": "string",
        "mime_type": "string",
        "name": "string"
      }
    ],
    "dsCode": "string",
    "dsMdmCode": "string",
    "dsName": "string",
    "dsNameAlias": "string",
    "dsType": "string",
    "insLevel": "string",
    "insGrade": "string",
    "dsAreaType": "string",
    "fixedPoint": "string",
    "dsNature": {},
    "dsNatureStr": "string",
    "province": "string",
    "provinceCode": "string",
    "city": "string",
    "cityCode": "string",
    "district": "string",
    "districtCode": "string",
    "address": "string",
    "legalPerson": "string",
    "socialCreditCode": "string",
    "businessTerm": "string",
    "dsEconomic": {},
    "dsEconomicStr": "string",
    "docNum": "string",
    "openingTime": "string",
    "weekOpeningTime": "string",
    "dsContactTel": "string",
    "docContactTel": "string",
    "ddiStatus": "string",
    "businessScope": {},
    "businessScopeStr": "string",
    "longitude": 0.1,
    "latitude": 0.1,
    "status": "string",
    "businessContact": "string",
    "parentDealer": "string",
    "startDate": "string",
    "endDate": "string",
    "superiorDsCode": "string",
    "superiorDsMdmCode": "string",
    "superiorDsName": "string",
    "ossId": {},
    "bpmId": 0,
    "btCode": "string",
    "sapCode": "string",
    "mergeCode": "string",
    "existMergeDs": "string",
    "mergeBeforeName": "string",
    "mergeBeforeCode": "string",
    "dsHeadquarters": "string",
    "clodSales": "string",
    "hisDocking": "string",
    "propertyDs": "string",
    "conformInsDs": "string",
    "dealerChain": "string",
    "dsSource": "string",
    "dsLabelStr": "string",
    "dsLabel": {},
    "supplyMode": "string",
    "calibrateLongitude": 0.1,
    "calibrateLatitude": 0.1,
    "extendInfo": "string",
    "orderStr": "string"
  }
}

```

响应信息主体

### 属性

| 名称 | 类型                              | 必选  | 约束 | 中文名 | 说明       |
| ---- | --------------------------------- | ----- | ---- | ------ | ---------- |
| code | integer(int32)                    | false | none |        | 消息状态码 |
| msg  | string                            | false | none |        | 消息内容   |
| data | [DrugstoreBo](#schemadrugstorebo) | false | none |        | none       |

<h2 id="tocS_RMapStringString">RMapStringString</h2>

<a id="schemarmapstringstring"></a>
<a id="schema_RMapStringString"></a>
<a id="tocSrmapstringstring"></a>
<a id="tocsrmapstringstring"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "property1": "string",
    "property2": "string"
  }
}

```

响应信息主体

### 属性

| 名称                       | 类型           | 必选  | 约束 | 中文名 | 说明       |
| -------------------------- | -------------- | ----- | ---- | ------ | ---------- |
| code                       | integer(int32) | false | none |        | 消息状态码 |
| msg                        | string         | false | none |        | 消息内容   |
| data                       | object         | false | none |        | 数据对象   |
| » **additionalProperties** | string         | false | none |        | none       |

<h2 id="tocS_EsPageInfoObject">EsPageInfoObject</h2>

<a id="schemaespageinfoobject"></a>
<a id="schema_EsPageInfoObject"></a>
<a id="tocSespageinfoobject"></a>
<a id="tocsespageinfoobject"></a>

```json
{
  "total": 0,
  "list": [
    {}
  ],
  "pageNum": 0,
  "pageSize": 0,
  "size": 0,
  "startRow": 0,
  "endRow": 0,
  "pages": 0,
  "prePage": 0,
  "nextPage": 0,
  "hasPreviousPage": true,
  "hasNextPage": true,
  "navigatePages": 0,
  "navigatePageNums": [
    0
  ],
  "navigateFirstPage": 0,
  "navigateLastPage": 0,
  "firstPage": true,
  "lastPage": true
}

```

### 属性

| 名称              | 类型           | 必选  | 约束 | 中文名 | 说明 |
| ----------------- | -------------- | ----- | ---- | ------ | ---- |
| total             | integer(int64) | false | none |        | none |
| list              | [object]       | false | none |        | none |
| pageNum           | integer(int32) | false | none |        | none |
| pageSize          | integer(int32) | false | none |        | none |
| size              | integer(int32) | false | none |        | none |
| startRow          | integer(int32) | false | none |        | none |
| endRow            | integer(int32) | false | none |        | none |
| pages             | integer(int32) | false | none |        | none |
| prePage           | integer(int32) | false | none |        | none |
| nextPage          | integer(int32) | false | none |        | none |
| hasPreviousPage   | boolean        | false | none |        | none |
| hasNextPage       | boolean        | false | none |        | none |
| navigatePages     | integer(int32) | false | none |        | none |
| navigatePageNums  | [integer]      | false | none |        | none |
| navigateFirstPage | integer(int32) | false | none |        | none |
| navigateLastPage  | integer(int32) | false | none |        | none |
| firstPage         | boolean        | false | none |        | none |
| lastPage          | boolean        | false | none |        | none |

<h2 id="tocS_REsPageInfoObject">REsPageInfoObject</h2>

<a id="schemarespageinfoobject"></a>
<a id="schema_REsPageInfoObject"></a>
<a id="tocSrespageinfoobject"></a>
<a id="tocsrespageinfoobject"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "total": 0,
    "list": [
      {}
    ],
    "pageNum": 0,
    "pageSize": 0,
    "size": 0,
    "startRow": 0,
    "endRow": 0,
    "pages": 0,
    "prePage": 0,
    "nextPage": 0,
    "hasPreviousPage": true,
    "hasNextPage": true,
    "navigatePages": 0,
    "navigatePageNums": [
      0
    ],
    "navigateFirstPage": 0,
    "navigateLastPage": 0,
    "firstPage": true,
    "lastPage": true
  }
}

```

响应信息主体

### 属性

| 名称 | 类型                                        | 必选  | 约束 | 中文名 | 说明       |
| ---- | ------------------------------------------- | ----- | ---- | ------ | ---------- |
| code | integer(int32)                              | false | none |        | 消息状态码 |
| msg  | string                                      | false | none |        | 消息内容   |
| data | [EsPageInfoObject](#schemaespageinfoobject) | false | none |        | none       |

<h2 id="tocS_RJurisdictionDtpDrugstoreTypeEnum">RJurisdictionDtpDrugstoreTypeEnum</h2>

<a id="schemarjurisdictiondtpdrugstoretypeenum"></a>
<a id="schema_RJurisdictionDtpDrugstoreTypeEnum"></a>
<a id="tocSrjurisdictiondtpdrugstoretypeenum"></a>
<a id="tocsrjurisdictiondtpdrugstoretypeenum"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": "JURISDICTION_DTP_DRUGSTORE_INSERT"
}

```

响应信息主体

### 属性

| 名称 | 类型           | 必选  | 约束 | 中文名 | 说明       |
| ---- | -------------- | ----- | ---- | ------ | ---------- |
| code | integer(int32) | false | none |        | 消息状态码 |
| msg  | string         | false | none |        | 消息内容   |
| data | string         | false | none |        | 数据对象   |

#### 枚举值

| 属性 | 值                                         |
| ---- | ------------------------------------------ |
| data | JURISDICTION_DTP_DRUGSTORE_INSERT          |
| data | JURISDICTION_DTP_DRUGSTORE_DELETED         |
| data | JURISDICTION_DTP_DRUGSTORE_PRODUCT_INSERT  |
| data | JURISDICTION_DTP_DRUGSTORE_PRODUCT_DELETED |
| data | WAIT_AUDITOR_SUBMIT                        |
| data | DTP_EMP_COMPLETE                           |
| data | DTP_EMP_OPERATION                          |

<h2 id="tocS_DrugScore">DrugScore</h2>

<a id="schemadrugscore"></a>
<a id="schema_DrugScore"></a>
<a id="tocSdrugscore"></a>
<a id="tocsdrugscore"></a>

```json
{
  "scoreItemName": "string",
  "scoreItem": [
    {
      "scorePointsTitle": "string",
      "score": "string",
      "inputScore": "string"
    }
  ]
}

```

### 属性

| 名称          | 类型                              | 必选  | 约束 | 中文名 | 说明 |
| ------------- | --------------------------------- | ----- | ---- | ------ | ---- |
| scoreItemName | string                            | false | none |        | none |
| scoreItem     | [[ScorePoint](#schemascorepoint)] | false | none |        | none |

<h2 id="tocS_ScorePoint">ScorePoint</h2>

<a id="schemascorepoint"></a>
<a id="schema_ScorePoint"></a>
<a id="tocSscorepoint"></a>
<a id="tocsscorepoint"></a>

```json
{
  "scorePointsTitle": "string",
  "score": "string",
  "inputScore": "string"
}

```

### 属性

| 名称             | 类型   | 必选  | 约束 | 中文名 | 说明 |
| ---------------- | ------ | ----- | ---- | ------ | ---- |
| scorePointsTitle | string | false | none |        | none |
| score            | string | false | none |        | none |
| inputScore       | string | false | none |        | none |

<h2 id="tocS_DtpEmpOperateBo">DtpEmpOperateBo</h2>

<a id="schemadtpempoperatebo"></a>
<a id="schema_DtpEmpOperateBo"></a>
<a id="tocSdtpempoperatebo"></a>
<a id="tocsdtpempoperatebo"></a>

```json
{
  "flowId": 0,
  "status": "string",
  "refuseReason": "string",
  "ossIdList": [
    "string"
  ],
  "tagList": [
    {}
  ],
  "drugSocreList": [
    {
      "scoreItemName": "string",
      "scoreItem": [
        {
          "scorePointsTitle": "string",
          "score": "string",
          "inputScore": "string"
        }
      ]
    }
  ]
}

```

### 属性

| 名称          | 类型                            | 必选  | 约束 | 中文名 | 说明     |
| ------------- | ------------------------------- | ----- | ---- | ------ | -------- |
| flowId        | integer(int64)                  | false | none |        | 流程 id  |
| status        | string                          | false | none |        | 操作状态 |
| refuseReason  | string                          | false | none |        | 拒绝原因 |
| ossIdList     | [string]                        | false | none |        | 附件集合 |
| tagList       | [object]                        | false | none |        | 标签集合 |
| drugSocreList | [[DrugScore](#schemadrugscore)] | false | none |        | 药店评分 |

<h2 id="tocS_Tag">Tag</h2>

<a id="schematag"></a>
<a id="schema_Tag"></a>
<a id="tocStag"></a>
<a id="tocstag"></a>

```json
{
  "tagCode": "string",
  "tagName": "string"
}

```

### 属性

| 名称    | 类型   | 必选  | 约束 | 中文名 | 说明 |
| ------- | ------ | ----- | ---- | ------ | ---- |
| tagCode | string | false | none |        | none |
| tagName | string | false | none |        | none |

<h2 id="tocS_GsApplyBpmJurDsBo">GsApplyBpmJurDsBo</h2>

<a id="schemagsapplybpmjurdsbo"></a>
<a id="schema_GsApplyBpmJurDsBo"></a>
<a id="tocSgsapplybpmjurdsbo"></a>
<a id="tocsgsapplybpmjurdsbo"></a>

```json
{
  "createBy": 0,
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": 0,
  "updateTime": "2019-08-24T14:15:22Z",
  "params": {
    "property1": {},
    "property2": {}
  },
  "appName": "string",
  "dsCode": "string",
  "dsName": "string",
  "dsMdmCode": "string",
  "province": "string",
  "provinceCode": "string",
  "city": "string",
  "cityCode": "string",
  "district": "string",
  "districtCode": "string",
  "specList": [
    {
      "mdmCode": "string",
      "productCode": "string",
      "productName": "string",
      "specCode": "string",
      "specName": "string"
    }
  ],
  "createdEmpName": "string",
  "fileList": [
    {
      "id": "string",
      "ossId": "string",
      "url": "string",
      "aeFileId": "string",
      "size": "string",
      "mime_type": "string",
      "name": "string"
    }
  ],
  "jurCode": "string",
  "jurType": "string",
  "bu": "string",
  "address": "string"
}

```

### 属性

| 名称                       | 类型                                              | 必选  | 约束 | 中文名 | 说明         |
| -------------------------- | ------------------------------------------------- | ----- | ---- | ------ | ------------ |
| createBy                   | integer(int64)                                    | false | none |        | 创建者       |
| createTime                 | string(date-time)                                 | false | none |        | 创建时间     |
| updateBy                   | integer(int64)                                    | false | none |        | 更新者       |
| updateTime                 | string(date-time)                                 | false | none |        | 更新时间     |
| params                     | object                                            | false | none |        | 请求参数     |
| » **additionalProperties** | object                                            | false | none |        | none         |
| appName                    | string                                            | false | none |        | none         |
| dsCode                     | string                                            | false | none |        | 药店 GS 编码 |
| dsName                     | string                                            | false | none |        | none         |
| dsMdmCode                  | string                                            | false | none |        | none         |
| province                   | string                                            | false | none |        | 省           |
| provinceCode               | string                                            | false | none |        | none         |
| city                       | string                                            | false | none |        | 市           |
| cityCode                   | string                                            | false | none |        | none         |
| district                   | string                                            | false | none |        | 区           |
| districtCode               | string                                            | false | none |        | none         |
| specList                   | [[ProductVo](#schemaproductvo)]                   | false | none |        | 品规信息     |
| createdEmpName             | string                                            | false | none |        | 创建人       |
| fileList                   | [[AeTaskAttachmentBo](#schemaaetaskattachmentbo)] | false | none |        | 营业执照     |
| jurCode                    | string                                            | false | none |        | 辖区编码     |
| jurType                    | string                                            | false | none |        | 辖区类型     |
| bu                         | string                                            | false | none |        | 来源         |
| address                    | string                                            | false | none |        | 地址         |

<h2 id="tocS_GsApplyBpmJurDsContextGsApplyBpmJurDsBo">GsApplyBpmJurDsContextGsApplyBpmJurDsBo</h2>

<a id="schemagsapplybpmjurdscontextgsapplybpmjurdsbo"></a>
<a id="schema_GsApplyBpmJurDsContextGsApplyBpmJurDsBo"></a>
<a id="tocSgsapplybpmjurdscontextgsapplybpmjurdsbo"></a>
<a id="tocsgsapplybpmjurdscontextgsapplybpmjurdsbo"></a>

```json
{
  "appCode": "string",
  "tenantId": "string",
  "postIdList": [
    0
  ],
  "enableWorkflow": true,
  "applyType": "string",
  "processId": "string",
  "applyTime": "2019-08-24T14:15:22Z",
  "applicant": "string",
  "applicantCode": "string",
  "postCode": "string",
  "postName": "string",
  "deptCode": "string",
  "deptName": "string",
  "ancestors": "string",
  "applyContent": {
    "createBy": 0,
    "createTime": "2019-08-24T14:15:22Z",
    "updateBy": 0,
    "updateTime": "2019-08-24T14:15:22Z",
    "params": {
      "property1": {},
      "property2": {}
    },
    "appName": "string",
    "dsCode": "string",
    "dsName": "string",
    "dsMdmCode": "string",
    "province": "string",
    "provinceCode": "string",
    "city": "string",
    "cityCode": "string",
    "district": "string",
    "districtCode": "string",
    "specList": [
      {
        "mdmCode": "string",
        "productCode": "string",
        "productName": "string",
        "specCode": "string",
        "specName": "string"
      }
    ],
    "createdEmpName": "string",
    "fileList": [
      {
        "id": "string",
        "ossId": "string",
        "url": "string",
        "aeFileId": "string",
        "size": "string",
        "mime_type": "string",
        "name": "string"
      }
    ],
    "jurCode": "string",
    "jurType": "string",
    "bu": "string",
    "address": "string"
  },
  "appUrl": "string",
  "pcUrl": "string",
  "applyNodeInfo": [
    {
      "bpmNode": {
        "property1": "string",
        "property2": "string"
      }
    }
  ],
  "jurCode": "string",
  "dsCode": "string",
  "dsName": "string",
  "bu": "string",
  "jurDsCode": "string",
  "dsMdmCode": "string"
}

```

### 属性

| 名称           | 类型                                                      | 必选  | 约束 | 中文名 | 说明                                                                                                               |
| -------------- | --------------------------------------------------------- | ----- | ---- | ------ | ------------------------------------------------------------------------------------------------------------------ |
| appCode        | string                                                    | false | none |        | none                                                                                                               |
| tenantId       | string                                                    | false | none |        | none                                                                                                               |
| postIdList     | [integer]                                                 | false | none |        | none                                                                                                               |
| enableWorkflow | boolean                                                   | false | none |        | none                                                                                                               |
| applyType      | string                                                    | false | none |        | 审批类型[1-机构新增，2-客户新增，3-多点执业新增，4-药店新增，5-辖区机构认领，6-辖区客户认领,7-新增讲者,8-变更讲者] |
| processId      | string                                                    | false | none |        | none                                                                                                               |
| applyTime      | string(date-time)                                         | false | none |        | 申请时间                                                                                                           |
| applicant      | string                                                    | false | none |        | 申请人                                                                                                             |
| applicantCode  | string                                                    | false | none |        | 申请人工号                                                                                                         |
| postCode       | string                                                    | false | none |        | 岗位编码                                                                                                           |
| postName       | string                                                    | false | none |        | 岗位名称                                                                                                           |
| deptCode       | string                                                    | false | none |        | 部门编码                                                                                                           |
| deptName       | string                                                    | false | none |        | 部门名称                                                                                                           |
| ancestors      | string                                                    | false | none |        | 部门层级结构                                                                                                       |
| applyContent   | [GsApplyBpmJurDsBo](#schemagsapplybpmjurdsbo)             | false | none |        | none                                                                                                               |
| appUrl         | string                                                    | false | none |        | 小程序跳转地址                                                                                                     |
| pcUrl          | string                                                    | false | none |        | pc 跳转地址                                                                                                        |
| applyNodeInfo  | [[CrmApplyBpmNodeContext](#schemacrmapplybpmnodecontext)] | false | none |        | 审批节点信息                                                                                                       |
| jurCode        | string                                                    | false | none |        | 辅助查询(辖区编码)                                                                                                 |
| dsCode         | string                                                    | false | none |        | 辅助查询(药店信息)                                                                                                 |
| dsName         | string                                                    | false | none |        | none                                                                                                               |
| bu             | string                                                    | false | none |        | 来源                                                                                                               |
| jurDsCode      | string                                                    | false | none |        | 关联药店 code                                                                                                      |
| dsMdmCode      | string                                                    | false | none |        | none                                                                                                               |

<h2 id="tocS_GsJurDrugProductBo">GsJurDrugProductBo</h2>

<a id="schemagsjurdrugproductbo"></a>
<a id="schema_GsJurDrugProductBo"></a>
<a id="tocSgsjurdrugproductbo"></a>
<a id="tocsgsjurdrugproductbo"></a>

```json
{
  "jurDsCode": "string",
  "productCode": "string",
  "specCode": "string",
  "bu": "string",
  "applicant": "string"
}

```

DTP 药店产品查询

### 属性

| 名称        | 类型   | 必选  | 约束 | 中文名 | 说明             |
| ----------- | ------ | ----- | ---- | ------ | ---------------- |
| jurDsCode   | string | false | none |        | DTP 关联药店编码 |
| productCode | string | false | none |        | 产品编码         |
| specCode    | string | false | none |        | 品规编码         |
| bu          | string | false | none |        | BU               |
| applicant   | string | false | none |        | 申请人名字或工号 |

<h2 id="tocS_DtpJurDrugProductVo">DtpJurDrugProductVo</h2>

<a id="schemadtpjurdrugproductvo"></a>
<a id="schema_DtpJurDrugProductVo"></a>
<a id="tocSdtpjurdrugproductvo"></a>
<a id="tocsdtpjurdrugproductvo"></a>

```json
{
  "jurDsCode": "string",
  "jurDsSpecCode": "string",
  "productCode": "string",
  "productName": "string",
  "specCode": "string",
  "specName": "string",
  "applicant": "string",
  "applicantCode": "string",
  "applicantDept": "string",
  "bu": "string",
  "ancestors": "string",
  "deptName": "string"
}

```

DTP 药店关联产品信息

### 属性

| 名称          | 类型   | 必选  | 约束 | 中文名 | 说明                   |
| ------------- | ------ | ----- | ---- | ------ | ---------------------- |
| jurDsCode     | string | false | none |        | 关联 DTP 药店 code     |
| jurDsSpecCode | string | false | none |        | 关联 DTP 药店规格 code |
| productCode   | string | false | none |        | 产品编码               |
| productName   | string | false | none |        | 产品名称               |
| specCode      | string | false | none |        | 品规编码               |
| specName      | string | false | none |        | 品规名称               |
| applicant     | string | false | none |        | 申请人                 |
| applicantCode | string | false | none |        | 申请人工号             |
| applicantDept | string | false | none |        | 申请人所在部门         |
| bu            | string | false | none |        | 业务 BU                |
| ancestors     | string | false | none |        | none                   |
| deptName      | string | false | none |        | none                   |

<h2 id="tocS_RListDtpJurDrugProductVo">RListDtpJurDrugProductVo</h2>

<a id="schemarlistdtpjurdrugproductvo"></a>
<a id="schema_RListDtpJurDrugProductVo"></a>
<a id="tocSrlistdtpjurdrugproductvo"></a>
<a id="tocsrlistdtpjurdrugproductvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "jurDsCode": "string",
      "jurDsSpecCode": "string",
      "productCode": "string",
      "productName": "string",
      "specCode": "string",
      "specName": "string",
      "applicant": "string",
      "applicantCode": "string",
      "applicantDept": "string",
      "bu": "string",
      "ancestors": "string",
      "deptName": "string"
    }
  ]
}

```

响应信息主体

### 属性

| 名称 | 类型                                                | 必选  | 约束 | 中文名 | 说明       |
| ---- | --------------------------------------------------- | ----- | ---- | ------ | ---------- |
| code | integer(int32)                                      | false | none |        | 消息状态码 |
| msg  | string                                              | false | none |        | 消息内容   |
| data | [[DtpJurDrugProductVo](#schemadtpjurdrugproductvo)] | false | none |        | 数据对象   |

<h2 id="tocS_GsJurDrugBo">GsJurDrugBo</h2>

<a id="schemagsjurdrugbo"></a>
<a id="schema_GsJurDrugBo"></a>
<a id="tocSgsjurdrugbo"></a>
<a id="tocsgsjurdrugbo"></a>

```json
{
  "pageSize": 0,
  "pageNum": 0,
  "orderByColumn": "string",
  "isAsc": "string",
  "keyword": "string",
  "viewAdmin": true,
  "userId": 0
}

```

### 属性

| 名称          | 类型           | 必选  | 约束 | 中文名 | 说明                     |
| ------------- | -------------- | ----- | ---- | ------ | ------------------------ |
| pageSize      | integer(int32) | false | none |        | 分页大小                 |
| pageNum       | integer(int32) | false | none |        | 当前页数                 |
| orderByColumn | string         | false | none |        | 排序列                   |
| isAsc         | string         | false | none |        | 排序的方向 desc 或者 asc |
| keyword       | string         | false | none |        | 关键字                   |
| viewAdmin     | boolean        | false | none |        | 是否可以查看全部数据     |
| userId        | integer(int64) | false | none |        | 用户 id                  |

<h2 id="tocS_DtpJurDrugVo">DtpJurDrugVo</h2>

<a id="schemadtpjurdrugvo"></a>
<a id="schema_DtpJurDrugVo"></a>
<a id="tocSdtpjurdrugvo"></a>
<a id="tocsdtpjurdrugvo"></a>

```json
{
  "id": "string",
  "jurDsCode": "string",
  "drugMdmCode": "string",
  "drugName": "string",
  "drugProvince": "string",
  "drugCity": "string",
  "drugDistrict": "string",
  "lastTime": "2019-08-24T14:15:22Z",
  "fileList": [
    {
      "id": "string",
      "ossId": "string",
      "url": "string",
      "aeFileId": "string",
      "size": "string",
      "mime_type": "string",
      "name": "string"
    }
  ],
  "drugCode": "string"
}

```

DTP 辖区药店信息

### 属性

| 名称         | 类型                                              | 必选  | 约束 | 中文名 | 说明          |
| ------------ | ------------------------------------------------- | ----- | ---- | ------ | ------------- |
| id           | string                                            | false | none |        | none          |
| jurDsCode    | string                                            | false | none |        | 关联 Code     |
| drugMdmCode  | string                                            | false | none |        | 药店 MDM 编码 |
| drugName     | string                                            | false | none |        | 药店名称      |
| drugProvince | string                                            | false | none |        | 省份          |
| drugCity     | string                                            | false | none |        | 城市          |
| drugDistrict | string                                            | false | none |        | 区县          |
| lastTime     | string(date-time)                                 | false | none |        | 最后更新时间  |
| fileList     | [[AeTaskAttachmentBo](#schemaaetaskattachmentbo)] | false | none |        | 营业执照      |
| drugCode     | string                                            | false | none |        | 药店编码      |

<h2 id="tocS_TableDataInfoDtpJurDrugVo">TableDataInfoDtpJurDrugVo</h2>

<a id="schematabledatainfodtpjurdrugvo"></a>
<a id="schema_TableDataInfoDtpJurDrugVo"></a>
<a id="tocStabledatainfodtpjurdrugvo"></a>
<a id="tocstabledatainfodtpjurdrugvo"></a>

```json
{
  "total": 0,
  "rows": [
    {
      "id": "string",
      "jurDsCode": "string",
      "drugMdmCode": "string",
      "drugName": "string",
      "drugProvince": "string",
      "drugCity": "string",
      "drugDistrict": "string",
      "lastTime": "2019-08-24T14:15:22Z",
      "fileList": [
        {
          "id": "string",
          "ossId": "string",
          "url": "string",
          "aeFileId": "string",
          "size": "string",
          "mime_type": "string",
          "name": "string"
        }
      ],
      "drugCode": "string"
    }
  ],
  "code": 0,
  "msg": "string"
}

```

表格分页数据对象

### 属性

| 名称  | 类型                                  | 必选  | 约束 | 中文名 | 说明       |
| ----- | ------------------------------------- | ----- | ---- | ------ | ---------- |
| total | integer(int64)                        | false | none |        | 总记录数   |
| rows  | [[DtpJurDrugVo](#schemadtpjurdrugvo)] | false | none |        | 列表数据   |
| code  | integer(int32)                        | false | none |        | 消息状态码 |
| msg   | string                                | false | none |        | 消息内容   |

<h2 id="tocS_RDtpJurDrugVo">RDtpJurDrugVo</h2>

<a id="schemardtpjurdrugvo"></a>
<a id="schema_RDtpJurDrugVo"></a>
<a id="tocSrdtpjurdrugvo"></a>
<a id="tocsrdtpjurdrugvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "id": "string",
    "jurDsCode": "string",
    "drugMdmCode": "string",
    "drugName": "string",
    "drugProvince": "string",
    "drugCity": "string",
    "drugDistrict": "string",
    "lastTime": "2019-08-24T14:15:22Z",
    "fileList": [
      {
        "id": "string",
        "ossId": "string",
        "url": "string",
        "aeFileId": "string",
        "size": "string",
        "mime_type": "string",
        "name": "string"
      }
    ],
    "drugCode": "string"
  }
}

```

响应信息主体

### 属性

| 名称 | 类型                                | 必选  | 约束 | 中文名 | 说明             |
| ---- | ----------------------------------- | ----- | ---- | ------ | ---------------- |
| code | integer(int32)                      | false | none |        | 消息状态码       |
| msg  | string                              | false | none |        | 消息内容         |
| data | [DtpJurDrugVo](#schemadtpjurdrugvo) | false | none |        | DTP 辖区药店信息 |

<h2 id="tocS_DtpAssistantWriteRecordVo">DtpAssistantWriteRecordVo</h2>

<a id="schemadtpassistantwriterecordvo"></a>
<a id="schema_DtpAssistantWriteRecordVo"></a>
<a id="tocSdtpassistantwriterecordvo"></a>
<a id="tocsdtpassistantwriterecordvo"></a>

```json
{
  "flowId": 0,
  "status": "string",
  "refuseReason": "string",
  "ossIdList": [
    "string"
  ],
  "tagList": [
    {}
  ],
  "drugSocreList": [
    {
      "scoreItemName": "string",
      "scoreItem": [
        {
          "scorePointsTitle": "string",
          "score": "string",
          "inputScore": "string"
        }
      ]
    }
  ]
}

```

DTP 查看自己填写的资料信息

### 属性

| 名称          | 类型                            | 必选  | 约束 | 中文名 | 说明                                      |
| ------------- | ------------------------------- | ----- | ---- | ------ | ----------------------------------------- |
| flowId        | integer(int64)                  | false | none |        | 审批流 id                                 |
| status        | string                          | false | none |        | 操作状态（0 待操作 1 接受 2 拒绝 3 提交） |
| refuseReason  | string                          | false | none |        | 拒绝原因                                  |
| ossIdList     | [string]                        | false | none |        | 附件集合                                  |
| tagList       | [object]                        | false | none |        | 标签集合                                  |
| drugSocreList | [[DrugScore](#schemadrugscore)] | false | none |        | 药店评分                                  |

<h2 id="tocS_RDtpAssistantWriteRecordVo">RDtpAssistantWriteRecordVo</h2>

<a id="schemardtpassistantwriterecordvo"></a>
<a id="schema_RDtpAssistantWriteRecordVo"></a>
<a id="tocSrdtpassistantwriterecordvo"></a>
<a id="tocsrdtpassistantwriterecordvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "flowId": 0,
    "status": "string",
    "refuseReason": "string",
    "ossIdList": [
      "string"
    ],
    "tagList": [
      {}
    ],
    "drugSocreList": [
      {
        "scoreItemName": "string",
        "scoreItem": [
          {
            "scorePointsTitle": null,
            "score": null,
            "inputScore": null
          }
        ]
      }
    ]
  }
}

```

响应信息主体

### 属性

| 名称 | 类型                                                          | 必选  | 约束 | 中文名 | 说明                       |
| ---- | ------------------------------------------------------------- | ----- | ---- | ------ | -------------------------- |
| code | integer(int32)                                                | false | none |        | 消息状态码                 |
| msg  | string                                                        | false | none |        | 消息内容                   |
| data | [DtpAssistantWriteRecordVo](#schemadtpassistantwriterecordvo) | false | none |        | DTP 查看自己填写的资料信息 |

<h2 id="tocS_AssistantWriteRecordVo">AssistantWriteRecordVo</h2>

<a id="schemaassistantwriterecordvo"></a>
<a id="schema_AssistantWriteRecordVo"></a>
<a id="tocSassistantwriterecordvo"></a>
<a id="tocsassistantwriterecordvo"></a>

```json
{
  "empCode": "string",
  "empName": "string",
  "ossIdList": [
    "string"
  ],
  "drugScore": [
    {
      "scoreItemName": "string",
      "scoreItem": [
        {
          "scorePointsTitle": "string",
          "score": "string",
          "inputScore": "string"
        }
      ]
    }
  ]
}

```

### 属性

| 名称      | 类型                            | 必选  | 约束 | 中文名 | 说明 |
| --------- | ------------------------------- | ----- | ---- | ------ | ---- |
| empCode   | string                          | false | none |        | none |
| empName   | string                          | false | none |        | none |
| ossIdList | [string]                        | false | none |        | none |
| drugScore | [[DrugScore](#schemadrugscore)] | false | none |        | none |

<h2 id="tocS_DtpAssistantAdminVo">DtpAssistantAdminVo</h2>

<a id="schemadtpassistantadminvo"></a>
<a id="schema_DtpAssistantAdminVo"></a>
<a id="tocSdtpassistantadminvo"></a>
<a id="tocsdtpassistantadminvo"></a>

```json
{
  "flowId": 0,
  "tagList": [
    {}
  ],
  "scoreAvg": 0,
  "assistantWriteList": [
    {
      "empCode": "string",
      "empName": "string",
      "ossIdList": [
        "string"
      ],
      "drugScore": [
        {
          "scoreItemName": "string",
          "scoreItem": [
            {}
          ]
        }
      ]
    }
  ]
}

```

查看专员上传的资料信息

### 属性

| 名称               | 类型                                                      | 必选  | 约束 | 中文名 | 说明           |
| ------------------ | --------------------------------------------------------- | ----- | ---- | ------ | -------------- |
| flowId             | integer(int64)                                            | false | none |        | 审批流 id      |
| tagList            | [object]                                                  | false | none |        | 标签集合       |
| scoreAvg           | number                                                    | false | none |        | 药店平均分     |
| assistantWriteList | [[AssistantWriteRecordVo](#schemaassistantwriterecordvo)] | false | none |        | 专员的填写记录 |

<h2 id="tocS_RListDtpAssistantAdminVo">RListDtpAssistantAdminVo</h2>

<a id="schemarlistdtpassistantadminvo"></a>
<a id="schema_RListDtpAssistantAdminVo"></a>
<a id="tocSrlistdtpassistantadminvo"></a>
<a id="tocsrlistdtpassistantadminvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "flowId": 0,
      "tagList": [
        {}
      ],
      "scoreAvg": 0,
      "assistantWriteList": [
        {
          "empCode": "string",
          "empName": "string",
          "ossIdList": [
            "string"
          ],
          "drugScore": [
            {}
          ]
        }
      ]
    }
  ]
}

```

响应信息主体

### 属性

| 名称 | 类型                                                | 必选  | 约束 | 中文名 | 说明       |
| ---- | --------------------------------------------------- | ----- | ---- | ------ | ---------- |
| code | integer(int32)                                      | false | none |        | 消息状态码 |
| msg  | string                                              | false | none |        | 消息内容   |
| data | [[DtpAssistantAdminVo](#schemadtpassistantadminvo)] | false | none |        | 数据对象   |

<h2 id="tocS_AssignEmpBo">AssignEmpBo</h2>

<a id="schemaassignempbo"></a>
<a id="schema_AssignEmpBo"></a>
<a id="tocSassignempbo"></a>
<a id="tocsassignempbo"></a>

```json
{
  "empCode": "string",
  "empName": "string"
}

```

### 属性

| 名称    | 类型   | 必选  | 约束 | 中文名 | 说明 |
| ------- | ------ | ----- | ---- | ------ | ---- |
| empCode | string | false | none |        | none |
| empName | string | false | none |        | none |

<h2 id="tocS_GsDtpDrugApplyAssignEmpBo">GsDtpDrugApplyAssignEmpBo</h2>

<a id="schemagsdtpdrugapplyassignempbo"></a>
<a id="schema_GsDtpDrugApplyAssignEmpBo"></a>
<a id="tocSgsdtpdrugapplyassignempbo"></a>
<a id="tocsgsdtpdrugapplyassignempbo"></a>

```json
{
  "flowId": 0,
  "assignEmpBoList": [
    {
      "empCode": "string",
      "empName": "string"
    }
  ]
}

```

### 属性

| 名称            | 类型                                | 必选  | 约束 | 中文名 | 说明      |
| --------------- | ----------------------------------- | ----- | ---- | ------ | --------- |
| flowId          | integer(int64)                      | false | none |        | 审批流 id |
| assignEmpBoList | [[AssignEmpBo](#schemaassignempbo)] | false | none |        | 专员列表  |

<h2 id="tocS_RDtpAssistantAdminVo">RDtpAssistantAdminVo</h2>

<a id="schemardtpassistantadminvo"></a>
<a id="schema_RDtpAssistantAdminVo"></a>
<a id="tocSrdtpassistantadminvo"></a>
<a id="tocsrdtpassistantadminvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "flowId": 0,
    "tagList": [
      {}
    ],
    "scoreAvg": 0,
    "assistantWriteList": [
      {
        "empCode": "string",
        "empName": "string",
        "ossIdList": [
          "string"
        ],
        "drugScore": [
          {
            "scoreItemName": null,
            "scoreItem": null
          }
        ]
      }
    ]
  }
}

```

响应信息主体

### 属性

| 名称 | 类型                                              | 必选  | 约束 | 中文名 | 说明                   |
| ---- | ------------------------------------------------- | ----- | ---- | ------ | ---------------------- |
| code | integer(int32)                                    | false | none |        | 消息状态码             |
| msg  | string                                            | false | none |        | 消息内容               |
| data | [DtpAssistantAdminVo](#schemadtpassistantadminvo) | false | none |        | 查看专员上传的资料信息 |

<h2 id="tocS_DtpJurProductVo">DtpJurProductVo</h2>

<a id="schemadtpjurproductvo"></a>
<a id="schema_DtpJurProductVo"></a>
<a id="tocSdtpjurproductvo"></a>
<a id="tocsdtpjurproductvo"></a>

```json
{
  "productCode": "string",
  "productName": "string"
}

```

辖区店产品查询

### 属性

| 名称        | 类型   | 必选  | 约束 | 中文名 | 说明     |
| ----------- | ------ | ----- | ---- | ------ | -------- |
| productCode | string | false | none |        | 产品编码 |
| productName | string | false | none |        | 产品名称 |

<h2 id="tocS_RListDtpJurProductVo">RListDtpJurProductVo</h2>

<a id="schemarlistdtpjurproductvo"></a>
<a id="schema_RListDtpJurProductVo"></a>
<a id="tocSrlistdtpjurproductvo"></a>
<a id="tocsrlistdtpjurproductvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "productCode": "string",
      "productName": "string"
    }
  ]
}

```

响应信息主体

### 属性

| 名称 | 类型                                        | 必选  | 约束 | 中文名 | 说明       |
| ---- | ------------------------------------------- | ----- | ---- | ------ | ---------- |
| code | integer(int32)                              | false | none |        | 消息状态码 |
| msg  | string                                      | false | none |        | 消息内容   |
| data | [[DtpJurProductVo](#schemadtpjurproductvo)] | false | none |        | 数据对象   |

<h2 id="tocS_DtpDrugApplyPageBo">DtpDrugApplyPageBo</h2>

<a id="schemadtpdrugapplypagebo"></a>
<a id="schema_DtpDrugApplyPageBo"></a>
<a id="tocSdtpdrugapplypagebo"></a>
<a id="tocsdtpdrugapplypagebo"></a>

```json
{
  "pageSize": 0,
  "pageNum": 0,
  "orderByColumn": "string",
  "isAsc": "string",
  "status": "string",
  "applyType": "string",
  "aeFlowId": 0,
  "drugName": "string",
  "source": "string",
  "instanceStates": [
    "string"
  ],
  "createBy": 0,
  "createCode": "string"
}

```

### 属性

| 名称           | 类型           | 必选  | 约束 | 中文名 | 说明                     |
| -------------- | -------------- | ----- | ---- | ------ | ------------------------ |
| pageSize       | integer(int32) | false | none |        | 分页大小                 |
| pageNum        | integer(int32) | false | none |        | 当前页数                 |
| orderByColumn  | string         | false | none |        | 排序列                   |
| isAsc          | string         | false | none |        | 排序的方向 desc 或者 asc |
| status         | string         | false | none |        | 审批状态                 |
| applyType      | string         | false | none |        | 申请类型                 |
| aeFlowId       | integer(int64) | false | none |        | AE 流程执行 id           |
| drugName       | string         | false | none |        | 药店名称或编码           |
| source         | string         | false | none |        | 来源                     |
| instanceStates | [string]       | false | none |        | none                     |
| createBy       | integer(int64) | false | none |        | none                     |
| createCode     | string         | false | none |        | none                     |

<h2 id="tocS_DtpApplyVo">DtpApplyVo</h2>

<a id="schemadtpapplyvo"></a>
<a id="schema_DtpApplyVo"></a>
<a id="tocSdtpapplyvo"></a>
<a id="tocsdtpapplyvo"></a>

```json
{
  "createBy": 0,
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": 0,
  "updateTime": "2019-08-24T14:15:22Z",
  "params": {
    "property1": {},
    "property2": {}
  },
  "id": 0,
  "applyType": "string",
  "status": "string",
  "jurCode": "string",
  "dsCode": "string",
  "dsName": "string",
  "applyTime": "2019-08-24T14:15:22Z",
  "applicant": "string",
  "applicantCode": "string",
  "postCode": "string",
  "postName": "string",
  "deptCode": "string",
  "deptName": "string",
  "ancestors": "string",
  "applyContent": "string",
  "applyNodeInfo": "string",
  "aeFlowId": 0,
  "cancelFlag": "string",
  "cancelTime": "2019-08-24T14:15:22Z",
  "completeFlag": "string",
  "completeTime": "2019-08-24T14:15:22Z",
  "bu": "string",
  "delFlag": "string",
  "remark": "string",
  "extendInfo": "string",
  "applyId": "string",
  "flowId": "string",
  "dsMdmCode": "string",
  "dsProvinceName": "string",
  "dsCityName": "string",
  "dsAreaName": "string",
  "actorId": 0,
  "dtpCode": "string",
  "dtpStatus": "string",
  "applyButtonStatus": "string",
  "instanceState": "string"
}

```

DTP 药店申请列表

### 属性

| 名称                       | 类型              | 必选  | 约束 | 中文名 | 说明                                                                           |
| -------------------------- | ----------------- | ----- | ---- | ------ | ------------------------------------------------------------------------------ |
| createBy                   | integer(int64)    | false | none |        | 创建者                                                                         |
| createTime                 | string(date-time) | false | none |        | 创建时间                                                                       |
| updateBy                   | integer(int64)    | false | none |        | 更新者                                                                         |
| updateTime                 | string(date-time) | false | none |        | 更新时间                                                                       |
| params                     | object            | false | none |        | 请求参数                                                                       |
| » **additionalProperties** | object            | false | none |        | none                                                                           |
| id                         | integer(int64)    | false | none |        | none                                                                           |
| applyType                  | string            | false | none |        | 审批类型[jurDtpDsInsert-新增关联药店，jurDtpDsDeleted-删除关联药店]            |
| status                     | string            | false | none |        | 申请状态[-2 已撤回，-1 驳回，1 审批中，2 业务已审批，3 主数据已审批]           |
| jurCode                    | string            | false | none |        | 辖区编码                                                                       |
| dsCode                     | string            | false | none |        | 药店编号                                                                       |
| dsName                     | string            | false | none |        | 药店名称                                                                       |
| applyTime                  | string(date-time) | false | none |        | 申请时间                                                                       |
| applicant                  | string            | false | none |        | 申请人                                                                         |
| applicantCode              | string            | false | none |        | 申请人工号                                                                     |
| postCode                   | string            | false | none |        | 岗位编码                                                                       |
| postName                   | string            | false | none |        | 岗位名称                                                                       |
| deptCode                   | string            | false | none |        | 部门编码                                                                       |
| deptName                   | string            | false | none |        | 部门名称                                                                       |
| ancestors                  | string            | false | none |        | 部门层级结构                                                                   |
| applyContent               | string            | false | none |        | 申请表单内容（快照）                                                           |
| applyNodeInfo              | string            | false | none |        | 审批节点信息                                                                   |
| aeFlowId                   | integer(int64)    | false | none |        | AE 流程执行 id                                                                 |
| cancelFlag                 | string            | false | none |        | 取消标识[0 进行中，1 取消]                                                     |
| cancelTime                 | string(date-time) | false | none |        | 取消时间                                                                       |
| completeFlag               | string            | false | none |        | 完成标识[0 进行中，1 完成]                                                     |
| completeTime               | string(date-time) | false | none |        | 完成时间                                                                       |
| bu                         | string            | false | none |        | BU                                                                             |
| delFlag                    | string            | false | none |        | 删除标志（0 代表存在 2 代表删除）                                              |
| remark                     | string            | false | none |        | 备注                                                                           |
| extendInfo                 | string            | false | none |        | 扩展补充信息                                                                   |
| applyId                    | string            | false | none |        | 申请单 id                                                                      |
| flowId                     | string            | false | none |        | 流程 id                                                                        |
| dsMdmCode                  | string            | false | none |        | 药店 MDM 编码                                                                  |
| dsProvinceName             | string            | false | none |        | 省份名称                                                                       |
| dsCityName                 | string            | false | none |        | 城市名称                                                                       |
| dsAreaName                 | string            | false | none |        | 区域名称                                                                       |
| actorId                    | integer(int64)    | false | none |        | none                                                                           |
| dtpCode                    | string            | false | none |        | none                                                                           |
| dtpStatus                  | string            | false | none |        | none                                                                           |
| applyButtonStatus          | string            | false | none |        | 申请列表按钮展示状态 0:详情 1:管理员审批 2:DTP 专员查看详情 3:DTP 专员填写资料 |
| instanceState              | string            | false | none |        | 审批单状态                                                                     |

<h2 id="tocS_RTableDataInfoDtpApplyVo">RTableDataInfoDtpApplyVo</h2>

<a id="schemartabledatainfodtpapplyvo"></a>
<a id="schema_RTableDataInfoDtpApplyVo"></a>
<a id="tocSrtabledatainfodtpapplyvo"></a>
<a id="tocsrtabledatainfodtpapplyvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "total": 0,
    "rows": [
      {
        "createBy": 0,
        "createTime": "2019-08-24T14:15:22Z",
        "updateBy": 0,
        "updateTime": "2019-08-24T14:15:22Z",
        "params": {
          "property1": {},
          "property2": {}
        },
        "id": 0,
        "applyType": "string",
        "status": "string",
        "jurCode": "string",
        "dsCode": "string",
        "dsName": "string",
        "applyTime": "2019-08-24T14:15:22Z",
        "applicant": "string",
        "applicantCode": "string",
        "postCode": "string",
        "postName": "string",
        "deptCode": "string",
        "deptName": "string",
        "ancestors": "string",
        "applyContent": "string",
        "applyNodeInfo": "string",
        "aeFlowId": 0,
        "cancelFlag": "string",
        "cancelTime": "2019-08-24T14:15:22Z",
        "completeFlag": "string",
        "completeTime": "2019-08-24T14:15:22Z",
        "bu": "string",
        "delFlag": "string",
        "remark": "string",
        "extendInfo": "string",
        "applyId": "string",
        "flowId": "string",
        "dsMdmCode": "string",
        "dsProvinceName": "string",
        "dsCityName": "string",
        "dsAreaName": "string",
        "actorId": 0,
        "dtpCode": "string",
        "dtpStatus": "string",
        "applyButtonStatus": "string",
        "instanceState": "string"
      }
    ],
    "code": 0,
    "msg": "string"
  }
}

```

响应信息主体

### 属性

| 名称 | 类型                                                      | 必选  | 约束 | 中文名 | 说明             |
| ---- | --------------------------------------------------------- | ----- | ---- | ------ | ---------------- |
| code | integer(int32)                                            | false | none |        | 消息状态码       |
| msg  | string                                                    | false | none |        | 消息内容         |
| data | [TableDataInfoDtpApplyVo](#schematabledatainfodtpapplyvo) | false | none |        | 表格分页数据对象 |

<h2 id="tocS_TableDataInfoDtpApplyVo">TableDataInfoDtpApplyVo</h2>

<a id="schematabledatainfodtpapplyvo"></a>
<a id="schema_TableDataInfoDtpApplyVo"></a>
<a id="tocStabledatainfodtpapplyvo"></a>
<a id="tocstabledatainfodtpapplyvo"></a>

```json
{
  "total": 0,
  "rows": [
    {
      "createBy": 0,
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": 0,
      "updateTime": "2019-08-24T14:15:22Z",
      "params": {
        "property1": {},
        "property2": {}
      },
      "id": 0,
      "applyType": "string",
      "status": "string",
      "jurCode": "string",
      "dsCode": "string",
      "dsName": "string",
      "applyTime": "2019-08-24T14:15:22Z",
      "applicant": "string",
      "applicantCode": "string",
      "postCode": "string",
      "postName": "string",
      "deptCode": "string",
      "deptName": "string",
      "ancestors": "string",
      "applyContent": "string",
      "applyNodeInfo": "string",
      "aeFlowId": 0,
      "cancelFlag": "string",
      "cancelTime": "2019-08-24T14:15:22Z",
      "completeFlag": "string",
      "completeTime": "2019-08-24T14:15:22Z",
      "bu": "string",
      "delFlag": "string",
      "remark": "string",
      "extendInfo": "string",
      "applyId": "string",
      "flowId": "string",
      "dsMdmCode": "string",
      "dsProvinceName": "string",
      "dsCityName": "string",
      "dsAreaName": "string",
      "actorId": 0,
      "dtpCode": "string",
      "dtpStatus": "string",
      "applyButtonStatus": "string",
      "instanceState": "string"
    }
  ],
  "code": 0,
  "msg": "string"
}

```

表格分页数据对象

### 属性

| 名称  | 类型                              | 必选  | 约束 | 中文名 | 说明       |
| ----- | --------------------------------- | ----- | ---- | ------ | ---------- |
| total | integer(int64)                    | false | none |        | 总记录数   |
| rows  | [[DtpApplyVo](#schemadtpapplyvo)] | false | none |        | 列表数据   |
| code  | integer(int32)                    | false | none |        | 消息状态码 |
| msg   | string                            | false | none |        | 消息内容   |

<h2 id="tocS_RDtpApplyVo">RDtpApplyVo</h2>

<a id="schemardtpapplyvo"></a>
<a id="schema_RDtpApplyVo"></a>
<a id="tocSrdtpapplyvo"></a>
<a id="tocsrdtpapplyvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "createBy": 0,
    "createTime": "2019-08-24T14:15:22Z",
    "updateBy": 0,
    "updateTime": "2019-08-24T14:15:22Z",
    "params": {
      "property1": {},
      "property2": {}
    },
    "id": 0,
    "applyType": "string",
    "status": "string",
    "jurCode": "string",
    "dsCode": "string",
    "dsName": "string",
    "applyTime": "2019-08-24T14:15:22Z",
    "applicant": "string",
    "applicantCode": "string",
    "postCode": "string",
    "postName": "string",
    "deptCode": "string",
    "deptName": "string",
    "ancestors": "string",
    "applyContent": "string",
    "applyNodeInfo": "string",
    "aeFlowId": 0,
    "cancelFlag": "string",
    "cancelTime": "2019-08-24T14:15:22Z",
    "completeFlag": "string",
    "completeTime": "2019-08-24T14:15:22Z",
    "bu": "string",
    "delFlag": "string",
    "remark": "string",
    "extendInfo": "string",
    "applyId": "string",
    "flowId": "string",
    "dsMdmCode": "string",
    "dsProvinceName": "string",
    "dsCityName": "string",
    "dsAreaName": "string",
    "actorId": 0,
    "dtpCode": "string",
    "dtpStatus": "string",
    "applyButtonStatus": "string",
    "instanceState": "string"
  }
}

```

响应信息主体

### 属性

| 名称 | 类型                            | 必选  | 约束 | 中文名 | 说明             |
| ---- | ------------------------------- | ----- | ---- | ------ | ---------------- |
| code | integer(int32)                  | false | none |        | 消息状态码       |
| msg  | string                          | false | none |        | 消息内容         |
| data | [DtpApplyVo](#schemadtpapplyvo) | false | none |        | DTP 药店申请列表 |
