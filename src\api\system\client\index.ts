import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ClientVO, ClientForm, ClientQuery } from '@/api/system/client/types';

/**
 * 查询客户端管理列表
 * @param query
 * @returns {*}
 */

export const listClient = (query?: ClientQuery): AxiosPromise<ClientVO[]> => {
  return request({
    url: '/plt/client/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询客户端管理详细
 * @param id
 */
export const getClient = (id: string | number): AxiosPromise<ClientVO> => {
  return request({
    url: '/plt/client/' + id,
    method: 'get'
  });
};

/**
 * 新增客户端管理
 * @param data
 */
export const addClient = (data: ClientForm) => {
  return request({
    url: '/plt/client',
    method: 'post',
    data: data
  });
};

/**
 * 修改客户端管理
 * @param data
 */
export const updateClient = (data: ClientForm) => {
  return request({
    url: '/plt/client',
    method: 'put',
    data: data
  });
};

/**
 * 删除客户端管理
 * @param id
 */
export const delClient = (id: string | number | Array<string | number>) => {
  return request({
    url: '/plt/client/' + id,
    method: 'delete'
  });
};

/**
 * 状态修改
 * @param id ID
 * @param status 状态
 */
export function changeStatus(id: number | string, status: string) {
  const data = {
    id,
    status
  };
  return request({
    url: '/plt/client/changeStatus',
    method: 'put',
    data: data
  });
}
