{"version": 3, "sources": ["../../node_modules/@form-create/utils/lib/tocase.js"], "sourcesContent": ["export default function toCase(str) {\n    const to = str.replace(/(-[a-z])/g, function (v) {\n        return v.replace('-', '').toLocaleUpperCase();\n    });\n\n    return lower(to);\n}\n\nexport function lower(str) {\n    return str.replace(str[0], str[0].toLowerCase());\n}\n"], "mappings": ";;;AAAe,SAAR,OAAwB,KAAK;AAChC,QAAM,KAAK,IAAI,QAAQ,aAAa,SAAU,GAAG;AAC7C,WAAO,EAAE,QAAQ,KAAK,EAAE,EAAE,kBAAkB;AAAA,EAChD,CAAC;AAED,SAAO,MAAM,EAAE;AACnB;AAEO,SAAS,MAAM,KAAK;AACvB,SAAO,IAAI,QAAQ,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,YAAY,CAAC;AACnD;", "names": []}