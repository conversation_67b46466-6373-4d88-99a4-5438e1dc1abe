/**
 * HCO管理相关的composable
 *
 * 提供HCO列表查询、详情查询、标签查询等功能
 */

import { ref, reactive, computed } from 'vue';
import { ElMessage } from 'element-plus';

/**
 * 查询条件持久化管理
 * @param storageKey 存储键，用于区分不同业务场景
 */
export function useQueryPersistence(storageKey: string = 'hco_query_conditions') {
  /**
   * 保存查询条件到localStorage
   */
  const saveQueryConditions = (basicQuery: Record<string, any>, advancedFilters: any[], queryParams: any) => {
    try {
      const queryState = {
        basicQuery,
        advancedFilters,
        queryParams: {
          conditions: queryParams.conditions,
          logic: queryParams.logic,
          pageSize: queryParams.pageSize,
          pageNum: queryParams.pageNum
        },
        timestamp: Date.now()
      };
      localStorage.setItem(storageKey, JSON.stringify(queryState));
    } catch (error) {
      console.warn('保存查询条件失败:', error);
    }
  };

  /**
   * 从localStorage恢复查询条件
   */
  const restoreQueryConditions = () => {
    try {
      const stored = localStorage.getItem(storageKey);
      if (stored) {
        const queryState = JSON.parse(stored);
        // 检查数据是否过期（24小时）
        const isExpired = Date.now() - queryState.timestamp > 24 * 60 * 60 * 1000;
        if (!isExpired) {
          return queryState;
        } else {
          // 清除过期数据
          localStorage.removeItem(storageKey);
        }
      }
    } catch (error) {
      console.warn('恢复查询条件失败:', error);
      localStorage.removeItem(storageKey);
    }
    return null;
  };

  /**
   * 清除保存的查询条件
   */
  const clearQueryConditions = () => {
    try {
      localStorage.removeItem(storageKey);
    } catch (error) {
      console.warn('清除查询条件失败:', error);
    }
  };

  return {
    saveQueryConditions,
    restoreQueryConditions,
    clearQueryConditions
  };
}

/**
 * 将简单的查询条件转换为QueryCondition格式
 */
export function convertToQueryConditions(searchConditions: Record<string, any>): QueryCondition[] {
  const conditions: QueryCondition[] = [];

  Object.entries(searchConditions).forEach(([field, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      conditions.push({
        field,
        operator: 'contains', // 默认使用包含操作
        value: String(value)
      });
    }
  });

  return conditions;
}
import {
  hcoPageQuery,
  queryByIns,
  insTagQuery,
  insLicenseQuery,
  headerQuery,
  insDeptQuery,
  customizeHeader,
  type HcoPageQuery,
  type QueryCondition,
  type HcpPageQuery,
  type MdmInstitutionVo,
  type MdmCustomerVo,
  type CustomizeHeaderVo
} from '../hco';

/**
 * HCO列表管理（带查询条件持久化）
 * @param businessType 业务类型，用于区分不同的业务场景，如 'ins_mdm' 或 'drug_mdm'
 */
export function useHcoList(businessType: string = 'default') {
  const loading = ref(false);
  const tableData = ref<MdmInstitutionVo[]>([]);
  const total = ref(0);

  // 根据业务类型生成唯一的存储键
  const storageKey = `hco_query_conditions_${businessType}`;

  // 获取持久化管理器
  const { saveQueryConditions, restoreQueryConditions, clearQueryConditions } = useQueryPersistence(storageKey);

  // 查询参数
  const queryParams = reactive<HcoPageQuery>({
    conditions: [],
    logic: 'and',
    pageSize: 20,
    pageNum: 1
  });

  /**
   * 保存当前查询状态（供外部调用）
   */
  const saveCurrentQueryState = (basicQuery: Record<string, any>, advancedFilters: any[]) => {
    saveQueryConditions(basicQuery, advancedFilters, queryParams);
  };

  /**
   * 恢复查询状态（供外部调用）
   */
  const restoreQueryState = () => {
    return restoreQueryConditions();
  };

  /**
   * 清除查询状态（供外部调用）
   */
  const clearQueryState = () => {
    clearQueryConditions();
  };

  /**
   * 获取HCO列表
   */
  const fetchHcoList = async (conditions?: QueryCondition[], logic = 'and') => {
    loading.value = true;
    try {
      // 如果传入了新的查询条件，则保存到queryParams中
      if (conditions !== undefined) {
        queryParams.conditions = conditions;
        queryParams.logic = logic;
      }

      // 构建查询参数
      const searchParams: HcoPageQuery = {
        conditions: queryParams.conditions,
        logic: queryParams.logic,
        pageSize: queryParams.pageSize,
        pageNum: queryParams.pageNum,
        status: '0'
      };

      const response = await hcoPageQuery(searchParams);

      if (response.code === 200) {
        tableData.value = response?.data?.rows || [];
        total.value = response?.data?.total;
      } else {
        ElMessage.error(response.msg || '查询失败');
        tableData.value = [];
        total.value = 0;
      }
    } catch (error) {
      console.error('HCO列表查询失败:', error);
      ElMessage.error('查询失败，请稍后重试');
      tableData.value = [];
      total.value = 0;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 重置查询参数
   */
  const resetQuery = () => {
    Object.assign(queryParams, {
      conditions: [],
      logic: 'and',
      pageSize: 20,
      pageNum: 1
    });
    // 清除持久化的查询条件
    clearQueryConditions();
  };

  /**
   * 分页改变
   */
  const handlePageChange = (page: number) => {
    queryParams.pageNum = page;
    // 不传递条件参数，使用已保存的查询条件
    fetchHcoList();
  };

  /**
   * 分页大小改变
   */
  const handleSizeChange = (size: number) => {
    queryParams.pageSize = size;
    queryParams.pageNum = 1;
    // 不传递条件参数，使用已保存的查询条件
    fetchHcoList();
  };

  return {
    loading,
    tableData,
    total,
    queryParams,
    fetchHcoList,
    resetQuery,
    handlePageChange,
    handleSizeChange,
    // 新增的持久化相关方法
    saveCurrentQueryState,
    restoreQueryState,
    clearQueryState
  };
}

/**
 * HCP列表管理
 */
export function useHcpList() {
  const loading = ref(false);
  const tableData = ref<MdmCustomerVo[]>([]);
  const total = ref(0);

  /**
   * 获取HCP列表
   */
  const fetchHcpList = async (params: HcpPageQuery) => {
    loading.value = true;
    try {
      const response = await queryByIns(params);

      if (response.code === 200) {
        tableData.value = response.rows || [];
        total.value = response.total;
      } else {
        ElMessage.error(response.msg || 'HCP查询失败');
        tableData.value = [];
        total.value = 0;
      }
    } catch (error) {
      console.error('HCP列表查询失败:', error);
      // ElMessage.error('HCP查询失败，请稍后重试');
      tableData.value = [];
      total.value = 0;
    } finally {
      loading.value = false;
    }
  };

  return {
    loading,
    tableData,
    total,
    fetchHcpList
  };
}

/**
 * 机构详情管理
 */
export function useHcoDetail() {
  const loading = ref(false);
  const institutionInfo = ref<MdmInstitutionVo | null>(null);
  const tags = ref<string[]>([]);
  const licenses = ref<string[]>([]);
  const departments = ref<string[]>([]);

  /**
   * 获取机构标签
   */
  const fetchInsTags = async (insCode: string) => {
    try {
      const response = await insTagQuery(insCode);
      tags.value = response.data || [];
    } catch (error) {
      console.error('机构标签查询失败:', error);
      tags.value = [];
    }
  };

  /**
   * 获取机构证照
   */
  const fetchInsLicenses = async (insCode: string) => {
    try {
      const response = await insLicenseQuery(insCode);
      licenses.value = response.data || [];
    } catch (error) {
      console.error('机构证照查询失败:', error);
      licenses.value = [];
    }
  };

  /**
   * 获取机构科室
   */
  const fetchInsDepartments = async (insCode: string) => {
    try {
      const response = await insDeptQuery(insCode);
      departments.value = response.data || [];
    } catch (error) {
      console.error('机构科室查询失败:', error);
      departments.value = [];
    }
  };

  /**
   * 获取机构完整信息
   */
  const fetchInstitutionDetail = async (insCode: string) => {
    loading.value = true;
    try {
      // 并行获取各种信息
      await Promise.all([fetchInsTags(insCode), fetchInsLicenses(insCode), fetchInsDepartments(insCode)]);
    } catch (error) {
      console.error('机构详情查询失败:', error);
    } finally {
      loading.value = false;
    }
  };

  return {
    loading,
    institutionInfo,
    tags,
    licenses,
    departments,
    fetchInsTags,
    fetchInsLicenses,
    fetchInsDepartments,
    fetchInstitutionDetail
  };
}

/**
 * 自定义表头管理
 */
export function useCustomHeader() {
  const loading = ref(false);
  const headers = ref<CustomizeHeaderVo[]>([]);

  /**
   * 获取自定义表头配置
   */
  const fetchHeaders = async () => {
    loading.value = true;
    try {
      const response = await headerQuery();
      if (response.code === 200) {
        headers.value = response.data || [];
      } else {
        ElMessage.error(response.data.msg || '表头配置获取失败');
      }
    } catch (error) {
      console.error('表头配置获取失败:', error);
      ElMessage.error('表头配置获取失败');
    } finally {
      loading.value = false;
    }
  };

  /**
   * 保存自定义表头配置
   */
  const saveHeaders = async (headerData: CustomizeHeaderVo[]) => {
    loading.value = true;
    try {
      const response = await customizeHeader(headerData);
      if (response.code === 200) {
        ElMessage.success('表头配置保存成功');
        headers.value = headerData;
        return true;
      } else {
        ElMessage.error(response.msg || '表头配置保存失败');
        return false;
      }
    } catch (error) {
      console.error('表头配置保存失败:', error);
      ElMessage.error('表头配置保存失败');
      return false;
    } finally {
      loading.value = false;
    }
  };

  return {
    loading,
    headers,
    fetchHeaders,
    saveHeaders
  };
}
