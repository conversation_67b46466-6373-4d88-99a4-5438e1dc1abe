import request from '@/utils/request';
import { AxiosPromise } from 'axios';

export const deptListApi = (query: any, version: any) => {
  return request({
    url: `/hr/dept${version ? '-version' : ''}/list`,
    method: 'get',
    params: query
  });
};
export const deptUpdateApi = (data: any, version: any) => {
  return request({
    url: `/hr/dept${version ? '-version' : ''}/update`,
    method: 'post',
    data
  });
};

export const versionMonthApi = () => {
  return request({
    url: '/hr/sys/version-month',
    method: 'get'
  });
};
export const addDepInsertApi = (data: any, version: any) => {
  return request({
    url: `/hr/dept${version ? '-version' : ''}/insert`,
    method: 'post',
    data
  });
};
