import request from '@/utils/request';
import { AxiosPromise } from 'axios';

export const rolePage = (query: any) => {
  return request({
    url: `/lib/role/page`,
    method: 'get',
    params: query
  });
};

export const addrolePage = (data: any) => {
  return request({
    url: `/lib/role`,
    method: 'post',
    data: data
  });
};

export const addrolePagePut = (data: any) => {
  return request({
    url: `/lib/role`,
    method: 'put',
    data: data
  });
};
export const roleuserList = (query: any) => {
  return request({
    url: `/lib/roleuser/list`,
    method: 'get',
    params: query
  });
};
export const deleteRoleuser = (id: any) => {
  return request({
    url: `/lib/roleuser/${id}`,
    method: 'delete'
  });
};
export const deleteRole = (id: any) => {
  return request({
    url: `/lib/role/${id}`,
    method: 'delete'
  });
};

export const libRoleuser = (data: any) => {
  return request({
    url: `/lib/roleuser`,
    method: 'post',
    data: data
  });
};

export const roleuserUser = (query: any) => {
  return request({
    url: `/lib/roleuser/user`,
    method: 'get',
    params: query
  });
};
