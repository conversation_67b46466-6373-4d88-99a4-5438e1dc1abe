<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { getJurisdictionDrugList } from '../api'
import type { DtpJurDrugBo, DtpJurVo } from '../api'
import { district } from '@/api/dictlist/index'
import { usedDstrictManagementStore } from '@/store/modules/districtManagement'
import { getProductList } from '@/api/mdm/dept_product'

interface ProductOption {
  level3Code: string
  level3Name: string
}

interface SpecOption {
  specCode: string
  specName: string
}

const dstrictManagement = usedDstrictManagementStore()

const tableData = ref<DtpJurVo[]>([])
const loading = ref(false)
const total = ref(0)
const searchFormRef = ref<FormInstance>()
const distList = ref(dstrictManagement.districtList)

const state = reactive({
  option: {
    productCode: [] as ProductOption[],
    specCode: [] as SpecOption[]
  }
})

const searchForm = reactive<DtpJurDrugBo>({
  pageNum: 1,
  pageSize: 10,
  jurCode: '',
  drugName: '',
  productName: '',
  specName: '',
  province: '',
  city: '',
  district: []
})

const columns = [
  { prop: 'jurCode', label: '辖区编码', width: '120' },
  { prop: 'userName', label: '辖区负责人', width: '120' },
  { prop: 'userCode', label: '工号', width: '120' },
  { prop: 'deptName', label: '部门' },
  { prop: 'postName', label: '岗位' },
  { prop: 'postCode', label: '岗位编码', width: '120' },
  { prop: 'drugName', label: '辖区DTP药店名称', width: '150' },
  { prop: 'drugCode', label: '辖区DTP药店编码', width: '150' },
  { prop: 'province', label: '省份', width: '100' },
  { prop: 'city', label: '城市', width: '100' },
  { prop: 'district', label: '区/县', width: '100' },
  { prop: 'productName', label: '辖区DTP药店产品', width: '150' },
  { prop: 'specCode', label: '辖区DTP药店品规编码' },
  { prop: 'specName', label: '辖区DTP药店品规名称' }
]

const fetchData = async () => {
  loading.value = true
  try {
    const response = await getJurisdictionDrugList({
      ...searchForm,
      district: searchForm.district?.join(',')
    })
    if (response.code === 200) {
      tableData.value = response.data.rows
      total.value = response.data.total
    } else {
      ElMessage.error(response.msg || '获取数据失败')
    }
  } catch (error) {
    console.error('Error fetching data:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = async () => {
  searchForm.pageNum = 1
  await fetchData()
}

const handleReset = async () => {
  if (!searchFormRef.value) return
  await searchFormRef.value.resetFields()
  searchForm.district = [];
  searchForm.pageNum = 1
  await fetchData()
}

const handlePageChange = (page: number) => {
  searchForm.pageNum = page
  fetchData()
}

const handleSizeChange = (size: number) => {
  searchForm.pageSize = size
  searchForm.pageNum = 1
  fetchData()
}

const propsPC = {
  label: 'value',
  value: 'code',
  multiple: true,
  emitPath: false,
}

const getDistrict = async () => {
  const res = await district()
  dstrictManagement.changeDistrictList(res.data)
}

const remoteProductCode = async (level3Name: string) => {
  if (!level3Name) return null
  const res = await getProductList({ level3Name, type: 1 })
  state.option.specCode = []
  state.option.productCode = res.data?.rows.map((el: any) => ({
    level3Code: el.level3Code,
    level3Name: el.level3Name
  }))
}

const remoteSpecCode = async (specName: string, search = false) => {
  if (!specName && !search) return null
  const res = await getProductList({
    level3Name: searchForm.productName,
    specName
  })
  state.option.specCode = res.data.rows
}

const setCode = (item: any) => {
  searchForm.specName = ''
  remoteSpecCode('', true)
}

const initSandP = async () => {
  const res = await getProductList({ type: 1 })
  state.option.productCode = res.data.rows?.map((el: any) => ({
    level3Code: el.level3Code,
    level3Name: el.level3Name
  }))
  initSpecList()
}

const initSpecList = async () => {
  const res1 = await getProductList({})
  state.option.specCode = res1.data.rows
}

onMounted(() => {
  fetchData()
  getDistrict()
  initSandP()
})
</script>

<template>
  <div class="drug-table-container">
    <el-form ref="searchFormRef" :model="searchForm" class="search-form">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="辖区编码" prop="jurCode">
            <el-input v-model="searchForm.jurCode" placeholder="请输入辖区编码" clearable @keyup.enter="handleSearch" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="辖区负责人" prop="userName">
            <el-input v-model="searchForm.userName" placeholder="请输入辖区负责人" clearable @keyup.enter="handleSearch" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="药店名称" prop="drugName">
            <el-input v-model="searchForm.drugName" placeholder="请输入药店名称" clearable @keyup.enter="handleSearch" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="产品名称" prop="productName">
            <el-select
              @clear="initSandP"
              :remote-method="remoteProductCode"
              filterable
              remote
              remote-show-suffix
              v-model="searchForm.productName"
              placeholder="请选择"
              clearable
              @change="handleSearch"
              style="width: 100%;"
            >
              <el-option
                @click="setCode(item)"
                v-for="item in state.option.productCode"
                :key="item.level3Name"
                :label="item.level3Name"
                :value="item.level3Name"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="品规名称" prop="specName">
            <el-select
              :remote-method="remoteSpecCode"
              filterable
              remote
              remote-show-suffix
              v-model="searchForm.specName"
              placeholder="请选择"
              clearable
              @change="handleSearch"
              style="width: 100%;"
            >
              <el-option v-for="item in state.option.specCode" :key="item.specCode" :label="item.specName" :value="item.specName" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="省市" prop="province">
            <el-cascader
              @change="fetchData"
              v-model="searchForm.district"
              clearable
              :options="distList"
              :props="propsPC"
              style="width: 100%;"
              :getCheckedNodes="true"
              collapse-tags
              max-collapse-tags="6"
              collapse-tags-tooltip
            />
          </el-form-item>
        </el-col>
        <el-col :span="12" class="search-buttons">
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-table v-loading="loading" :data="tableData" style="width: 100%" height="calc(100vh - 280px)">
      <el-table-column v-for="col in columns" :key="col.prop" :prop="col.prop" :label="col.label" :width="col.width" show-overflow-tooltip />
    </el-table>

    <div class="pagination-container">
      <el-pagination
        v-model:current-page="searchForm.pageNum"
        v-model:page-size="searchForm.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>

<style scoped>
.drug-table-container {
  padding: 20px;
}

.search-form {
  margin-bottom: 20px;
}

.search-buttons {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
}

:deep(.el-form-item) {
  margin-bottom: 18px;
}

:deep(.el-input) {
  width: 100%;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
