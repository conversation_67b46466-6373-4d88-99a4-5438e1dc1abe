import request from '@/utils/request';
import { AxiosPromise } from 'axios';

export const insDsProposeListApi = (query: any) => {
  return request({
    url: `/mdm/ins-ds-propose/list`,
    method: 'get',
    params: query
  });
};
export const dsDourceApi = () => {
  return request({
    url: `/plt/dict/data/list?dictType=ds_source`,
    method: 'get'
  });
};
export const deleteInsDsProposeApi = (ids: string) => {
  return request({
    url: `/mdm/ins-ds-propose/${ids} `,
    method: 'delete'
  });
};
