/**
 * HCO API使用示例
 *
 * 本文件展示了如何使用HCO相关的API接口
 * 可以作为开发参考，实际使用时请根据具体需求调整
 *
 * 注意：所有HCO相关的API都在 @/api/mdm/hco 文件中
 */

import {
  customizeHeader,
  queryByIns,
  insTagQuery,
  hcoPageQuery,
  insLicenseQuery,
  headerQuery,
  insDeptQuery,
  queryDetail,
  type CustomizeHeaderVo,
  type HcoPageQuery,
  type QueryCondition,
  type HcpPageQuery,
  type MdmInstitutionVo,
  type MdmCustomerVo
} from '@/front-pages/src/pages/hco/hco';

// ==================== 使用示例 ====================

/**
 * 示例1: 查询HCO分页列表（支持自定义条件组）
 */
export async function exampleHcoPageQuery() {
  try {
    const params: HcoPageQuery = {
      conditions: [
        {
          field: 'insName',
          operator: 'contains',
          value: '北京医院'
        },
        {
          field: 'insType',
          operator: 'eq',
          value: '医院'
        },
        {
          field: 'provinceName',
          operator: 'eq',
          value: '北京市'
        }
      ],
      logic: 'and',
      pageSize: 10,
      pageNum: 1
    };

    const response = await hcoPageQuery(params);

    if (response.data.code === 0) {
      const institutions: MdmInstitutionVo[] = response.data.rows;
      console.log('查询成功，共找到', response.data.total, '条记录');
      console.log('机构列表:', institutions);
      return institutions;
    } else {
      console.error('查询失败:', response.data.msg);
      return [];
    }
  } catch (error) {
    console.error('API调用失败:', error);
    return [];
  }
}

/**
 * 示例2: 查询HCP分页列表
 */
export async function exampleHcpQuery() {
  try {
    const params: HcpPageQuery = {
      insCode: 'INS001',
      pageSize: 20,
      pageNum: 1
    };

    const response = await queryByIns(params);

    if (response.data.code === 0) {
      const customers: MdmCustomerVo[] = response.data.rows;
      console.log('HCP查询成功，共找到', response.data.total, '条记录');
      return customers;
    } else {
      console.error('HCP查询失败:', response.data.msg);
      return [];
    }
  } catch (error) {
    console.error('HCP API调用失败:', error);
    return [];
  }
}

/**
 * 示例3: 查询机构标签
 */
export async function exampleInsTagQuery() {
  try {
    const insCode = 'INS001';
    const response = await insTagQuery(insCode);

    console.log('机构标签:', response.data);
    return response.data;
  } catch (error) {
    console.error('标签查询失败:', error);
    return [];
  }
}

/**
 * 示例4: 查询机构证照
 */
export async function exampleInsLicenseQuery() {
  try {
    const insCode = 'INS001';
    const response = await insLicenseQuery(insCode);

    console.log('机构证照:', response.data);
    return response.data;
  } catch (error) {
    console.error('证照查询失败:', error);
    return [];
  }
}

/**
 * 示例5: 查询标准科室
 */
export async function exampleInsDeptQuery() {
  try {
    const insCode = 'INS001';
    const response = await insDeptQuery(insCode);

    console.log('标准科室:', response.data);
    return response.data;
  } catch (error) {
    console.error('科室查询失败:', error);
    return [];
  }
}

/**
 * 示例6: 查询自定义表头
 */
export async function exampleHeaderQuery() {
  try {
    const response = await headerQuery();

    if (response.data.code === 0) {
      const headers: CustomizeHeaderVo[] = response.data.data;
      console.log('自定义表头:', headers);
      return headers;
    } else {
      console.error('表头查询失败:', response.data.msg);
      return [];
    }
  } catch (error) {
    console.error('表头查询API调用失败:', error);
    return [];
  }
}

/**
 * 示例7: 设置自定义表头
 */
export async function exampleCustomizeHeader() {
  try {
    const headerData: CustomizeHeaderVo[] = [
      {
        header: '机构名称',
        index: '1',
        isFrozen: 'true'
      },
      {
        header: '机构类型',
        index: '2',
        isFrozen: 'false'
      },
      {
        header: '地址',
        index: '3',
        isFrozen: 'false'
      }
    ];

    const response = await customizeHeader(headerData);

    if (response.data.code === 0) {
      console.log('自定义表头设置成功');
      return true;
    } else {
      console.error('自定义表头设置失败:', response.data.msg);
      return false;
    }
  } catch (error) {
    console.error('自定义表头API调用失败:', error);
    return false;
  }
}

/**
 * 示例8: HCO详情查询
 */
export async function exampleHcoDetailQuery() {
  try {
    const insCode = 'INS001';
    const response = await queryDetail(insCode);

    if (response.data.code === 0) {
      const institution: MdmInstitutionVo = response.data.data;
      console.log('机构详情:', institution);
      return institution;
    } else {
      console.error('详情查询失败:', response.data.msg);
      return null;
    }
  } catch (error) {
    console.error('详情查询API调用失败:', error);
    return null;
  }
}

/**
 * 示例9: 综合查询示例
 */
export async function exampleComprehensiveQuery() {
  try {
    // 1. 先查询HCO列表
    const hcoList = await exampleHcoPageQuery();

    if (hcoList.length > 0) {
      const firstInstitution = hcoList[0];
      console.log('选择第一个机构进行详细查询:', firstInstitution.insName);

      // 2. 查询该机构的详情
      const detail = await queryDetail(firstInstitution.wbId);
      console.log('机构详情:', detail.data);

      // 3. 查询该机构的标签
      const tags = await insTagQuery(firstInstitution.wbId);
      console.log('机构标签:', tags.data);

      // 4. 查询该机构的证照
      const licenses = await insLicenseQuery(firstInstitution.wbId);
      console.log('机构证照:', licenses.data);

      // 5. 查询该机构的科室
      const depts = await insDeptQuery(firstInstitution.wbId);
      console.log('标准科室:', depts.data);

      // 6. 查询该机构的HCP
      const hcpParams: HcpPageQuery = {
        insCode: firstInstitution.wbId,
        pageSize: 5,
        pageNum: 1
      };
      const hcpList = await queryByIns(hcpParams);
      console.log('机构HCP:', hcpList.data.rows);

      return {
        institution: firstInstitution,
        detail: detail.data,
        tags: tags.data,
        licenses: licenses.data,
        depts: depts.data,
        hcpList: hcpList.data.rows
      };
    }
  } catch (error) {
    console.error('综合查询失败:', error);
    return null;
  }
}

// ==================== 错误处理示例 ====================

/**
 * 带错误处理的API调用示例
 */
export async function exampleWithErrorHandling() {
  try {
    const params: HcoPageQuery = {
      pageSize: 10,
      pageNum: 1
    };

    const response = await hcoPageQuery(params);

    // 检查HTTP状态
    if (response.status !== 200) {
      throw new Error(`HTTP错误: ${response.status}`);
    }

    // 检查业务状态码
    if (response.data.code !== 0) {
      throw new Error(`业务错误: ${response.data.msg}`);
    }

    return response.data.rows;
  } catch (error: any) {
    // 统一错误处理
    if (error.response) {
      // 服务器响应了错误状态码
      console.error('服务器错误:', error.response.status, error.response.data);
    } else if (error.request) {
      // 请求已发出但没有收到响应
      console.error('网络错误:', error.request);
    } else {
      // 其他错误
      console.error('未知错误:', error.message);
    }

    // 可以在这里添加用户友好的错误提示
    // ElMessage.error('查询失败，请稍后重试');

    return [];
  }
}
