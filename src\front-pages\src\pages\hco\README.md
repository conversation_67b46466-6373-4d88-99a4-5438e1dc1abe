# HCO 管理模块

本目录包含了 HCO（Healthcare Organization）管理相关的完整前端实现，包括 API 接口定义、页面组件和数据管理。

## 🎯 接口对接完成情况

✅ **已完成接口对接的功能**：

- HCO 分页列表查询
- HCP 分页列表查询
- 机构标签查询
- 机构证照查询
- 标准科室查询
- 自定义表头配置
- 详情页面数据展示
- **高级筛选功能** 🆕

```
src/front-pages/src/pages/hco/
├── README.md                     # 本文档
├── api.md                        # API规范文档（原始）
├── api-usage-example.ts          # API使用示例
├── hco.ts                        # HCO管理API接口
├── index.vue                     # HCO列表页面 ✅ 已对接 🆕 高级筛选
├── ADVANCED_FILTER_GUIDE.md     # 高级筛选功能使用指南 🆕
├── composables/
│   └── useHco.ts                 # HCO数据管理composable ✅ 新增
└── detail/
    ├── index.vue                 # HCO详情页面主框架
    ├── EffectiveTags.vue         # 标签组件 ✅ 已对接
    ├── StandardDepartmentTable.vue # 科室组件 ✅ 已对接
    ├── CustomerInfo.vue          # 客户信息组件 ✅ 已对接
    ├── LicenseInfo.vue           # 证照信息组件 ✅ 已对接
    ├── GdMap.vue                 # 地图组件
    ├── SitePhotos.vue            # 现场照片组件
    ├── BusinessRecord.vue        # 商务备案组件
    └── detailInformation.vue     # 详情信息组件
```

## API 接口说明

### 主要接口

1. **自定义表头** - `customizeHeader(data: CustomizeHeaderVo[])`

   - 用于设置 HCO 列表的自定义表头配置

2. **HCO 分页列表查询** - `hcoPageQuery(params: HcoPageQuery)`

   - 查询 HCO 机构的分页列表，支持多种筛选条件

3. **HCP 分页列表查询** - `queryByIns(params: HcpPageQuery)`

   - 根据机构编码查询该机构下的 HCP（Healthcare Professional）列表

4. **机构标签查询** - `insTagQuery(insCode: string)`

   - 查询指定机构的标签信息

5. **机构证照查询** - `insLicenseQuery(insCode: string)`

   - 查询指定机构的证照信息

6. **自定义查询表头** - `headerQuery()`

   - 获取当前用户的自定义表头配置

7. **标准科室查询** - `insDeptQuery(insCode: string)`
   - 查询指定机构的标准科室信息

## 🔧 技术实现

### 核心组件

1. **`hco.ts`** - API 接口定义

   - 包含所有 7 个 HCO 相关 API 接口
   - 完整的 TypeScript 类型定义
   - 统一的错误处理

2. **`composables/useHco.ts`** - 数据管理层

   - `useHcoList()` - HCO 列表管理
   - `useHcpList()` - HCP 列表管理
   - `useHcoDetail()` - 机构详情管理
   - `useCustomHeader()` - 自定义表头管理

3. **页面组件** - 用户界面层
   - 列表页面：支持搜索、筛选、分页
   - 详情页面：多标签页展示机构完整信息

### API 接口对接状态

| 接口名称          | 方法     | 路径                                 | 功能描述                             | 对接状态      |
| ----------------- | -------- | ------------------------------------ | ------------------------------------ | ------------- |
| `customizeHeader` | POST     | `/data/institution/customize-header` | 自定义表头配置                       | ✅            |
| `hcoPageQuery`    | **POST** | `/data/institution/page`             | HCO 分页列表查询（支持自定义条件组） | ✅ **已更新** |
| `queryByIns`      | POST     | `/data/customer/by-ins`              | HCP 分页列表查询                     | ✅            |
| `insTagQuery`     | GET      | `/data/institution/tag`              | 机构标签查询                         | ✅            |
| `insLicenseQuery` | GET      | `/data/institution/license`          | 机构证照查询                         | ✅            |
| `headerQuery`     | GET      | `/data/institution/header-query`     | 查询表头配置                         | ✅            |
| `insDeptQuery`    | GET      | `/data/institution/dept`             | 标准科室查询                         | ✅            |
| `queryDetail`     | GET      | `/data/institution/detail`           | HCO 详情查询                         | ✅ **新增**   |

### 数据类型

#### 主要 VO 类型

- `CustomizeHeaderVo` - 自定义表头配置
- `InstitutionTagVo` - 机构标签
- `MdmCustomerVo` - MDM 客户信息
- `MdmInstitutionVo` - MDM 机构信息
- `TableDataInfo<T>` - 分页数据结构
- `ResponseResult<T>` - 统一响应结构

#### 查询参数类型

- `HcoPageQuery` - HCO 分页查询参数
- `HcpPageQuery` - HCP 分页查询参数

## 使用方法

### 1. 导入 HCO API

```typescript
import {
  hcoPageQuery,
  queryByIns,
  insTagQuery,
  type HcoPageQuery,
  type MdmInstitutionVo
} from './hco';
```

**注意**: 所有 HCO 相关的 API 都在当前目录的 `hco.ts` 文件中。

### 2. 使用 Composables（推荐）

```typescript
import { useHcoList, useHcpList, useHcoDetail } from './composables/useHco';

// 在组件中使用
const hcoList = useHcoList();
const hcpList = useHcpList();
const hcoDetail = useHcoDetail();

// 获取HCO列表
await hcoList.fetchHcoList({ insName: '北京医院' });

// 获取HCP列表
await hcpList.fetchHcpList({ insCode: 'INS001' });

// 获取机构详情
await hcoDetail.fetchInstitutionDetail('INS001');
```

### 3. 页面组件使用

#### 列表页面 (`index.vue`)

- ✅ 已集成 `useHcoList` composable
- ✅ 支持机构名称搜索
- ✅ 支持分页查询
- ✅ 支持自定义表头
- ✅ 点击机构名称跳转详情页
- 🆕 **高级筛选功能**
  - 支持 16 个字段的多条件筛选
  - 快捷筛选按钮（三甲医院、地区等）
  - 筛选条件可视化标签
  - 支持筛选条件的添加、删除、清空

#### 详情页面 (`detail/index.vue`)

- ✅ 多标签页展示机构信息
- ✅ 标签信息展示 (`EffectiveTags.vue`)
- ✅ 标准科室展示 (`StandardDepartmentTable.vue`)
- ✅ 客户信息展示 (`CustomerInfo.vue`)
- ✅ 证照信息展示 (`LicenseInfo.vue`)

### 4. 直接使用 API 示例

```typescript
// 查询HCO列表 (注意：API已更新为POST方法)
const params: HcoPageQuery = {
  queryBo: {
    insName: '北京医院',
    insType: '医院'
  },
  pageSize: 10,
  pageNum: 1
};

const response = await hcoPageQuery(params);
if (response.data.code === 0) {
  const institutions = response.data.rows;
  console.log('查询成功:', institutions);
}
```

### 📢 重要 API 变化说明

**HCO 分页列表查询接口重大更新**：

- **接口名称**: "HCO 分页列表查询（支持自定义条件组）"
- **查询方式**: 使用 `QueryCondition` 数组进行条件组合
- **逻辑支持**: 支持 "and" 或 "or" 逻辑组合
- **新增接口**: HCO 详情查询接口

**新的参数结构**：

```typescript
interface QueryCondition {
  field?: string;     // 字段名
  operator?: string;  // 操作符: eq, neq, contains, not_contains, empty, not_empty
  value?: string;     // 右值
}

interface HcoPageQuery {
  conditions?: QueryCondition[];  // 条件数组
  logic?: string;                 // 条件逻辑 "and" 或 "or"
  pageSize?: number;              // 分页参数
  pageNum?: number;
}
```

**支持的操作符**：

- `eq`: 等于
- `neq`: 不等于
- `contains`: 包含
- `not_contains`: 不包含
- `empty`: 为空
- `not_empty`: 不为空

### 5. 错误处理

```typescript
try {
  const response = await hcoPageQuery(params);

  if (response.data.code !== 0) {
    throw new Error(response.data.msg);
  }

  return response.data.rows;
} catch (error) {
  console.error('查询失败:', error);
  // 处理错误，如显示用户友好的错误信息
}
```

## 详细示例

请参考 `api-usage-example.ts` 文件，其中包含了：

- 各个 API 接口的详细使用示例
- 错误处理最佳实践
- 综合查询示例
- TypeScript 类型使用示例

## API 规范

所有 API 接口都遵循以下规范：

1. **统一的响应格式**：

   ```typescript
   {
     code: number,    // 状态码，0表示成功
     msg: string,     // 消息内容
     data: T          // 数据对象
   }
   ```

2. **分页数据格式**：

   ```typescript
   {
     total: number,   // 总记录数
     rows: T[],       // 数据列表
     code: number,    // 状态码
     msg: string      // 消息内容
   }
   ```

3. **请求头要求**：
   - `Authorization`: 认证 token（可选）
   - `clientId`: 客户端 ID（可选）

## 注意事项

1. **类型安全**：所有接口都提供了完整的 TypeScript 类型定义，建议使用时充分利用类型检查。

2. **错误处理**：建议在调用 API 时添加适当的错误处理逻辑。

3. **分页查询**：分页参数 `pageNum` 和 `pageSize` 都是可选的，如果不传递会使用默认值。

4. **排序功能**：支持通过 `orderByColumn` 和 `isAsc` 参数进行排序。

5. **独立性**：HCO API 完全独立在 `hco.ts` 文件中，不影响其他现有 API 文件。

## 开发建议

1. 在 Vue 组件中使用时，建议将 API 调用封装在 composables 中：

```typescript
// composables/useHco.ts
import { ref } from 'vue';
import { hcoPageQuery, type HcoPageQuery, type MdmInstitutionVo } from '@/api/mdm/hco';

export function useHco() {
  const loading = ref(false);
  const institutions = ref<MdmInstitutionVo[]>([]);
  const total = ref(0);

  const fetchInstitutions = async (params: HcoPageQuery) => {
    loading.value = true;
    try {
      const response = await hcoPageQuery(params);
      if (response.data.code === 0) {
        institutions.value = response.data.rows;
        total.value = response.data.total;
      }
    } catch (error) {
      console.error('查询失败:', error);
    } finally {
      loading.value = false;
    }
  };

  return {
    loading,
    institutions,
    total,
    fetchInstitutions
  };
}
```

2. 使用响应式数据和计算属性来管理状态。

3. 考虑添加缓存机制来优化性能。

## 📋 接口对接总结

### ✅ 已完成的工作

1. **API 接口层**

   - 完成所有 7 个 HCO 相关 API 接口的定义和实现
   - 提供完整的 TypeScript 类型定义
   - 统一的错误处理和响应格式

2. **数据管理层**

   - 创建 `useHco.ts` composable，提供 4 个核心功能模块
   - 封装 API 调用逻辑，简化组件使用
   - 统一的 loading 状态和错误处理

3. **页面组件层**

   - 更新列表页面，集成真实 API 接口
   - 更新详情页面的 4 个子组件，对接相应 API
   - 保持原有 UI 交互，仅替换数据源

4. **类型安全**
   - 所有接口都有完整的 TypeScript 类型定义
   - 组件间数据传递类型安全
   - 编译时类型检查

### 🎯 功能特性

- **列表查询**: 支持机构名称搜索、分页、排序
- **详情展示**: 多标签页展示机构完整信息
- **标签管理**: 动态展示机构标签信息
- **科室管理**: 展示标准科室列表
- **客户管理**: HCP 列表查询和展示
- **证照管理**: 证照图片展示和预览
- **表头配置**: 自定义表头配置功能
- 🆕 **高级筛选**: 多字段、多条件的复杂筛选功能
  - 支持 16 个字段的筛选
  - 6 种操作符（包含、等于、为空等）
  - 快捷筛选预设
  - 筛选条件可视化管理

### 📝 使用说明

1. **开发环境**: 确保 API 服务正常运行
2. **导入方式**: 使用相对路径导入 API 和 composables
3. **错误处理**: 所有 API 调用都包含错误处理
4. **类型检查**: 充分利用 TypeScript 类型定义

## 更新日志

### v1.3.0 (2024-01-XX) - API 接口重大更新

- 🔄 **重大更新**: HCO 分页列表查询接口支持自定义条件组
- 🆕 **新增接口**: HCO 详情查询接口 `queryDetail`
- 🔄 **查询方式**: 使用 `QueryCondition` 数组进行条件组合
- ✅ 支持 "and" 或 "or" 逻辑组合
- ✅ 支持 6 种操作符：eq, neq, contains, not_contains, empty, not_empty
- ✅ 更新所有相关代码以适应新的 API 结构
- ✅ 更新类型定义和示例代码
- ✅ 保持高级筛选功能的完整性

### v1.2.0 (2024-01-XX) - 高级筛选功能

- 🆕 **新增功能**: 完整的高级筛选功能
- ✅ 支持 16 个字段的多条件筛选
- ✅ 提供 6 种操作符（包含、等于、为空等）
- ✅ 快捷筛选按钮（三甲医院、地区筛选等）
- ✅ 筛选条件可视化标签管理
- ✅ 筛选条件与基础搜索的智能合并
- 📚 新增高级筛选使用指南文档

### v1.1.0 (2024-01-XX) - API 接口更新

- 🔄 **重要更新**: HCO 分页列表查询接口从 GET 改为 POST
- 🔄 更新参数结构：查询条件现在使用`InstitutionQueryBo`
- ✅ 更新所有相关代码以适应新的 API 结构
- ✅ 更新类型定义和示例代码
- ✅ 保持向后兼容的使用方式

### v1.0.0 (2024-01-XX) - 初始版本

- ✅ 完成 HCO 模块接口对接工作
- ✅ 包含 7 个主要 API 接口的完整实现
- ✅ 提供完整的 TypeScript 类型定义
- ✅ 包含详细的使用示例和文档
- ✅ 完成列表页面和详情页面的接口对接
