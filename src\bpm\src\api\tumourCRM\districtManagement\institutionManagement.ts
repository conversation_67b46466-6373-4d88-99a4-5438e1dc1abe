import request from '@/utils/request';
import { AxiosPromise } from 'axios';
export const setDictList = (type: string): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=${type}`,
    method: 'get'
  });
};

export const editReplace = (id:any,query: any): AxiosPromise<any> => {
  return request({
    url: `/mdm/gs-institution/replace/${id}`,
    method: 'get',
    params: query
  });
};
export const institutionList = (query: any): AxiosPromise<any> => {
  return request({
    url: '/mdm/gs-institution/list',
    method: 'get',
    params: query
  });
};

export const deleteInstitution = (id: any): AxiosPromise<any> => {
  return request({
    url: `/mdm/institution/${id}`,
    method: 'delete'
  });
};

export const institutionImport = (data: any) => {
  return request({
    url: '/mdm/institution/import',
    method: 'post',
    data: data
  });
};

export const institutionExport = (data: any) => {
  return request({
    url: '/mdm/institution/export',
    method: 'post',
    data: data
  });
};

export const cusExport = (data: any) => {
  return request({
    url: '/mdm/customer/import',
    method: 'post',
    data: data
  });
};

export const institutionDetail = (id: any): AxiosPromise<any> => {
  return request({
    url: `/mdm/institution/${id}`,
    method: 'get'
  });
};

export const institutionUpdate = (data: any) => {
  return request({
    url: '/mdm/institution',
    method: 'put',
    data: data
  });
};
export const add = (data: any) => {
  return request({
    url: '/mdm/institution/add',
    method: 'post',
    data: data
  });
};
export const insDetail = (insCode: any, pageNum: any, pageSize: any): AxiosPromise<any> => {
  return request({
    url: `/mdm/institution/jur/${insCode}?pageNum=${pageNum}&pageSize=${pageSize}`,
    method: 'get'
  });
};

export const dictTypeList = (): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=ins_level`,
    method: 'get'
  });
};

export const insProfessionTypeList = (): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=ins_profession_type`,
    method: 'get'
  });
};

export const insTypeList = (): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=ins_type`,
    method: 'get'
  });
};

export const economicTypeList = (): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=economic_type`,
    method: 'get'
  });
};

export const getInsListRes = (): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=department_label`,
    method: 'get'
  });
};
export const deptNameList = (): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=dept_name`,
    method: 'get'
  });
};

export const customerList = (query: any): AxiosPromise<any> => {
  return request({
    url: `/mdm/customer/list`,
    method: 'get',
    params: query
  });
};

export const deleteCustomer = (ids: any): AxiosPromise<any> => {
  return request({
    url: `/mdm/customer/${ids}`,
    method: 'delete'
  });
};

export const customerDetail = (id: any): AxiosPromise<any> => {
  return request({
    url: `/mdm/customer/${id}`,
    method: 'get'
  });
};
export const createCustomer = (data): AxiosPromise<any> => {
  return request({
    url: `/mdm/customer/add`,
    method: 'post',
    data
  });
};
export const customerUpdate = (data): AxiosPromise<any> => {
  return request({
    url: `/mdm/customer`,
    method: 'put',
    data
  });
};
export const professionList = (): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=profession_tech_title`,
    method: 'get'
  });
};

export const administrationList = (): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=administration_lvl`,
    method: 'get'
  });
};

export const lectureLvlList = (): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=lecture_lvl`,
    method: 'get'
  });
};
export const district = (): AxiosPromise<any> => {
  return request({
    url: `/mdm/hcd/district`,
    method: 'get'
  });
};
export const jobList = (query: any): AxiosPromise<any> => {
  return request({
    url: `/mdm/customer/detail`,
    method: 'get',
    params: query
  });
};
export const addCustomerType = (data: any): AxiosPromise<any> => {
  return request({
    url: `/mdm/customer`,
    method: 'post',
    data
  });
};
export const customerTypeUpdate = (data: any): AxiosPromise<any> => {
  return request({
    url: `/mdm/customer/update`,
    method: 'put',
    data
  });
};

export const deleteCustomerType = (ids: any): AxiosPromise<any> => {
  return request({
    url: `/mdm/customer/${ids}`,
    method: 'delete'
  });
};

export const cusLevelList = (): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=cus_idea_level`,
    method: 'get'
  });
};
export const cusPotenLevel = (): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=cus_poten_level`,
    method: 'get'
  });
};
