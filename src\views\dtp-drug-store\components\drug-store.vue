<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import { useDtpDrugstoreList } from '@/api/dtp-drug-store/hooks'
import { deleteDrugstoreApplication, getDrugstoreProducts } from '@/api/dtp-drug-store'
import type { DtpJurDrugVo } from '@/api/dtp-drug-store/types'
import { useUserStore } from '@/store/modules/user'
import { useRouter } from 'vue-router'

// 获取用户信息
const userStore = useUserStore()
const user = userStore.info || {}

// 查询条件
const queryParams = ref({
  keyword: '',
  pageNum: 1,
  pageSize: 10
});

// 使用API钩子函数
const {
  loading,
  drugStoreList,
  total,
  fetchDrugStores,
  changePage,
  changePageSize
} = useDtpDrugstoreList();

// 初始化路由
const router = useRouter()

// 表格数据加载
onMounted(() => {
  fetchDrugStores(queryParams.value);
});

// 查询方法
const handleSearch = () => {
  queryParams.value.pageNum = 1;
  fetchDrugStores(queryParams.value);
};

// 重置方法
const handleReset = () => {
  queryParams.value = {
    keyword: '',
    pageNum: 1,
    pageSize: 10
  };
  fetchDrugStores(queryParams.value);
};

// 分页方法
const handleCurrentChange = (current: number) => {
  changePage(current);
};

const handleSizeChange = (size: number) => {
  changePageSize(size);
};

// 操作按钮
const handleEdit = (row: DtpJurDrugVo) => {
  console.log('编辑', row);
  // 后续可以实现跳转到编辑页面或弹窗编辑
};

// 删除药店处理方法
const handleDelete = (row: DtpJurDrugVo) => {
  ElMessageBox.confirm(
    `确定要删除药店"${row.drugName}"吗？`,
    '删除提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      const loading = ElLoading.service({
        lock: true,
        text: '提交删除申请中...',
        background: 'rgba(0, 0, 0, 0.7)'
      });

      try {
        // 获取药店产品列表
        const productRes = await getDrugstoreProducts({
          jurDsCode: row.jurDsCode
        });

        // 构建删除申请数据
        const submitData = {
          applyContent: {
            dsCode: row.drugCode,
            dsName: row.drugName,
            dsMdmCode: row.drugMdmCode,
            province: row.drugProvince,
            city: row.drugCity,
            district: row.drugDistrict || '',
            districtCode: '',
            provinceCode: '',
            cityCode: '',
            jurCode: row.jurCode,
            jurType: '',
            bu: 'DTP', // 设置默认BU为'DTP'
            address: '',
            applyType: ['dtpDsDeleted'],
            specList: productRes.data?.map(p => ({
              mdmCode: '',
              productCode: p.productCode,
              productName: p.productName,
              specCode: p.specCode,
              specName: p.specName
            })) || []
          },
          applyType: 'dtpDsDeleted',
          dsCode: row.drugCode,
          dsName: row.drugName,
          jurCode: row.jurCode,
          bu: 'DTP', // 设置默认BU为'DTP'
          dsMdmCode: row.drugMdmCode, // 添加dsMdmCode
          // 用户信息
          applicant: user.nickName,
          applicantCode: user.userName,
          postCode: user.posts?.postCode,
          postName: user.posts?.postName,
          deptCode: user.dept?.deptCode,
          deptName: user.dept?.deptName,
          ancestors: user.dept?.ancestors,
          tenantId: import.meta.env.VITE_APP_TENANT_ID,
          enableWorkflow: true,
          appCode: import.meta.env.VITE_APP_CODE,
          postIdList: user.postIdList?.map((item: any) => item.postId)
        };

        const res = await deleteDrugstoreApplication(submitData);
        if (res.code === 200 && !res.msg) {
          ElMessage.success('删除申请提交成功');
          // 刷新列表
          fetchDrugStores(queryParams.value);
        } else {
          ElMessage.error(res.msg || '删除申请提交失败');
        }
      } catch (error) {
        console.error('删除申请提交失败', error);
        ElMessage.error('删除申请提交失败');
      } finally {
        loading.close();
      }
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消删除',
      });
    });
};

// 添加查看详情的方法
const handleDetail = (row: DtpJurDrugVo) => {
  router.push({
    path: '/dtp_drug_store/detail',
    query: {
      id: row.jurDsCode
    }
  });
};

// 添加查看操作记录的方法
const handleViewLogs = () => {
  router.push({
    path: '/dtp_drug_store/log',
  });
};
</script>

<template>
  <!-- 搜索区域 -->
  <el-form :model="queryParams" inline class="mb-4">
    <el-form-item label="药店名称/编码">
      <el-input v-model="queryParams.keyword" placeholder="请输入药店名称或编码" clearable @keyup.enter="handleSearch" />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" :loading="loading" @click="handleSearch">搜索</el-button>
      <el-button @click="handleReset">重置</el-button>
    </el-form-item>
  </el-form>

  <!-- 右上角操作按钮区域 -->
  <div class="flex justify-end mb-4">
    <el-button type="info" @click="handleViewLogs">查看操作记录</el-button>
  </div>

  <!-- 数据表格 -->
  <el-table v-loading="loading" :data="drugStoreList" style="width: 100%" border>
    <el-table-column prop="jurDsCode" label="关联编码" min-width="120" />
    <el-table-column prop="drugMdmCode" label="药店编码" min-width="120" />
    <el-table-column prop="drugName" label="药店名称" min-width="180" />
    <el-table-column prop="drugMdmCode" label="MDM编码" min-width="120" />
    <el-table-column prop="drugProvince" label="省份" width="100" />
    <el-table-column prop="drugCity" label="城市" width="100" />
    <el-table-column prop="drugDistrict" label="区县" width="100" />
    <el-table-column prop="lastTime" label="最新更新时间" min-width="180">
      <template #default="{ row }">
        {{ row.lastTime ? new Date(row.lastTime).toLocaleString() : '--' }}
      </template>
    </el-table-column>
    <el-table-column fixed="right" label="操作" width="220">
      <template #default="{ row }">
        <el-button type="primary" link @click="handleDetail(row)">查看详情</el-button>
        <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
      </template>
    </el-table-column>
  </el-table>

  <!-- 分页 -->
  <div class="flex justify-end mt-4">
    <el-pagination
      v-model:current-page="queryParams.pageNum"
      v-model:page-size="queryParams.pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<style lang="scss" scoped>
.el-form {
  margin-bottom: 16px;
}
</style>
