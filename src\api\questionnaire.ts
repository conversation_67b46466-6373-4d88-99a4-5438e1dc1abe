import request from '@/utils/request';
import type { Questionnaire, QuestionnaireQueryParams } from '@/types/questionnaire';

export function getQuestionnaireList(params: QuestionnaireQueryParams) {
  return request({
    url: '/questionnaire/list',
    method: 'get',
    params
  });
}

export function copyQuestionnaire(id: number) {
  return request({
    url: `/questionnaire/${id}/copy`,
    method: 'post'
  });
}

export function closeQuestionnaire(id: number) {
  return request({
    url: `/questionnaire/${id}/close`,
    method: 'put'
  });
}

export function deleteQuestionnaire(id: number) {
  return request({
    url: `/questionnaire/${id}`,
    method: 'delete'
  });
}

export function getProducts() {
  return request({
    url: '/products',
    method: 'get'
  });
}
