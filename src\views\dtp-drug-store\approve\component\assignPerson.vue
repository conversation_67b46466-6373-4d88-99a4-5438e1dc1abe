<script setup>
import { ref, reactive, defineEmits, watch } from 'vue'
import { Search, Plus, Delete } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import request from '@/utils/request'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '指派DTP专员'
  },
  loading: {
    type: Boolean,
    default: false
  },
  // 已选择的人员列表
  selectedPersons: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:visible', 'confirm', 'cancel'])

// 弹窗显示状态
const dialogVisible = ref(props.visible)

// 搜索条件
const searchForm = reactive({
  keyword: '',
  deptId: ''
})

// 搜索加载状态
const searchLoading = ref(false)

// 分页信息
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 部门选项
const departmentOptions = ref([
  { value: 'dept1', label: '市场部' },
  { value: 'dept2', label: '销售部' },
  { value: 'dept3', label: '产品部' }
])

// 人员列表
const personList = ref([])

// 选中的人员列表
const selectedList = ref(props.selectedPersons || [])

// 监听visible变化
watch(() => props.visible, (val) => {
  dialogVisible.value = val
  if (val) {
    // 当弹窗打开时，加载用户列表
    fetchUserList()
  }
})

// 更新弹窗状态
const updateVisible = (val) => {
  dialogVisible.value = val
  emit('update:visible', val)
}

// 获取用户列表
const fetchUserList = async () => {
  try {
    searchLoading.value = true

    const params = {
      pageNum: pagination.currentPage,
      pageSize: pagination.pageSize,
      userName: searchForm.keyword || undefined,
      deptId: searchForm.deptId || undefined
    }

    const res = await request({
      url: '/plt/user/list',
      method: 'get',
      params
    })

    if (res && res.code === 200) {
      personList.value = res.rows?.map(user => ({
        id: user.userId,
        code: user.userCode || user.userName,
        name: user.nickName || user.userName,
        deptName: user.dept?.deptName || '未知部门'
      }))
      pagination.total = res.total
    } else {
      personList.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
    personList.value = []
    pagination.total = 0
  } finally {
    searchLoading.value = false
  }
}

// 搜索方法
const handleSearch = () => {
  pagination.currentPage = 1
  fetchUserList()
}

// 重置搜索
const resetSearch = () => {
  searchForm.keyword = ''
  searchForm.deptId = ''
  handleSearch()
}

// 处理分页变化
const handleCurrentChange = (page) => {
  pagination.currentPage = page
  fetchUserList()
}

// 处理每页显示条数变化
const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  fetchUserList()
}

// 选择人员
const handleSelect = (row) => {
  // 检查是否已选择
  const isSelected = selectedList.value.some(item => item.id === row.id)
  if (isSelected) {
    ElMessage.warning(`${row.name}已经选择`)
    return
  }

  // 添加到已选列表
  selectedList.value.push({
    id: row.id,
    code: row.code,
    name: row.name,
    deptName: row.deptName
  })
}

// 移除已选人员
const handleRemove = (index) => {
  selectedList.value.splice(index, 1)
}

// 清空已选
const clearSelected = () => {
  selectedList.value = []
}

// 确认选择
const handleConfirm = () => {
  if (selectedList.value.length === 0) {
    ElMessage.warning('请至少选择一名DTP专员')
    return
  }

  emit('confirm', selectedList.value)
  updateVisible(false)
}

// 取消选择
const handleCancel = () => {
  emit('cancel')
  updateVisible(false)
}
</script>

<template>
  <el-dialog v-model="dialogVisible" :title="props.title" width="60%" :close-on-click-modal="false" @close="updateVisible(false)">
    <div class="assign-person-container">
      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="工号">
            <el-input v-model="searchForm.keyword" placeholder="工号" clearable @keyup.enter="handleSearch" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" :icon="Search" @click="handleSearch" :loading="searchLoading">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 内容区 -->
      <div class="content-area">
        <!-- 左侧人员列表 -->
        <div class="person-list-panel w-[50%]">
          <div class="panel-header">
            <span class="panel-title">人员列表</span>
          </div>
          <div class="panel-body">
            <el-table v-loading="searchLoading" :data="personList" height="350" style="width: 100%" border>
              <el-table-column type="index" width="50" label="序号" />
              <el-table-column prop="code" label="工号" width="100" />
              <el-table-column prop="name" label="姓名" width="100" />
              <el-table-column prop="deptName" label="所属部门" />
              <el-table-column label="操作" width="80" fixed="right">
                <template #default="{ row }">
                  <el-button type="primary" link :icon="Plus" @click="handleSelect(row)"> 选择 </el-button>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-container">
              <el-pagination
                v-model:current-page="pagination.currentPage"
                v-model:page-size="pagination.pageSize"
                :page-sizes="[10, 20, 50]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="pagination.total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </div>
        </div>

        <!-- 右侧已选人员 -->
        <div class="selected-panel">
          <div class="panel-header">
            <span class="panel-title">已选人员</span>
            <el-button type="primary" link @click="clearSelected">清空</el-button>
          </div>
          <div class="panel-body">
            <el-empty v-if="selectedList.length === 0" description="暂无已选人员" />
            <el-table v-else :data="selectedList" height="350" style="width: 100%" border>
              <el-table-column type="index" width="50" label="序号" />
              <el-table-column prop="code" label="工号" width="100" />
              <el-table-column prop="name" label="姓名" width="100" />
              <el-table-column prop="deptName" label="所属部门" />
              <el-table-column label="操作" width="80" fixed="right">
                <template #default="{ $index }">
                  <el-button type="danger" link :icon="Delete" @click="handleRemove($index)"> 移除 </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部按钮区 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" :loading="props.loading" @click="handleConfirm"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.assign-person-container {
  .search-area {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #ebeef5;
  }

  .content-area {
    display: flex;
    gap: 16px;
    height: 400px;

    .person-list-panel,
    .selected-panel {
      flex: 1;
      display: flex;
      flex-direction: column;
      border: 1px solid #ebeef5;
      border-radius: 4px;

      .panel-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        background-color: #f5f7fa;
        border-bottom: 1px solid #ebeef5;

        .panel-title {
          font-weight: 600;
          color: #303133;
        }
      }

      .panel-body {
        flex: 1;
        overflow: auto;
        display: flex;
        flex-direction: column;

        .el-table {
          flex: 1;
        }

        .pagination-container {
          padding: 10px;
          text-align: right;
          background-color: #fff;
          border-top: 1px solid #ebeef5;
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
