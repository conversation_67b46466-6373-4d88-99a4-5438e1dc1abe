import request from '@/utils/request';

export const taskList = (query: any) => {
  return request({
    url: `/bdp/task`,
    method: 'get',
    params: query
  });
};

export const addTask = (data: any) => {
  return request({
    url: `/bdp/task/add`,
    method: 'put',
    data
  });
};

export const menberList = (projectId: any) => {
  return request({
    url: `/bdp/menber/task/${projectId}`,
    method: 'get'
  });
};

export const deleteTask = (id: any) => {
  return request({
    url: `/bdp/task/${id}`,
    method: 'delete'
  });
};

export const taskChildDetail = (id: any) => {
  return request({
    url: `/bdp/task/child/${id}`,
    method: 'get'
  });
};

export const updateTask = (data: any) => {
  return request({
    url: `/bdp/task`,
    method: 'put',
    data
  });
};

export const taskRisk = (query: any) => {
  return request({
    url: `/bdp/risk`,
    method: 'get',
    params: query
  });
};
export const taskLog = (query: any) => {
  return request({
    url: `/bdp/log`,
    method: 'get',
    params: query
  });
};
export const taskFile = (query: any) => {
  return request({
    url: `/bdp/file`,
    method: 'get',
    params: query
  });
};
export const taskFileUpload = (data: any) => {
  return request({
    url: `/bdp/file/upload`,
    method: 'post',
    data
  });
};

export const updatePreApi = (data: any) => {
  return request({
    url: `/bdp/task/pre`,
    method: 'put',
    data
  });
};

export const taskDownload = (id: any) => {
  return request({
    url: `/bdp/log/download/${id}`,
    method: 'get'
  });
};

export const deleteTaskFile = (id: any) => {
  return request({
    url: `/bdp/file/${id}`,
    method: 'delete'
  });
};

export const fileBind = (data: any) => {
  return request({
    url: `/bdp/file/bind`,
    method: 'post',
    data: data
  });
};

export const updateRiskApi = (data: any) => {
  return request({
    url: `/bdp/task/risk`,
    method: 'post',
    data: data
  });
};
// 获取未完成的子任务
export const undoneTaskApi = (id: string) => {
  return request({
    url: `/bdp/task/undone/${id}`,
    method: 'post'
  });
};
// 批量完成的子任务
export const finishAllApi = (id: string) => {
  return request({
    url: `/bdp/task/finish/${id}`,
    method: 'get'
  });
};
// 获取有风险的子任务
export const riskListApi = (id: string) => {
  return request({
    url: `/bdp/task/risk/${id}`,
    method: 'get'
  });
};
// 关键任务判断
export const isKeyTask = (id: string) => {
  return request({
    url: `/bdp/task/key/${id}`,
    method: 'get'
  });
};
