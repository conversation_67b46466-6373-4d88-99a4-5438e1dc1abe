<template>
  <div>
    <el-button
      plain
      class="!p-2"
      :class="{ 'filter-active': activeFiltersCount > 0 }"
      @click="togglePopover"
    >
      <el-icon :class="{ 'text-primary': activeFiltersCount > 0 }">
        <Filter />
      </el-icon>
      <span v-if="activeFiltersCount > 0" class="ml-1 text-primary font-medium">{{ activeFiltersCount }}</span>
    </el-button>
    <teleport to="body">
      <div v-if="isPopoverOpen" class="fixed inset-0 bg-transparent z-40" @click="closePopover"></div>
      <div
        v-if="isPopoverOpen"
        class="advanced-filter-popover fixed z-50 bg-white rounded-lg shadow-lg"
        :style="{
          width: '520px',
          top: popoverPosition.top + 'px',
          left: popoverPosition.left - 330 + 'px',
        }"
        @click.stop
      >
        <div class="p-4 w-full box-border" :style="{minWidth: '480px', maxWidth: '100%', maxHeight: 'calc(50vh)', overflowY: 'auto'}">
          <el-form :model="formModel" :rules="rules" ref="formRef" label-width="0">
            <div class="flex flex-row relative">
              <!-- 左侧：且关系线 -->
              <div
                v-if="formModel.filters.length > 1"
                class="flex flex-col items-center justify-center relative"
                :style="{width: '32px', height: lineHeight + 'px'}"
              >
                <!-- <div class="w-full h-px bg-gray-200 absolute left-0 top-0"></div> -->
                <div class="w-px bg-gray-200 absolute left-1/2" :style="{top: '0', height: lineHeight - 15 + 'px'}"></div>
                <span class="text-xs text-gray-500 absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 bg-white px-1">且</span>
                <!-- <div class="w-full h-px bg-gray-200 absolute left-0 bottom-0"></div> -->
              </div>
              <!-- 右侧：条件列表 -->
              <div class="flex-1" ref="rightPanelRef">
                <div v-for="(item, idx) in formModel.filters" :key="item.id" class="relative">
                  <el-row :gutter="8" align="middle">
                    <el-col :span="8">
                      <el-form-item
                        :prop="`filters.${idx}.left`"
                        required
                        :rules="[{ required: true, message: '必填', trigger: ['change', 'blur'] }]"
                      >
                        <el-select
                          v-model="formModel.filters[idx].left"
                          placeholder="左值"
                          filterable
                          clearable
                          size="small"
                          style="width: 100%"
                          @change="handleLeftValueChange(idx)"
                        >
                          <el-option v-for="f in fieldOptions" :key="f.value" :label="f.label" :value="f.value" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="6">
                      <el-form-item :prop="`filters.${idx}.op`" required :rules="[{ required: true, message: '必填', trigger: ['change', 'blur'] }]">
                        <el-select
                          v-model="formModel.filters[idx].op"
                          placeholder="操作符"
                          size="small"
                          :disabled="!formModel.filters[idx].left"
                          style="width: 100%"
                          @change="handleOperatorChange(idx)"
                        >
                          <el-option v-for="op in operatorOptions" :key="op.value" :label="op.label" :value="op.value" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item
                        :prop="`filters.${idx}.right`"
                        :required="!isOperatorWithoutRightValue(formModel.filters[idx].op)"
                        :rules="getRightValueRules(formModel.filters[idx].op)"
                      >
                        <el-select
                          v-if="isBooleanField(formModel.filters[idx].left)"
                          v-model="formModel.filters[idx].right"
                          placeholder="右值"
                          size="small"
                          :disabled="!formModel.filters[idx].left || isOperatorWithoutRightValue(formModel.filters[idx].op)"
                          style="width: 100%"
                        >
                          <el-option label="是" :value="true" />
                          <el-option label="/" :value="false" />
                        </el-select>
                        <el-select
                          v-else-if="isBuField(formModel.filters[idx].left)"
                          v-model="formModel.filters[idx].right"
                          placeholder="请选择BU归属"
                          size="small"
                          :disabled="!formModel.filters[idx].left || isOperatorWithoutRightValue(formModel.filters[idx].op)"
                          style="width: 100%"
                          filterable
                          clearable
                          multiple
                        >
                          <el-option v-for="option in buOptions" :key="option" :label="option" :value="option" />
                        </el-select>
                        <el-input
                          v-else
                          v-model="formModel.filters[idx].right"
                          :placeholder="isOperatorWithoutRightValue(formModel.filters[idx].op) ? '无需填写' : '右值'"
                          size="small"
                          :disabled="!formModel.filters[idx].left || isOperatorWithoutRightValue(formModel.filters[idx].op)"
                          style="width: 100%"
                          v-show="formModel.filters[idx].right !== 'NULL' && formModel.filters[idx].right !== 'NOT_NULL'"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="2">
                      <el-icon class="text-gray-400 cursor-pointer" @click.stop="removeRow(idx)"><Close /></el-icon>
                    </el-col>
                  </el-row>
                </div>
              </div>
            </div>
          </el-form>
          <!-- 添加条件 -->
          <div class="flex items-center mb-2" v-if="formModel.filters.length <= 19">
            <el-button type="primary" link icon="plus" @click="addRow">添加条件</el-button>
          </div>
          <!-- 操作按钮 -->
          <div class="flex justify-between items-center mt-6 pt-2 border-t">
            <el-button type="info" link @click="clearAll">清空条件</el-button>
            <div>
              <el-button size="small" @click="onCancel">取消</el-button>
              <el-button size="small" type="primary" @click="onConfirm">确定</el-button>
            </div>
          </div>
        </div>
      </div>
    </teleport>
  </div>
</template>

<script setup>
import { ref, reactive, nextTick, onMounted, onBeforeUnmount, watch, computed } from 'vue'
import { Close, Filter } from '@element-plus/icons-vue'
import { getBuOptions } from '@/front-pages/src/pages/hco/hco'

const isPopoverOpen = ref(false)
const buttonRef = ref(null)
const popoverPosition = reactive({ top: 0, left: 0 })
const defaultFilter = () => ({ id: Date.now() + Math.random(), left: '', op: '', right: '' })
const formModel = reactive({ filters: [defaultFilter()] })
const lastConfirmed = ref([])
const buOptions = ref([])

const formRef = ref()

// Count active filters for badge
const activeFiltersCount = computed(() => {
  return lastConfirmed.value.filter(f => {
    if (!f.left || !f.op) return false

    // 对于"为空"和"不为空"操作符，不需要检查右值
    if (isOperatorWithoutRightValue(f.op)) {
      return true
    }

    // 对于数组类型的值（如多选），检查数组是否非空
    if (Array.isArray(f.right)) {
      return f.right.length > 0
    }

    // 对于其他类型的值
    return f.right !== undefined && f.right !== null && f.right !== ''
  }).length
})

const requiredValidator = (rule, value, callback) => {
  if (value === '' || value === undefined || value === null) {
    callback(new Error('必填'))
  } else if (Array.isArray(value) && value.length === 0) {
    // 处理空数组的情况
    callback(new Error('必填'))
  } else {
    callback()
  }
}

const rules = {
  filters: {
    type: 'array',
    required: true,
    defaultField: {
      type: 'object',
      fields: {
        left:  [{ validator: requiredValidator, trigger: ['change', 'blur'] }],
        op:    [{ validator: requiredValidator, trigger: ['change', 'blur'] }],
        right: [{ validator: requiredValidator, trigger: ['change', 'blur'] }]
      }
    }
  }
}

const fieldOptions = [
  { label: '机构ID', value: 'wbId' },
  { label: '机构名称', value: 'insName' },
  { label: '机构类别', value: 'insType' },
  { label: '省份', value: 'provinceName' },
  { label: '城市', value: 'cityName' },
  { label: '地址', value: 'address' },
  { label: '标准科室', value: 'insDept' },
  { label: '合作门诊', value: '合作门诊' },
  { label: '优质机构', value: '优质机构' },
  { label: 'BU归属', value: 'BU归属' },
]
const operatorOptions = [
  { label: '包含', value: 'contains' },
  { label: '不包含', value: 'not-contains' },
  { label: '等于', value: 'eq' },
  { label: '不等于', value: 'neq' },
  { label: '为空', value: 'empty' },
  { label: '不为空', value: 'not-empty' },
]

function togglePopover(event) {
  if (event && event.target) {
    const button = event.target.closest('button')
    if (button) {
      const rect = button.getBoundingClientRect()
      popoverPosition.top = rect.bottom + 10
      popoverPosition.left = rect.left
    }
  }
  isPopoverOpen.value = !isPopoverOpen.value
}

function closePopover(event) {
  if (event && (event.target.closest('.el-select-dropdown') || event.target.closest('.el-dropdown-menu'))) {
    return
  }
  isPopoverOpen.value = false
}

// 判断操作符是否不需要右值
function isOperatorWithoutRightValue(operator) {
  return operator === 'empty' || operator === 'not-empty'
}

// 获取右值的验证规则
function getRightValueRules(operator) {
  if (isOperatorWithoutRightValue(operator)) {
    // 为空或不为空操作符不需要验证
    return []
  }
  // 其他操作符需要必填验证
  return [{ required: true, message: '必填', trigger: ['change', 'blur'] }]
}

// 处理左值变化，清空右值
function handleLeftValueChange(index) {
  // 清空对应行的操作符和右值
  formModel.filters[index].op = ''
  formModel.filters[index].right = ''

  // 清除对应字段的验证错误
  nextTick(() => {
    if (formRef.value) {
      formRef.value.clearValidate([`filters.${index}.op`, `filters.${index}.right`])
    }
  })

  console.log(`左值变化: 第${index + 1}行的操作符和右值已清空`)
}

// 处理操作符变化
function handleOperatorChange(index) {
  const operator = formModel.filters[index].op

  if (isOperatorWithoutRightValue(operator)) {
    // 为空或不为空操作符，自动设置右值为特殊标识
    formModel.filters[index].right = operator === 'empty' ? 'NULL' : 'NOT_NULL'

    // 清除右值字段的验证错误
    nextTick(() => {
      if (formRef.value) {
        formRef.value.clearValidate([`filters.${index}.right`])
      }
    })

    console.log(`操作符变化: 第${index + 1}行选择了"${operator === 'empty' ? '为空' : '不为空'}"，右值已自动设置`)
  } else {
    // 其他操作符，清空右值让用户填写
    formModel.filters[index].right = ''
    console.log(`操作符变化: 第${index + 1}行选择了需要右值的操作符，右值已清空`)
  }
}

function addRow() {
  formModel.filters.push(defaultFilter())
  nextTick(() => {
    formRef.value && formRef.value.clearValidate()
  })
  // 重新计算高度
  updateLineHeight()
}

function removeRow(idx) {
  formModel.filters.splice(idx, 1)
  nextTick(() => {
    formRef.value && formRef.value.clearValidate()
  })
  // 重新计算高度
  updateLineHeight()
}

function clearAll() {
  formModel.filters.splice(0, formModel.filters.length, defaultFilter())
  lastConfirmed.value = []
  nextTick(() => {
    formRef.value && formRef.value.clearValidate()
  })
  // 重新计算高度
  updateLineHeight()
  // 移除自动调用查询，让父组件决定是否查询
  emit('filter', [])
}

// 恢复筛选条件
function restoreFilters(filters) {
  if (Array.isArray(filters) && filters.length > 0) {
    // 恢复筛选条件
    formModel.filters.splice(0, formModel.filters.length, ...filters.map(f => ({ ...f })))
    lastConfirmed.value = filters.map(f => ({ ...f }))
  } else {
    // 如果没有筛选条件，设置为默认状态
    formModel.filters.splice(0, formModel.filters.length, defaultFilter())
    lastConfirmed.value = []
  }

  nextTick(() => {
    formRef.value && formRef.value.clearValidate()
    // 重新计算高度
    updateLineHeight()
  })

  console.log('恢复筛选条件:', filters)
}

defineExpose({ clearAll, restoreFilters })

function onCancel() {
  // 恢复到上次确认的状态，删除未确认的条件
  if (lastConfirmed.value.length > 0) {
    // 如果有已确认的条件，恢复到已确认的状态
    formModel.filters.splice(0, formModel.filters.length, ...lastConfirmed.value.map(f => ({ ...f })))
  } else {
    // 如果没有已确认的条件，恢复到默认状态（一个空条件）
    formModel.filters.splice(0, formModel.filters.length, defaultFilter())
  }

  isPopoverOpen.value = false
  nextTick(() => {
    formRef.value && formRef.value.clearValidate()
    // 重新计算高度
    updateLineHeight()
  })

  console.log('取消操作: 已恢复到上次确认的状态')
}

function onConfirm() {
  formRef.value.validate((valid) => {
    if (!valid) return

    // 过滤掉无效的条件（包括空数组）
    const validFilters = formModel.filters.filter(f => {
      if (!f.left || !f.op) return false

      // 对于"为空"和"不为空"操作符，不需要检查右值
      if (isOperatorWithoutRightValue(f.op)) {
        return true
      }

      // 对于数组类型的值（如多选），检查数组是否非空
      if (Array.isArray(f.right)) {
        return f.right.length > 0
      }

      // 对于其他类型的值
      return f.right !== undefined && f.right !== null && f.right !== ''
    })

    lastConfirmed.value = validFilters.map(f => ({ ...f }))
    isPopoverOpen.value = false
    console.log('Valid filters:', validFilters)
    // 移除自动调用查询，让父组件决定是否查询
    emit('filter', validFilters)
  })
}

function isBooleanField(fieldValue) {
  return fieldValue === '合作门诊' || fieldValue === '优质机构'
}

function isBuField(fieldValue) {
  return fieldValue === 'BU归属'
}

// 获取BU归属下拉框数据
async function fetchBuOptions() {
  try {
    const response = await getBuOptions()
    if (response && response.code === 200 && Array.isArray(response.data)) {
      // 提取所有的tagValue作为下拉选项
      const options = new Set()
      response.data?.forEach(item => {
        if (item.tagValue && Array.isArray(item.tagValue)) {
          item.tagValue.forEach(value => {
            options.add(value)
          })
        }
      })
      buOptions.value = Array.from(options)
    }
  } catch (error) {
    console.error('获取BU归属数据失败:', error)
    // 如果接口失败，使用默认数据
    buOptions.value = []
  }
}



// Define emits
const emit = defineEmits(['filter'])

const rightPanelRef = ref(null)
const lineHeight = ref(0)

// 统一的高度更新函数
const updateLineHeight = () => {
  nextTick(() => {
    if (rightPanelRef.value) {
      lineHeight.value = rightPanelRef.value.offsetHeight
    }
  })
}

watch(
  () => formModel.filters.length,
  () => {
    updateLineHeight()
    // 添加额外的延迟确保 DOM 完全更新
    setTimeout(() => {
      updateLineHeight()
    }, 10)
  },
  { immediate: true }
)

function handleKeyDown(e) {
  if (e.key === 'Escape' && isPopoverOpen.value) {
    isPopoverOpen.value = false
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeyDown)
  // 获取BU归属下拉框数据
  fetchBuOptions()
})

onBeforeUnmount(() => {
  document.removeEventListener('keydown', handleKeyDown)
})
</script>

<style scoped lang="scss">
.advanced-filter-popover {
  padding: 0 !important;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.08);
  .el-select {
    width: 100% !important;
    min-width: auto !important;
  }
}

// 筛选按钮激活状态样式
.filter-active {
  border-color: var(--el-color-primary) !important;
  background-color: var(--el-color-primary-light-9) !important;

  &:hover {
    border-color: var(--el-color-primary) !important;
    background-color: var(--el-color-primary-light-8) !important;
  }
}

// 主题色文字样式
.text-primary {
  color: var(--el-color-primary) !important;
}

// /**** 新增红色边框样式 ****/
// :deep(.el-select__wrapper.!border-red-500),
// :deep(.el-input__wrapper.!border-red-500) {
//   border-color: #ef4444 !important;
//   box-shadow: 0 0 0 1px #ef4444 !important;
// }
</style>
