<template>
  <div class="p-2 bg-white mx-4">
    <div class="h-[10px]" />
    <div class="title-form">辖区管理</div>
    <el-tabs v-model="activeName" class="mb-2" @tab-change="handleTabClick">
      <el-tab-pane label="辖区管理" name="current">
        <Area />
      </el-tab-pane>
      <el-tab-pane label="辖区下药店" name="export">
        <DrugTable />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script setup>
import { ref } from 'vue';
import Area from './components/area.vue'; // 辖区管理
import DrugTable from './components/drugTable.vue';

const activeName = ref('current')
</script>

<style lang="scss" scoped></style>
