import request from '@/utils/request';
import { AxiosPromise } from 'axios';

export const attributePage = (parentId: any) => {
  return request({
    url: `/lib/attributevalue/node/${parentId}`,
    method: 'get'
  });
};

export const addAttribute = (data: any) => {
  return request({
    url: `/lib/attribute`,
    method: 'post',
    data: data
  });
};
export const attributevaluePage = (query: any) => {
  return request({
    url: `/lib/attributevalue/page`,
    method: 'get',
    params: query
  });
};

export const addAttributevalue = (data: any) => {
  return request({
    url: `/lib/attributevalue`,
    method: 'post',
    data: data
  });
};

export const updateAttributevalue = (data: any) => {
  return request({
    url: `/lib/attributevalue`,
    method: 'put',
    data: data
  });
};

export const deleteAttributevalue = (id: any) => {
  return request({
    url: `/lib/attributevalue/${id}`,
    method: 'delete'
  });
};

export const getAttributeDict = () => {
  return request({
    url: `/lib/attributevalue/dict`,
    method: 'get'
  });
};

export const district = (): AxiosPromise<any> => {
  return request({
    url: `/mdm/hcd/district`,
    method: 'get'
  });
};
