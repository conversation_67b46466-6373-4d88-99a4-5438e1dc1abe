import request from '@/utils/request';
import { AxiosPromise } from 'axios';

export const institutionList = (query: any): AxiosPromise<any> => {
  return request({
    url: '/tum/institution/list',
    method: 'get',
    params: query
  });
};

export const deleteInstitution = (id: any): AxiosPromise<any> => {
  return request({
    url: `/tum/institution/${id}`,
    method: 'delete'
  });
};

export const institutionImport = (data: any) => {
  return request({
    url: '/tum/institution/import',
    method: 'post',
    data: data
  });
};

export const institutionExport = (data: any) => {
  return request({
    url: '/tum/institution/export',
    method: 'post',
    data: data
  });
};

export const cusExport = (data: any) => {
  return request({
    url: '/tum/customer/import',
    method: 'post',
    data: data
  });
};

export const institutionDetail = (id: any): AxiosPromise<any> => {
  return request({
    url: `/tum/institution/${id}`,
    method: 'get'
  });
};

export const institutionUpdate = (data: any) => {
  return request({
    url: '/tum/institution',
    method: 'put',
    data: data
  });
};

export const insDetail = (query: any): AxiosPromise<any> => {
  return request({
    url: `/tum/institution/detail`,
    method: 'get',
    params: query
  });
};

export const dictTypeList = (): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=ins_level`,
    method: 'get'
  });
};

export const insProfessionTypeList = (): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=ins_profession_type`,
    method: 'get'
  });
};

export const insTypeList = (): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=ins_type`,
    method: 'get'
  });
};

export const economicTypeList = (): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=economic_type`,
    method: 'get'
  });
};

export const deptNameList = (): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=dept_name`,
    method: 'get'
  });
};

export const customerList = (query: any): AxiosPromise<any> => {
  return request({
    url: `/tum/customer/list`,
    method: 'get',
    params: query
  });
};

export const deleteCustomer = (ids: any): AxiosPromise<any> => {
  return request({
    url: `/tum/customer/${ids}`,
    method: 'delete'
  });
};

export const customerDetail = (id: any): AxiosPromise<any> => {
  return request({
    url: `/tum/customer/${id}`,
    method: 'get'
  });
};

export const customerUpdate = (data): AxiosPromise<any> => {
  return request({
    url: `/tum/customer`,
    method: 'put',
    data
  });
};
export const professionList = (): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=profession_tech_title`,
    method: 'get'
  });
};

export const administrationList = (): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=administration_lvl`,
    method: 'get'
  });
};

export const lectureLvlList = (): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=lecture_lvl`,
    method: 'get'
  });
};
export const district = (): AxiosPromise<any> => {
  return request({
    url: `/mdm/hcd/district`,
    method: 'get'
  });
};
export const jobList = (query: any): AxiosPromise<any> => {
  return request({
    url: `/tum/customer/detail`,
    method: 'get',
    params: query
  });
};
export const addCustomerType = (data: any): AxiosPromise<any> => {
  return request({
    url: `/tum/customer`,
    method: 'post',
    data
  });
};
export const customerTypeUpdate = (data: any): AxiosPromise<any> => {
  return request({
    url: `/tum/customer/update`,
    method: 'put',
    data
  });
};

export const deleteCustomerType = (ids: any): AxiosPromise<any> => {
  return request({
    url: `/tum/customer/${ids}`,
    method: 'delete'
  });
};

export const cusLevelList = (): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=cus_level`,
    method: 'get'
  });
};
