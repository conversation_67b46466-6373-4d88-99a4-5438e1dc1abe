<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useDtpApplicationList } from '@/api/dtp-drug-store/hooks'
import type { DtpApplyVo } from '@/api/dtp-drug-store/types'
import {
  insDsBuConfigApi
} from "@/api/business/insDs";
import { getDicts } from '@/api/system/dict/data';

// 路由实例
const router = useRouter()

// 查询条件
const queryParams = ref({
  status: '',
  applyType: '',
  drugName: '',
  bu: '',
  specCode: '', // 品规编码，多个用逗号分隔
  pageNum: 1,
  pageSize: 10
});

const buConfigOptions = ref<any>([]);
const specOptions = ref<any>([]); // 品规选项
const selectedSpecs = ref<string[]>([]); // 选中的品规

// 申请类型选项
const applyTypeOptions = [
  { value: 'dtpDsInsert', label: 'DTP关联药店新增' },
  { value: 'dtpDsDeleted', label: 'DTP关联药店删除' },
  { value: 'dtpDsProductInsert', label: 'DTP药店产品新增/删除' }
];

// 审批状态选项
const statusOptions = [
  { value: '-2', label: '已撤回' },
  { value: '-1', label: '已驳回' },
  { value: '1', label: '审批中' },
  { value: '2', label: '已审批' },
];

// BU选项
const buOptions = [
  { value: 'OC', label: '肿瘤' },
  { value: 'PC', label: '初级保健' },
  { value: 'SP', label: '特殊保健' }
];

// 使用API钩子函数
const {
  loading,
  applicationList,
  total,
  fetchApplications,
  changePage,
  changePageSize
} = useDtpApplicationList();

const getBuConfigOptions = async () => {
  const res = await insDsBuConfigApi()
  buConfigOptions.value = res?.rows || []
}

// 获取品规字典选项
const getSpecOptions = async () => {
  try {
    const response = await getDicts('dtp.product.spec');
    if (response && response.data) {
      specOptions.value = response.data.map(item => ({
        label: item.dictLabel,
        value: item.dictValue
      }));
    }
  } catch (error) {
    console.error('获取品规字典失败:', error);
  }
};


// 表格数据加载
onMounted(() => {
  fetchApplications(queryParams.value);
  getBuConfigOptions();
  getSpecOptions();
});

// 查询方法
const handleSearch = () => {
  // 将选中的品规数组转换为逗号分隔的字符串
  queryParams.value.specCode = selectedSpecs.value.join(',');
  queryParams.value.pageNum = 1;
  fetchApplications(queryParams.value);
};

// 重置方法
const handleReset = () => {
  queryParams.value = {
    status: '',
    applyType: '',
    drugName: '',
    bu: '',
    specCode: '',
    pageNum: 1,
    pageSize: 10
  };
  selectedSpecs.value = [];
  fetchApplications(queryParams.value);
};

// 分页方法
const handleCurrentChange = (current: number) => {
  changePage(current);
};

const handleSizeChange = (size: number) => {
  changePageSize(size);
};

// 新增按钮
const handleAdd = () => {
  router.push('/dtp_drug_store/apply');
};

// 审批按钮
const handleApprove = (row: DtpApplyVo) => {
  router.push({
    path: '/dtp_drug_store/approve',
    query: { id: row.id?.toString(), instanceId: row.aeFlowId?.toString() }
  });
};

// 格式化显示
const formatApplyType = (type: string) => {
  const option = applyTypeOptions.find(item => item.value === type);
  return option ? option.label : type;
};

const formatStatus = (status: string) => {
  const option = statusOptions.find(item => item.value === status);
  return option ? option.label : status;
};
</script>

<template>
  <!-- 搜索区域 -->
  <el-form :model="queryParams" inline class="mb-4">
    <el-form-item label="药店名称/编码">
      <el-input v-model="queryParams.drugName" placeholder="请输入药店名称或编码" clearable @keyup.enter="handleSearch" />
    </el-form-item>
    <el-form-item label="申请类型">
      <el-select v-model="queryParams.applyType" placeholder="请选择申请类型" clearable>
        <el-option v-for="item in applyTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </el-form-item>
    <el-form-item label="审批状态">
      <el-select v-model="queryParams.status" placeholder="请选择审批状态" clearable>
        <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </el-form-item>
    <el-form-item label="品规名称">
      <el-select
        v-model="selectedSpecs"
        placeholder="请选择品规名称"
        clearable
        multiple
        collapse-tags
        collapse-tags-tooltip
        style="width: 240px"
      >
        <el-option v-for="item in specOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </el-form-item>
    <el-form-item label="申请人所属BU">
      <el-select v-model="queryParams.bu" placeholder="请选择申请人所属BU" clearable>
        <el-option v-for="item in buConfigOptions" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue" />
      </el-select>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" :loading="loading" @click="handleSearch">搜索</el-button>
      <el-button @click="handleReset">重置</el-button>
    </el-form-item>
  </el-form>

  <!-- 操作按钮区域 -->
  <div class="mb-4 flex justify-between">
    <el-button type="primary" @click="handleAdd">新增</el-button>
  </div>

  <!-- 数据表格 -->
  <el-table v-loading="loading" :data="applicationList" style="width: 100%" border>
    <el-table-column prop="aeFlowId" label="申请单编号" min-width="120" />
    <el-table-column prop="dsMdmCode" label="药店编码" min-width="120" />
    <el-table-column prop="dsName" label="药店名称" min-width="180" />
    <el-table-column prop="dsProvinceName" label="省份" width="100" />
    <el-table-column prop="dsCityName" label="城市" width="100" />
    <el-table-column prop="dsAreaName" label="区县" width="100" />
    <el-table-column label="申请类型" min-width="120">
      <template #default="{ row }">
        {{ formatApplyType(row.applyType) }}
      </template>
    </el-table-column>
    <el-table-column prop="specName" label="品规名称" min-width="180">
      <template #default="{ row }">
        {{ row.specName || '--' }}
      </template>
    </el-table-column>
    <el-table-column prop="deptName" label="申请人所在部门" min-width="140" />
    <el-table-column prop="applicantCode" label="申请人HRCode" min-width="120" />
    <el-table-column prop="applicant" label="申请人" width="100" />
    <el-table-column label="审批状态" min-width="120">
      <template #default="{ row }">
        {{ formatStatus(row.status) }}
      </template>
    </el-table-column>
    <el-table-column prop="applyTime" label="申请人发起时间" min-width="180">
      <template #default="{ row }">
        {{ row.applyTime ? new Date(row.applyTime).toLocaleString() : '--' }}
      </template>
    </el-table-column>
    <el-table-column prop="completeTime" label="审批结束时间" min-width="180">
      <template #default="{ row }">
        {{ row.completeTime ? new Date(row.completeTime).toLocaleString() : '--' }}
      </template>
    </el-table-column>
    <el-table-column prop="bu" label="申请人所属BU" width="120" />
    <el-table-column label="操作" width="100" fixed="right">
      <template #default="{ row }">
        <el-button type="primary" link @click="handleApprove(row)" v-if="row.applyButtonStatus && row.applyButtonStatus !== '0'">
          {{ row.applyButtonStatus === '1' ? '审批' :
             row.applyButtonStatus === '2' ? '查看' : '填写' }}
        </el-button>
        <el-button type="primary" link @click="handleApprove(row)" v-else> 详情 </el-button>
      </template>
    </el-table-column>
  </el-table>

  <!-- 分页 -->
  <div class="flex justify-end mt-4">
    <el-pagination
      v-model:current-page="queryParams.pageNum"
      v-model:page-size="queryParams.pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<style lang="scss" scoped>
.el-form {
  margin-bottom: 16px;
}
</style>
