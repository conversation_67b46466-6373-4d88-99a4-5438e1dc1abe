---
title: athena-cloud
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# athena-cloud

Base URLs:

# Authentication

# tenant-mdm/HCO 管理 Controller

<a id="opIdpage"></a>

## POST HCO 分页列表查询（支持自定义条件组）

POST /data/institution/page

HCO 分页列表查询（支持自定义条件组）

> Body 请求参数

```json
{
  "conditions": [
    {
      "field": "string",
      "operator": "string",
      "value": "string"
    }
  ],
  "logic": "string",
  "pageNum": 0,
  "pageSize": 0
}
```

### 请求参数

| 名称          | 位置   | 类型                                                      | 必选 | 说明 |
| ------------- | ------ | --------------------------------------------------------- | ---- | ---- |
| Authorization | header | string                                                    | 否   | none |
| clientId      | header | string                                                    | 否   | none |
| body          | body   | [InstitutionQueryRequest](#schemainstitutionqueryrequest) | 否   | none |

> 返回示例

> 200 Response

```
{"total":0,"rows":[{"wbId":"string","mdmCode":"string","insName":"string","insType":"string","insGrade":"string","insLevel":"string","provinceName":"string","cityName":"string","districtName":"string","address":"string","btCode":"string","sapCode":"string","u8Code":"string","institutionTagVos":[{"tagType":"string","tagValues":["string"]}],"longitude":"string","latitude":"string","calibrateLongitude":"string","calibrateLatitude":"string","businessName":"string","nameAlias":"string","categoryCode":"string","categoryName":"string","economicType":"string","socialCreditCode":"string","businessPermitName":"string","businessLicenseName":"string","businessTerm":"string","legalPerson":"string","registeredProvince":"string","registeredCity":"string","registeredCounty":"string","registeredAddress":"string","fullRegisteredAddress":"string","insDept":["string"],"licensing":["string"],"onSitePhotos":["string"]}],"code":0,"msg":"string"}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型                                                              |
| ------ | ------------------------------------------------------- | ---- | --------------------------------------------------------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | OK   | [TableDataInfoMdmInstitutionVo](#schematabledatainfomdminstitutionvo) |

<a id="opIdcustomizeHeader"></a>

## POST 自定义表头

POST /data/institution/customize-header

自定义表头

> Body 请求参数

```json
[
  {
    "header": "string",
    "index": "string",
    "isFrozen": "string"
  }
]
```

### 请求参数

| 名称          | 位置   | 类型                                          | 必选 | 说明 |
| ------------- | ------ | --------------------------------------------- | ---- | ---- |
| Authorization | header | string                                        | 否   | none |
| clientId      | header | string                                        | 否   | none |
| body          | body   | [CustomizeHeaderVo](#schemacustomizeheadervo) | 否   | none |

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":{}}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型              |
| ------ | ------------------------------------------------------- | ---- | --------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | OK   | [RVoid](#schemarvoid) |

<a id="opIdqueryByIns"></a>

## POST HCP 分页列表查询

POST /data/customer/by-ins

HCP 分页列表查询

### 请求参数

| 名称          | 位置   | 类型           | 必选 | 说明                     |
| ------------- | ------ | -------------- | ---- | ------------------------ |
| insCode       | query  | string         | 是   | none                     |
| pageSize      | query  | integer(int32) | 否   | 分页大小                 |
| pageNum       | query  | integer(int32) | 否   | 当前页数                 |
| orderByColumn | query  | string         | 否   | 排序列                   |
| isAsc         | query  | string         | 否   | 排序的方向 desc 或者 asc |
| Authorization | header | string         | 否   | none                     |
| clientId      | header | string         | 否   | none                     |

> 返回示例

> 200 Response

```
{"total":0,"rows":[{"wbId":"string","wbCode":"string","mdmCode":"string","name":"string","sex":"string","age":"string","professionTechTitle":"string","teachTitle":"string","level":"string","customerType":"string","job":"string","isMain":"string","status":"string"}],"code":0,"msg":"string"}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型                                                        |
| ------ | ------------------------------------------------------- | ---- | --------------------------------------------------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | OK   | [TableDataInfoMdmCustomerVo](#schematabledatainfomdmcustomervo) |

<a id="opIdinsTagQuery"></a>

## GET 机构标签查询

GET /data/institution/tag

机构标签查询

### 请求参数

| 名称          | 位置   | 类型   | 必选 | 说明 |
| ------------- | ------ | ------ | ---- | ---- |
| insCode       | query  | string | 是   | none |
| Authorization | header | string | 否   | none |
| clientId      | header | string | 否   | none |

> 返回示例

> 200 Response

```
["string"]
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型 |
| ------ | ------------------------------------------------------- | ---- | -------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | OK   | Inline   |

### 返回数据结构

<a id="opIdinsLicenseQuery"></a>

## GET 机构/药店证照查询

GET /data/institution/license

机构/药店证照查询

### 请求参数

| 名称          | 位置   | 类型   | 必选 | 说明 |
| ------------- | ------ | ------ | ---- | ---- |
| insCode       | query  | string | 是   | none |
| Authorization | header | string | 否   | none |
| clientId      | header | string | 否   | none |

> 返回示例

> 200 Response

```
["string"]
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型 |
| ------ | ------------------------------------------------------- | ---- | -------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | OK   | Inline   |

### 返回数据结构

<a id="opIdheaderQuery"></a>

## GET 自定义查询表头

GET /data/institution/header-query

自定义查询表头

### 请求参数

| 名称          | 位置   | 类型   | 必选 | 说明 |
| ------------- | ------ | ------ | ---- | ---- |
| Authorization | header | string | 否   | none |
| clientId      | header | string | 否   | none |

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":[{"header":"string","index":"string","isFrozen":"string"}]}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型                                                |
| ------ | ------------------------------------------------------- | ---- | ------------------------------------------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | OK   | [RListCustomizeHeaderVo](#schemarlistcustomizeheadervo) |

<a id="opIdqueryDetail"></a>

## GET HCO 详情查询

GET /data/institution/detail

HCO 详情查询

### 请求参数

| 名称          | 位置   | 类型   | 必选 | 说明 |
| ------------- | ------ | ------ | ---- | ---- |
| insCode       | query  | string | 是   | none |
| Authorization | header | string | 否   | none |
| clientId      | header | string | 否   | none |

> 返回示例

> 200 Response

```
{"wbId":"string","mdmCode":"string","insName":"string","insType":"string","insGrade":"string","insLevel":"string","provinceName":"string","cityName":"string","districtName":"string","address":"string","btCode":"string","sapCode":"string","u8Code":"string","institutionTagVos":[{"tagType":"string","tagValues":["string"]}],"longitude":"string","latitude":"string","calibrateLongitude":"string","calibrateLatitude":"string","businessName":"string","nameAlias":"string","categoryCode":"string","categoryName":"string","economicType":"string","socialCreditCode":"string","businessPermitName":"string","businessLicenseName":"string","businessTerm":"string","legalPerson":"string","registeredProvince":"string","registeredCity":"string","registeredCounty":"string","registeredAddress":"string","fullRegisteredAddress":"string","insDept":["string"],"licensing":["string"],"onSitePhotos":["string"]}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型                                    |
| ------ | ------------------------------------------------------- | ---- | ------------------------------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | OK   | [MdmInstitutionVo](#schemamdminstitutionvo) |

<a id="opIdinsDeptQuery"></a>

## GET 标准科室查询

GET /data/institution/dept

标准科室查询

### 请求参数

| 名称          | 位置   | 类型   | 必选 | 说明 |
| ------------- | ------ | ------ | ---- | ---- |
| insCode       | query  | string | 是   | none |
| Authorization | header | string | 否   | none |
| clientId      | header | string | 否   | none |

> 返回示例

> 200 Response

```
["string"]
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型 |
| ------ | ------------------------------------------------------- | ---- | -------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | OK   | Inline   |

### 返回数据结构

# 数据模型

<h2 id="tocS_RVoid">RVoid</h2>

<a id="schemarvoid"></a>
<a id="schema_RVoid"></a>
<a id="tocSrvoid"></a>
<a id="tocsrvoid"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {}
}

```

响应信息主体

### 属性

| 名称 | 类型           | 必选  | 约束 | 中文名 | 说明       |
| ---- | -------------- | ----- | ---- | ------ | ---------- |
| code | integer(int32) | false | none |        | 消息状态码 |
| msg  | string         | false | none |        | 消息内容   |
| data | object         | false | none |        | 数据对象   |

<h2 id="tocS_CustomizeHeaderVo">CustomizeHeaderVo</h2>

<a id="schemacustomizeheadervo"></a>
<a id="schema_CustomizeHeaderVo"></a>
<a id="tocScustomizeheadervo"></a>
<a id="tocscustomizeheadervo"></a>

```json
{
  "header": "string",
  "index": "string",
  "isFrozen": "string"
}

```

### 属性

| 名称     | 类型   | 必选  | 约束 | 中文名 | 说明     |
| -------- | ------ | ----- | ---- | ------ | -------- |
| header   | string | false | none |        | 表头     |
| index    | string | false | none |        | 排序     |
| isFrozen | string | false | none |        | 是否冻结 |

<h2 id="tocS_MdmCustomerVo">MdmCustomerVo</h2>

<a id="schemamdmcustomervo"></a>
<a id="schema_MdmCustomerVo"></a>
<a id="tocSmdmcustomervo"></a>
<a id="tocsmdmcustomervo"></a>

```json
{
  "wbId": "string",
  "wbCode": "string",
  "mdmCode": "string",
  "name": "string",
  "sex": "string",
  "age": "string",
  "professionTechTitle": "string",
  "teachTitle": "string",
  "level": "string",
  "customerType": "string",
  "job": "string",
  "isMain": "string",
  "status": "string"
}

```

### 属性

| 名称                | 类型   | 必选  | 约束 | 中文名 | 说明         |
| ------------------- | ------ | ----- | ---- | ------ | ------------ |
| wbId                | string | false | none |        | wbid         |
| wbCode              | string | false | none |        | wb 编码      |
| mdmCode             | string | false | none |        | 主数据编码   |
| name                | string | false | none |        | 客户名称     |
| sex                 | string | false | none |        | 性别         |
| age                 | string | false | none |        | 年龄         |
| professionTechTitle | string | false | none |        | 专业技术职称 |
| teachTitle          | string | false | none |        | 教学职称     |
| level               | string | false | none |        | 客户级别     |
| customerType        | string | false | none |        | 客户类型     |
| job                 | string | false | none |        | 职务         |
| isMain              | string | false | none |        | 是否主职业点 |
| status              | string | false | none |        | 是否启用     |

<h2 id="tocS_TableDataInfoMdmCustomerVo">TableDataInfoMdmCustomerVo</h2>

<a id="schematabledatainfomdmcustomervo"></a>
<a id="schema_TableDataInfoMdmCustomerVo"></a>
<a id="tocStabledatainfomdmcustomervo"></a>
<a id="tocstabledatainfomdmcustomervo"></a>

```json
{
  "total": 0,
  "rows": [
    {
      "wbId": "string",
      "wbCode": "string",
      "mdmCode": "string",
      "name": "string",
      "sex": "string",
      "age": "string",
      "professionTechTitle": "string",
      "teachTitle": "string",
      "level": "string",
      "customerType": "string",
      "job": "string",
      "isMain": "string",
      "status": "string"
    }
  ],
  "code": 0,
  "msg": "string"
}

```

表格分页数据对象

### 属性

| 名称  | 类型                                    | 必选  | 约束 | 中文名 | 说明       |
| ----- | --------------------------------------- | ----- | ---- | ------ | ---------- |
| total | integer(int64)                          | false | none |        | 总记录数   |
| rows  | [[MdmCustomerVo](#schemamdmcustomervo)] | false | none |        | 列表数据   |
| code  | integer(int32)                          | false | none |        | 消息状态码 |
| msg   | string                                  | false | none |        | 消息内容   |

<h2 id="tocS_InstitutionTagVo">InstitutionTagVo</h2>

<a id="schemainstitutiontagvo"></a>
<a id="schema_InstitutionTagVo"></a>
<a id="tocSinstitutiontagvo"></a>
<a id="tocsinstitutiontagvo"></a>

```json
{
  "tagType": "string",
  "tagValues": [
    "string"
  ]
}

```

### 属性

| 名称      | 类型     | 必选  | 约束 | 中文名 | 说明       |
| --------- | -------- | ----- | ---- | ------ | ---------- |
| tagType   | string   | false | none |        | 标签类型   |
| tagValues | [string] | false | none |        | 标签值列表 |

<h2 id="tocS_MdmInstitutionVo">MdmInstitutionVo</h2>

<a id="schemamdminstitutionvo"></a>
<a id="schema_MdmInstitutionVo"></a>
<a id="tocSmdminstitutionvo"></a>
<a id="tocsmdminstitutionvo"></a>

```json
{
  "wbId": "string",
  "mdmCode": "string",
  "insName": "string",
  "insType": "string",
  "insGrade": "string",
  "insLevel": "string",
  "provinceName": "string",
  "cityName": "string",
  "districtName": "string",
  "address": "string",
  "btCode": "string",
  "sapCode": "string",
  "u8Code": "string",
  "institutionTagVos": [
    {
      "tagType": "string",
      "tagValues": [
        "string"
      ]
    }
  ],
  "longitude": "string",
  "latitude": "string",
  "calibrateLongitude": "string",
  "calibrateLatitude": "string",
  "businessName": "string",
  "nameAlias": "string",
  "categoryCode": "string",
  "categoryName": "string",
  "economicType": "string",
  "socialCreditCode": "string",
  "businessPermitName": "string",
  "businessLicenseName": "string",
  "businessTerm": "string",
  "legalPerson": "string",
  "registeredProvince": "string",
  "registeredCity": "string",
  "registeredCounty": "string",
  "registeredAddress": "string",
  "fullRegisteredAddress": "string",
  "insDept": [
    "string"
  ],
  "licensing": [
    "string"
  ],
  "onSitePhotos": [
    "string"
  ]
}

```

### 属性

| 名称                  | 类型                                          | 必选  | 约束 | 中文名 | 说明             |
| --------------------- | --------------------------------------------- | ----- | ---- | ------ | ---------------- |
| wbId                  | string                                        | false | none |        | 机构 id          |
| mdmCode               | string                                        | false | none |        | 主数据编码       |
| insName               | string                                        | false | none |        | 机构名称         |
| insType               | string                                        | false | none |        | 机构类别         |
| insGrade              | string                                        | false | none |        | 机构级别         |
| insLevel              | string                                        | false | none |        | 机构等次         |
| provinceName          | string                                        | false | none |        | 省               |
| cityName              | string                                        | false | none |        | 市               |
| districtName          | string                                        | false | none |        | 区               |
| address               | string                                        | false | none |        | 地址             |
| btCode                | string                                        | false | none |        | 倍通编码         |
| sapCode               | string                                        | false | none |        | sap 编码         |
| u8Code                | string                                        | false | none |        | u8 编码          |
| institutionTagVos     | [[InstitutionTagVo](#schemainstitutiontagvo)] | false | none |        | bu 归属标签      |
| longitude             | string                                        | false | none |        | 经度             |
| latitude              | string                                        | false | none |        | 纬度             |
| calibrateLongitude    | string                                        | false | none |        | 校准后经度       |
| calibrateLatitude     | string                                        | false | none |        | 校准后纬度       |
| businessName          | string                                        | false | none |        | 机构商业名称     |
| nameAlias             | string                                        | false | none |        | 机构别名         |
| categoryCode          | string                                        | false | none |        | 分类编码         |
| categoryName          | string                                        | false | none |        | 分类名称         |
| economicType          | string                                        | false | none |        | 经济类型         |
| socialCreditCode      | string                                        | false | none |        | 统一社会信用代码 |
| businessPermitName    | string                                        | false | none |        | 经营许可证名称   |
| businessLicenseName   | string                                        | false | none |        | 营业执照名称     |
| businessTerm          | string                                        | false | none |        | 营业期限         |
| legalPerson           | string                                        | false | none |        | 法人             |
| registeredProvince    | string                                        | false | none |        | 注册省份         |
| registeredCity        | string                                        | false | none |        | 注册城市         |
| registeredCounty      | string                                        | false | none |        | 注册区县         |
| registeredAddress     | string                                        | false | none |        | 详细注册地址     |
| fullRegisteredAddress | string                                        | false | none |        | 完整注册地址     |
| insDept               | [string]                                      | false | none |        | 标准科室         |
| licensing             | [string]                                      | false | none |        | 证照信息         |
| onSitePhotos          | [string]                                      | false | none |        | 现场照片         |

<h2 id="tocS_TableDataInfoMdmInstitutionVo">TableDataInfoMdmInstitutionVo</h2>

<a id="schematabledatainfomdminstitutionvo"></a>
<a id="schema_TableDataInfoMdmInstitutionVo"></a>
<a id="tocStabledatainfomdminstitutionvo"></a>
<a id="tocstabledatainfomdminstitutionvo"></a>

```json
{
  "total": 0,
  "rows": [
    {
      "wbId": "string",
      "mdmCode": "string",
      "insName": "string",
      "insType": "string",
      "insGrade": "string",
      "insLevel": "string",
      "provinceName": "string",
      "cityName": "string",
      "districtName": "string",
      "address": "string",
      "btCode": "string",
      "sapCode": "string",
      "u8Code": "string",
      "institutionTagVos": [
        {
          "tagType": "string",
          "tagValues": [
            "string"
          ]
        }
      ],
      "longitude": "string",
      "latitude": "string",
      "calibrateLongitude": "string",
      "calibrateLatitude": "string",
      "businessName": "string",
      "nameAlias": "string",
      "categoryCode": "string",
      "categoryName": "string",
      "economicType": "string",
      "socialCreditCode": "string",
      "businessPermitName": "string",
      "businessLicenseName": "string",
      "businessTerm": "string",
      "legalPerson": "string",
      "registeredProvince": "string",
      "registeredCity": "string",
      "registeredCounty": "string",
      "registeredAddress": "string",
      "fullRegisteredAddress": "string",
      "insDept": [
        "string"
      ],
      "licensing": [
        "string"
      ],
      "onSitePhotos": [
        "string"
      ]
    }
  ],
  "code": 0,
  "msg": "string"
}

```

表格分页数据对象

### 属性

| 名称  | 类型                                          | 必选  | 约束 | 中文名 | 说明       |
| ----- | --------------------------------------------- | ----- | ---- | ------ | ---------- |
| total | integer(int64)                                | false | none |        | 总记录数   |
| rows  | [[MdmInstitutionVo](#schemamdminstitutionvo)] | false | none |        | 列表数据   |
| code  | integer(int32)                                | false | none |        | 消息状态码 |
| msg   | string                                        | false | none |        | 消息内容   |

<h2 id="tocS_RListCustomizeHeaderVo">RListCustomizeHeaderVo</h2>

<a id="schemarlistcustomizeheadervo"></a>
<a id="schema_RListCustomizeHeaderVo"></a>
<a id="tocSrlistcustomizeheadervo"></a>
<a id="tocsrlistcustomizeheadervo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "header": "string",
      "index": "string",
      "isFrozen": "string"
    }
  ]
}

```

响应信息主体

### 属性

| 名称 | 类型                                            | 必选  | 约束 | 中文名 | 说明       |
| ---- | ----------------------------------------------- | ----- | ---- | ------ | ---------- |
| code | integer(int32)                                  | false | none |        | 消息状态码 |
| msg  | string                                          | false | none |        | 消息内容   |
| data | [[CustomizeHeaderVo](#schemacustomizeheadervo)] | false | none |        | 数据对象   |

<h2 id="tocS_InstitutionQueryRequest">InstitutionQueryRequest</h2>

<a id="schemainstitutionqueryrequest"></a>
<a id="schema_InstitutionQueryRequest"></a>
<a id="tocSinstitutionqueryrequest"></a>
<a id="tocsinstitutionqueryrequest"></a>

```json
{
  "conditions": [
    {
      "field": "string",
      "operator": "string",
      "value": "string"
    }
  ],
  "logic": "string",
  "pageNum": 0,
  "pageSize": 0
}

```

### 属性

| 名称       | 类型                                      | 必选  | 约束 | 中文名 | 说明                  |
| ---------- | ----------------------------------------- | ----- | ---- | ------ | --------------------- |
| conditions | [[QueryCondition](#schemaquerycondition)] | false | none |        | 条件数组              |
| logic      | string                                    | false | none |        | 条件逻辑"and" 或 "or" |
| pageNum    | integer(int32)                            | false | none |        | 分页数                |
| pageSize   | integer(int32)                            | false | none |        | 分页大小              |

<h2 id="tocS_QueryCondition">QueryCondition</h2>

<a id="schemaquerycondition"></a>
<a id="schema_QueryCondition"></a>
<a id="tocSquerycondition"></a>
<a id="tocsquerycondition"></a>

```json
{
  "field": "string",
  "operator": "string",
  "value": "string"
}

```

### 属性

| 名称     | 类型   | 必选  | 约束 | 中文名 | 说明                                                      |
| -------- | ------ | ----- | ---- | ------ | --------------------------------------------------------- |
| field    | string | false | none |        | 字段名                                                    |
| operator | string | false | none |        | 操作符: eq, neq, contains, not_contains, empty, not_empty |
| value    | string | false | none |        | 右值                                                      |
