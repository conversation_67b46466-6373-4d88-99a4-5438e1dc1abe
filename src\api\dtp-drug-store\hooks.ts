import { ref, computed } from 'vue';
import type { Ref } from 'vue';
import type {
  GsJurDrugBo,
  DtpJurDrugVo,
  DtpJurDrugProductVo,
  GsJurDrugProductBo,
  DtpApplyVo,
  DtpDrugApplyPageBo,
  TableDataInfo,
  DtpJurProductVo
} from './types';
import { getDtpDrugstoreList, getDtpDrugstoreDetail, getDrugstoreProducts, getJurisdictionProducts, getDtpApplicationList } from './index';

/**
 * DTP药店列表钩子函数（带分页）
 */
export function useDtpDrugstoreList(defaultParams: Partial<GsJurDrugBo> = {}) {
  const loading = ref(false);
  const params = ref<GsJurDrugBo>({
    pageNum: 1,
    pageSize: 10,
    ...defaultParams
  });
  const drugStoreList = ref<DtpJurDrugVo[]>([]);
  const total = ref(0);

  const fetchDrugStores = async (newParams?: Partial<GsJurDrugBo>) => {
    if (newParams) {
      params.value = { ...params.value, ...newParams };
    }

    loading.value = true;
    try {
      const response = await getDtpDrugstoreList(params.value);
      if (response && response.rows) {
        drugStoreList.value = response.rows;
        total.value = response.total;
      }
    } catch (error) {
      console.error('获取药店列表失败:', error);
    } finally {
      loading.value = false;
    }
  };

  const refresh = () => fetchDrugStores();

  const changePage = (page: number) => {
    params.value.pageNum = page;
    fetchDrugStores();
  };

  const changePageSize = (size: number) => {
    params.value.pageSize = size;
    params.value.pageNum = 1;
    fetchDrugStores();
  };

  return {
    loading,
    params,
    drugStoreList,
    total,
    fetchDrugStores,
    refresh,
    changePage,
    changePageSize
  };
}

/**
 * 获取和缓存DTP药店详情的钩子函数
 */
export function useDtpDrugstoreDetail() {
  const loading = ref(false);
  const currentDrugStore = ref<DtpJurDrugVo | null>(null);
  const error = ref<string | null>(null);

  const fetchDrugStoreDetail = async (jurDsCode: string) => {
    loading.value = true;
    error.value = null;

    try {
      const response = await getDtpDrugstoreDetail(jurDsCode);
      if (response && response.data) {
        currentDrugStore.value = response.data;
      }
    } catch (err) {
      error.value = '获取药店详情失败';
      console.error(error.value, err);
    } finally {
      loading.value = false;
    }
  };

  return {
    loading,
    currentDrugStore,
    error,
    fetchDrugStoreDetail
  };
}

/**
 * 管理DTP药店产品的钩子函数
 */
export function useDrugStoreProducts() {
  const loading = ref(false);
  const products = ref<DtpJurDrugProductVo[]>([]);
  const error = ref<string | null>(null);

  const fetchProducts = async (params: GsJurDrugProductBo) => {
    loading.value = true;
    error.value = null;

    try {
      const response = await getDrugstoreProducts(params);
      if (response && response.data) {
        products.value = response.data;
      }
    } catch (err) {
      error.value = '获取药店产品失败';
      console.error(error.value, err);
    } finally {
      loading.value = false;
    }
  };

  return {
    loading,
    products,
    error,
    fetchProducts
  };
}

/**
 * 获取当前用户可用辖区产品的钩子函数
 */
export function useJurisdictionProducts() {
  const loading = ref(false);
  const products = ref<DtpJurProductVo[]>([]);
  const error = ref<string | null>(null);

  const fetchJurisdictionProducts = async () => {
    loading.value = true;
    error.value = null;

    try {
      const response = await getJurisdictionProducts();
      if (response && response.data) {
        products.value = response.data;
      }
    } catch (err) {
      error.value = '获取辖区产品失败';
      console.error(error.value, err);
    } finally {
      loading.value = false;
    }
  };

  return {
    loading,
    products,
    error,
    fetchJurisdictionProducts
  };
}

/**
 * DTP申请列表的钩子函数（带分页）
 */
export function useDtpApplicationList(defaultParams: Partial<DtpDrugApplyPageBo> = {}) {
  const loading = ref(false);
  const params = ref<DtpDrugApplyPageBo>({
    pageNum: 1,
    pageSize: 10,
    ...defaultParams
  });
  const applicationList = ref<DtpApplyVo[]>([]);
  const total = ref(0);
  const tableData = ref<TableDataInfo<DtpApplyVo> | null>(null);

  const fetchApplications = async (newParams?: Partial<DtpDrugApplyPageBo>) => {
    if (newParams) {
      params.value = { ...params.value, ...newParams };
    }

    loading.value = true;
    try {
      const response = await getDtpApplicationList(params.value);
      if (response && response.data) {
        tableData.value = response.data;
        applicationList.value = response.data.rows;
        total.value = response.data.total;
      }
    } catch (error) {
      console.error('获取申请列表失败:', error);
    } finally {
      loading.value = false;
    }
  };

  const refresh = () => fetchApplications();

  const changePage = (page: number) => {
    params.value.pageNum = page;
    fetchApplications();
  };

  const changePageSize = (size: number) => {
    params.value.pageSize = size;
    params.value.pageNum = 1;
    fetchApplications();
  };

  return {
    loading,
    params,
    applicationList,
    total,
    tableData,
    fetchApplications,
    refresh,
    changePage,
    changePageSize
  };
}
