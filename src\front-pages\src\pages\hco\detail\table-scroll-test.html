<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表格横向滚动测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        
        /* 测试1：基本滚动容器 */
        .table-container-1 {
            width: 100%;
            overflow-x: auto;
            overflow-y: hidden;
            border: 1px solid #ebeef5;
            border-radius: 4px;
            background-color: #fff;
        }
        
        .table-container-1::-webkit-scrollbar {
            height: 8px;
        }
        
        .table-container-1::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }
        
        .table-container-1::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }
        
        .table-container-1::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
        
        /* 测试2：强制宽度 */
        .table-container-2 {
            width: 100%;
            overflow-x: auto;
            overflow-y: hidden;
            border: 1px solid #ebeef5;
            border-radius: 4px;
            background-color: #fff;
        }
        
        .wide-table {
            min-width: 2400px;
            width: 2400px;
            border-collapse: collapse;
        }
        
        .wide-table th,
        .wide-table td {
            border: 1px solid #ddd;
            padding: 8px 12px;
            text-align: left;
            white-space: nowrap;
        }
        
        .wide-table th {
            background-color: #f5f7fa;
            font-weight: 600;
        }
        
        /* 测试3：Flex布局 */
        .table-container-3 {
            width: 100%;
            overflow-x: auto;
            overflow-y: hidden;
            border: 1px solid #ebeef5;
            border-radius: 4px;
            background-color: #fff;
            display: block;
        }
        
        .flex-table {
            display: flex;
            flex-direction: column;
            min-width: 2400px;
        }
        
        .flex-row {
            display: flex;
            border-bottom: 1px solid #ddd;
        }
        
        .flex-cell {
            flex: 0 0 150px;
            padding: 8px 12px;
            border-right: 1px solid #ddd;
            white-space: nowrap;
        }
        
        .flex-cell.wide {
            flex: 0 0 250px;
        }
        
        .flex-cell.header {
            background-color: #f5f7fa;
            font-weight: 600;
        }
        
        .status {
            margin-top: 10px;
            padding: 10px;
            background-color: #f0f9ff;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .success {
            background-color: #f0f9ff;
            color: #0369a1;
        }
        
        .error {
            background-color: #fef2f2;
            color: #dc2626;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>表格横向滚动测试</h1>
        <p>这个页面用于测试不同的表格横向滚动实现方案。</p>
        
        <div class="test-section">
            <h3>测试1：基本HTML表格 + 滚动容器</h3>
            <div class="table-container-1">
                <table class="wide-table">
                    <thead>
                        <tr>
                            <th style="min-width: 150px;">事业部</th>
                            <th style="min-width: 150px;">备案ID</th>
                            <th style="min-width: 250px;">品规名称</th>
                            <th style="min-width: 150px;">品规编码</th>
                            <th style="min-width: 180px;">品规倍通编码</th>
                            <th style="min-width: 150px;">产品分类</th>
                            <th style="min-width: 120px;">品牌</th>
                            <th style="min-width: 160px;">终端配送方式</th>
                            <th style="min-width: 120px;">备案月份</th>
                            <th style="min-width: 100px;">备案状态</th>
                            <th style="min-width: 100px;">定版状态</th>
                            <th style="min-width: 120px;">二级分类</th>
                            <th style="min-width: 120px;">三级分类</th>
                            <th style="min-width: 100px;">上报人姓名</th>
                            <th style="min-width: 100px;">上报人工号</th>
                            <th style="min-width: 100px;">BU负责人</th>
                            <th style="min-width: 120px;">BU负责人工号</th>
                            <th style="min-width: 160px;">创建时间</th>
                            <th style="min-width: 160px;">更新时间</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>事业部A</td>
                            <td>BA2023071401</td>
                            <td>测试产品名称1</td>
                            <td>P001</td>
                            <td>BT001</td>
                            <td>分类A</td>
                            <td>品牌A</td>
                            <td>配送方式1</td>
                            <td>2023-07</td>
                            <td>待审核</td>
                            <td>未定版</td>
                            <td>二级分类A</td>
                            <td>三级分类A</td>
                            <td>张三</td>
                            <td>EMP001</td>
                            <td>李四</td>
                            <td>MGR001</td>
                            <td>2023-07-14 10:00:00</td>
                            <td>2023-07-14 10:00:00</td>
                        </tr>
                        <tr>
                            <td>事业部B</td>
                            <td>BA2023071402</td>
                            <td>测试产品名称2</td>
                            <td>P002</td>
                            <td>BT002</td>
                            <td>分类B</td>
                            <td>品牌B</td>
                            <td>配送方式2</td>
                            <td>2023-07</td>
                            <td>已通过</td>
                            <td>已定版</td>
                            <td>二级分类B</td>
                            <td>三级分类B</td>
                            <td>王五</td>
                            <td>EMP002</td>
                            <td>赵六</td>
                            <td>MGR002</td>
                            <td>2023-07-14 11:00:00</td>
                            <td>2023-07-14 11:00:00</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="status success" id="status1">
                ✅ 如果您能看到横向滚动条并且可以滚动查看右侧列，说明基本方案有效。
            </div>
        </div>
        
        <div class="test-section">
            <h3>测试2：Flex布局表格</h3>
            <div class="table-container-3">
                <div class="flex-table">
                    <div class="flex-row">
                        <div class="flex-cell header">事业部</div>
                        <div class="flex-cell header">备案ID</div>
                        <div class="flex-cell header wide">品规名称</div>
                        <div class="flex-cell header">品规编码</div>
                        <div class="flex-cell header">品规倍通编码</div>
                        <div class="flex-cell header">产品分类</div>
                        <div class="flex-cell header">品牌</div>
                        <div class="flex-cell header">终端配送方式</div>
                        <div class="flex-cell header">备案月份</div>
                        <div class="flex-cell header">备案状态</div>
                        <div class="flex-cell header">定版状态</div>
                        <div class="flex-cell header">二级分类</div>
                        <div class="flex-cell header">三级分类</div>
                        <div class="flex-cell header">上报人姓名</div>
                        <div class="flex-cell header">上报人工号</div>
                        <div class="flex-cell header">BU负责人</div>
                    </div>
                    <div class="flex-row">
                        <div class="flex-cell">事业部A</div>
                        <div class="flex-cell">BA2023071401</div>
                        <div class="flex-cell wide">测试产品名称1</div>
                        <div class="flex-cell">P001</div>
                        <div class="flex-cell">BT001</div>
                        <div class="flex-cell">分类A</div>
                        <div class="flex-cell">品牌A</div>
                        <div class="flex-cell">配送方式1</div>
                        <div class="flex-cell">2023-07</div>
                        <div class="flex-cell">待审核</div>
                        <div class="flex-cell">未定版</div>
                        <div class="flex-cell">二级分类A</div>
                        <div class="flex-cell">三级分类A</div>
                        <div class="flex-cell">张三</div>
                        <div class="flex-cell">EMP001</div>
                        <div class="flex-cell">李四</div>
                    </div>
                </div>
            </div>
            <div class="status success" id="status2">
                ✅ Flex布局方案也应该能正常滚动。
            </div>
        </div>
        
        <div class="test-section">
            <h3>诊断信息</h3>
            <div id="diagnostics">
                <p><strong>浏览器信息：</strong><span id="browser-info"></span></p>
                <p><strong>视口宽度：</strong><span id="viewport-width"></span>px</p>
                <p><strong>容器宽度：</strong><span id="container-width"></span>px</p>
                <p><strong>表格宽度：</strong><span id="table-width"></span>px</p>
                <p><strong>滚动条支持：</strong><span id="scrollbar-support"></span></p>
            </div>
        </div>
    </div>

    <script>
        // 更新诊断信息
        function updateDiagnostics() {
            document.getElementById('browser-info').textContent = navigator.userAgent.split(' ').slice(-2).join(' ');
            document.getElementById('viewport-width').textContent = window.innerWidth;
            
            const container = document.querySelector('.table-container-1');
            const table = document.querySelector('.wide-table');
            
            if (container && table) {
                document.getElementById('container-width').textContent = container.offsetWidth;
                document.getElementById('table-width').textContent = table.offsetWidth;
                
                // 检查是否有滚动条
                const hasScrollbar = container.scrollWidth > container.clientWidth;
                document.getElementById('scrollbar-support').textContent = hasScrollbar ? '✅ 有滚动条' : '❌ 无滚动条';
                
                // 更新状态
                if (hasScrollbar) {
                    document.getElementById('status1').className = 'status success';
                    document.getElementById('status1').innerHTML = '✅ 滚动功能正常！表格宽度超出容器，滚动条已显示。';
                } else {
                    document.getElementById('status1').className = 'status error';
                    document.getElementById('status1').innerHTML = '❌ 滚动功能异常！表格没有超出容器宽度。';
                }
            }
        }
        
        // 页面加载完成后更新诊断信息
        window.addEventListener('load', updateDiagnostics);
        window.addEventListener('resize', updateDiagnostics);
        
        // 每秒更新一次，确保动态变化被捕获
        setInterval(updateDiagnostics, 1000);
    </script>
</body>
</html>
