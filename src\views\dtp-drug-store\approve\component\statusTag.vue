<template>
  <div class="status-tag" :style="{color:props.textColor,backgroundColor:props.bgcColor}">{{ task(props.taskState).text }}</div>
</template>
<script setup>
import { defineProps, } from "vue";
const props = defineProps({
  textColor:{
    type: String,
    default: "#2551F2"
  },
  bgcColor:{
    type: String,
    default: "#E8F0FF"
  },
  taskState: {
    type: Number
  }
})

const task = (taskState) => {
  // 0，活动 1，跳转 2，完成 3，拒绝 4，撤销审批 5，超时 6，终止 7，驳回终止
  switch (taskState) {
    case '0' :
      return {
        text: '审批中'
      }

    case '1' :
      return {
        text: '跳转'
      }

    case '2' :
      return {
        text: '完成'
      }

    case '3' :
      return {
        text: '拒绝'
      }

    case '4' :
      return {
        text: '撤销审批'
      }

    case '5' :
      return {
        text: '超时'
      }

    case '6' :
      return {
        text: '终止'
      }

    case '7' :
      return {
        text: '驳回终止'
      }
    default :
    return {}
  }
}
</script>
<style lang="scss" scoped>
.status-tag {
  height: 22px;
   padding: 0 8px;
   font-size: 14px;
   border-radius: 2px;
   line-height: 22px;

}
</style>
