import request from '@/utils/request';
import { AxiosPromise } from 'axios';
// 获取部门下权限范围
export const authorityPage = (query: any): AxiosPromise<any> => {
  return request({
    url: '/hr/authority/page',
    method: 'get',
    params: query
  });
};
export const authorityInsert = (data: any): AxiosPromise<any> => {
  return request({
    url: '/hr/authority/insert',
    method: 'post',
    data
  });
};
export const authorityEdit = (data: any): AxiosPromise<any> => {
	return request({
			url: '/hr/authority/update',
			method: 'post',
			data
	});
};
export const authorityDetail = (params: any): AxiosPromise<any> => {
	return request({
			url: '/hr/authority/detail',
			method: 'get',
			params
	});
};
export const authorityDel = (params: any): AxiosPromise<any> => {
	return request({
			url: `/hr/authority/${params.id}`,
			method: 'delete',
	});
};
export const authorityUpdateAuthFlag = (params: any,id:String): AxiosPromise<any> => {
	return request({
			url: `/hr/authority/updateAuthFlag/${id}`,
			method: 'post',
			params

	});
};
