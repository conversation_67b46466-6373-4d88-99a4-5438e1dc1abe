# 路由跳转清除功能测试指南

## 测试目的

验证修复后的路由跳转清除功能是否正确区分详情页跳转和其他页面跳转。

## 测试场景

### 场景 1：详情页跳转（应保持数据）

**测试步骤：**
1. 在 HCO 列表页面设置查询条件：
   - BU归属：选择一个或多个BU
   - 机构类别：选择医院
   - 机构名称：输入"北京"
   - 高级筛选：添加省份="北京"

2. 翻页到第3页，设置每页50条

3. 点击任意机构名称，跳转到详情页

4. 在详情页点击"返回"按钮

**预期结果：**
- ✅ 回到列表页面第3页
- ✅ 每页显示50条记录
- ✅ 所有查询条件完全保持
- ✅ 控制台显示："跳转到详情页，保持持久化查询条件"

### 场景 2：菜单跳转（应清除数据）

**测试步骤：**
1. 在 HCO 列表页面设置查询条件（同场景1）

2. 翻页到第3页，设置每页50条

3. 点击左侧菜单的其他项目（如"用户管理"）

4. 再次点击 HCO 菜单项

**预期结果：**
- ✅ 回到列表页面第1页
- ✅ 每页显示默认条数（20条）
- ✅ 所有查询条件被清除，显示默认状态
- ✅ 控制台显示："离开HCO页面，已清除持久化查询条件"

### 场景 3：地址栏跳转（应清除数据）

**测试步骤：**
1. 在 HCO 列表页面设置查询条件

2. 在浏览器地址栏直接输入其他页面URL

3. 通过菜单或地址栏返回 HCO 页面

**预期结果：**
- ✅ 查询条件被清除
- ✅ 显示默认状态

## 技术验证

### 1. 控制台日志

在浏览器开发者工具的控制台中观察日志：

```
// 详情页跳转时
跳转到详情页，保持持久化查询条件

// 其他页面跳转时
离开HCO页面，已清除持久化查询条件
```

### 2. localStorage 检查

在控制台中检查 localStorage：

```javascript
// 查看当前保存的数据
console.log(localStorage.getItem('hco_query_conditions'))

// 详情页跳转后应该有数据
// 其他页面跳转后应该为 null
```

### 3. 路由守卫验证

确认路由守卫正确工作：

```javascript
// 在 onBeforeRouteLeave 中添加调试日志
onBeforeRouteLeave((to) => {
  console.log('准备跳转到:', to.path)
  if (to.path !== '/ins_mdm_manage/ins_mdm/detail') {
    console.log('非详情页跳转，清除数据')
  } else {
    console.log('详情页跳转，保持数据')
  }
})
```

## 常见问题排查

### 1. 详情页跳转时数据被清除

**可能原因：**
- 详情页路径配置错误
- 路由守卫判断逻辑有误

**解决方法：**
- 检查详情页路径是否为 `/ins_mdm_manage/ins_mdm/detail`
- 确认路由配置正确

### 2. 菜单跳转时数据没有清除

**可能原因：**
- 路由守卫没有正确触发
- 清除逻辑有问题

**解决方法：**
- 检查控制台是否有错误信息
- 验证 `onBeforeRouteLeave` 是否正确导入和使用

### 3. 页面刷新时数据丢失

**说明：**
这是正常行为，页面刷新不会触发路由守卫，但会重新加载页面，此时应该恢复保存的数据。

## 自动化测试

使用 `test-persistence.html` 中的测试功能：

1. 点击"测试分页持久化"验证基本功能
2. 点击"模拟非详情页跳转清除"验证清除功能
3. 检查所有测试结果是否为成功状态

## 验证清单

- [ ] 详情页跳转保持查询条件
- [ ] 详情页跳转保持分页信息
- [ ] 菜单跳转清除查询条件
- [ ] 菜单跳转清除分页信息
- [ ] 控制台日志正确显示
- [ ] localStorage 状态正确
- [ ] 自动化测试通过

## 回归测试

确保修复没有影响其他功能：

- [ ] 基础查询功能正常
- [ ] 高级筛选功能正常
- [ ] 分页功能正常
- [ ] 重置功能正常
- [ ] 表格显示正常
- [ ] 详情页功能正常

## 性能考虑

路由守卫的性能影响：

- ✅ 只在路由跳转时执行，不影响页面内操作
- ✅ 判断逻辑简单，性能开销极小
- ✅ 比全局路由守卫更高效

## 总结

通过使用 `onBeforeRouteLeave` 路由守卫，我们成功实现了：

1. **智能区分跳转类型**：详情页跳转保持数据，其他跳转清除数据
2. **用户体验优化**：符合用户对不同跳转行为的预期
3. **技术实现可靠**：使用 Vue Router 官方API，稳定可靠
4. **维护性良好**：逻辑清晰，易于理解和维护

这个解决方案完美平衡了数据持久化的便利性和页面间的独立性。
