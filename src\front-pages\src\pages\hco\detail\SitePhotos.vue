<template>
  <div class="site-photos">
    <h3 class="section-title">门头照/院内照</h3>

    <div class="photos-container">
      <el-scrollbar>
        <div class="photo-list">
          <div v-for="(photo, index) in photos" :key="index" class="photo-item">
            <el-image :src="photo.url" :preview-src-list="photoUrls" :initial-index="index" fit="cover" class="photo-image">
              <template #error>
                <div class="image-error">
                  <el-icon><Picture /></el-icon>
                  <span>加载失败</span>
                </div>
              </template>
              <template #placeholder>
                <div class="image-placeholder">
                  <el-icon><Loading /></el-icon>
                </div>
              </template>
            </el-image>
            <div class="photo-date">{{ formatDateTime(photo.datetime) }}</div>
          </div>

          <!-- 上传按钮 -->
          <div v-if="editable" class="photo-upload">
            <el-upload action="#" list-type="picture-card" :auto-upload="false" :show-file-list="false" :on-change="handlePhotoChange">
              <template #default>
                <el-icon><Plus /></el-icon>
                <span>上传照片</span>
              </template>
            </el-upload>
          </div>
        </div>
      </el-scrollbar>
    </div>

    <!-- 空状态 -->
    <el-empty v-if="!photos.length && !editable" description="暂无现场照片" />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { Picture, Loading, Plus } from '@element-plus/icons-vue';
import dayjs from 'dayjs';

defineProps({
  editable: {
    type: Boolean,
    default: false
  }
});

// 照片数据
const photos = ref([
  // {
  //   url: 'path/to/photo1.jpg',
  //   datetime: '2026-06-10 10:00:00'
  // },
  // {
  //   url: 'path/to/photo2.jpg',
  //   datetime: '2026-06-10 10:30:00'
  // },
  // {
  //   url: 'path/to/photo3.jpg',
  //   datetime: '2026-06-10 11:00:00'
  // },
  // {
  //   url: 'path/to/photo4.jpg',
  //   datetime: '2026-06-10 11:30:00'
  // }
]);

// 用于预览的URL列表
const photoUrls = computed(() => photos.value.map(photo => photo.url));

// 格式化日期时间
const formatDateTime = (datetime) => {
  return dayjs(datetime).format('YYYY-MM-DD');
};

// 处理照片上传
const handlePhotoChange = (file) => {
  // 这里处理照片上传逻辑
  const reader = new FileReader();
  reader.onload = (e) => {
    photos.value.push({
      url: e.target.result,
      datetime: dayjs().format('YYYY-MM-DD HH:mm:ss')
    });
  };
  reader.readAsDataURL(file.raw);
};
</script>

<style lang="scss" scoped>
.site-photos {
  .section-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
  }

  .photos-container {
    margin: 0 -10px;

    .photo-list {
      display: flex;
      padding: 10px;
      gap: 20px;

      .photo-item {
        flex: 0 0 auto;
        width: 240px;

        .photo-image {
          width: 240px;
          height: 180px;
          border-radius: 8px;
          border: 1px solid #eee;
          overflow: hidden;
          transition: all 0.3s;
          cursor: pointer;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          }
        }

        .photo-date {
          margin-top: 8px;
          text-align: center;
          color: #666;
          font-size: 14px;
        }
      }
    }
  }

  .photo-upload {
    :deep(.el-upload--picture-card) {
      width: 240px;
      height: 180px;
      border-radius: 8px;

      .el-icon {
        font-size: 24px;
        color: #909399;
        margin-bottom: 8px;
      }
    }
  }

  .image-error,
  .image-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #909399;
    background: #f5f7fa;

    .el-icon {
      font-size: 24px;
      margin-bottom: 8px;
    }
  }
}

:deep(.el-scrollbar__wrap) {
  overflow-x: auto;
  overflow-y: hidden;
}

:deep(.el-scrollbar__bar.is-horizontal) {
  opacity: 1;
}
</style>
