/**
 * 部门查询参数
 */
export interface DeptQuery extends PageQuery {
  deptName?: string;
  status?: any;
  parentId?: number;
  deptType?: string;
  version?: string;
  source?: any;
  sysEnableFlag?: any;
}

/**
 * 部门类型
 */
export interface DeptVO extends BaseEntity {
  id: number | string;
  parentName: string;
  parentId: number | string;
  children: DeptVO[];
  deptId: number | string;
  deptName: string;
  orderNum: number;
  leader: string;
  phone: string;
  email: string;
  status: string;
  delFlag: string;
  ancestors: string;
  menuId: string | number;
  deptType: string | number;
  version?: string;
  source?: any;
  sysEnableFlag?: any;
}

/**
 * 部门表单类型
 */
export interface DeptForm {
  companyCode: any;
  deptCode: any;
  parentName?: string;
  parentId?: number | string;
  children?: DeptForm[];
  deptId?: number | string;
  deptName?: string;
  orderNum?: number;
  leader?: string;
  phone?: string;
  email?: string;
  status?: string;
  delFlag?: string;
  ancestors?: string;
  version?: string;
  deptType: string | number;
  source?: any;
  sysEnableFlag?: any;
  deptLevel?: number;
  sysOprFlag?: '1' | '0';
  parentDeptCode: any
}
