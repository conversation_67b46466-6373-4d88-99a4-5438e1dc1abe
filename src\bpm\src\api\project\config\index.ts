import request from '@/utils/request';
import { AxiosPromise } from 'axios';
// 获取一级列表
export const getListApi = (query: any, url: string): AxiosPromise<any> => {
  return request({
    url,
    method: 'get',
    params: query
  });
};

// 获取项目类型子级
export const getChildrenListApi = (url: string): AxiosPromise<any> => {
  return request({
    url,
    method: 'get'
  });
};

//新增项
export const addApi = (data: any, url: string) => {
  return request({
    url,
    method: 'post',
    data
  });
};
// 更新项
export const editApi = (data: any, url: string) => {
  return request({
    url,
    method: 'put',
    data: data
  });
};
/**
 * 删除测试单
 * @param id  id: string | number | Array<string | number>
 */
export const delApi = (url: any) => {
  return request({
    url,
    method: 'delete'
  });
};
