import request from '@/utils/request';
import { AxiosPromise } from 'axios';

export const userList = (query: any) => {
  return request({
    url: '/tum/user/list',
    method: 'get',
    params: query
  });
};

export const userDetail = (query: any): AxiosPromise<any> => {
  return request({
    // url: `/hcd/project/statistics/${id}`,
    url: `/tum/user/detail/${query}`,
    method: 'get'
  });
};

export const userRecover = (id: any) => {
  return request({
    url: `/tum/user/recover/${id}`,
    method: 'get'
  });
};

export const useRban = (data: any) => {
  return request({
    url: '/tum/user/ban',
    method: 'post',
    data: data
  });
};

// 恢复用户提示
export const recoverNoticeApi = (id: any) => {
  return request({
    url: `/hr/user/recover-notice/${id}`,
    method: 'post'
  });
};

// 禁用用户提示 /hr/user/ban-notice/{userId}

export const banNoticeApi = (id: any) => {
  return request({
    url: `/hr/user/ban-notice/${id}`,
    method: 'post'
  });
};

// 获取用户权限范围列表
export const userDeptListApi = (query: any) => {
  return request({
    url: `/tum/dept-user/list-by-user`,
    method: 'get',
    params: query
  });
};
