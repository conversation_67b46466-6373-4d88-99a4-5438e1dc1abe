<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BU归属标签显示测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
        }
        .code-block {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
        }
        .tag {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 12px;
            margin: 2px;
            border-radius: 16px;
            display: inline-block;
            font-size: 12px;
            border: 1px solid #bbdefb;
        }
        .tag-container {
            margin: 10px 0;
            min-height: 30px;
            padding: 10px;
            border: 1px dashed #ccc;
            border-radius: 4px;
        }
        .test-button {
            background: #1976d2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #1565c0;
        }
        .result-area {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            min-height: 100px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        .status-success {
            color: #4caf50;
            font-weight: bold;
        }
        .status-error {
            color: #f44336;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>BU归属标签显示测试</h1>
        
        <div class="section">
            <div class="section-title">1. 接口数据结构</div>
            <p>根据您提供的接口返回数据结构：</p>
            <div class="code-block">
{
    "code": 200,
    "msg": "操作成功",
    "data": [
        {
            "bu": "集团",
            "tagType": "bu标签",
            "tagValue": ["广阔"]
        }
    ]
}
            </div>
        </div>
        
        <div class="section">
            <div class="section-title">2. 标签处理逻辑</div>
            <p>处理函数会将标签数据转换为显示格式：<code>BU名称-标签值</code></p>
            <div class="code-block">
function getInstitutionTags(institutionTagVos) {
  if (!Array.isArray(institutionTagVos)) {
    return []
  }
  
  const tags = []
  institutionTagVos.forEach(tagVo => {
    if (tagVo.tagValue && Array.isArray(tagVo.tagValue)) {
      tagVo.tagValue.forEach(value => {
        tags.push(`${tagVo.bu || tagVo.tagType}-${value}`)
      })
    }
  })
  
  return tags
}
            </div>
        </div>
        
        <div class="section">
            <div class="section-title">3. 测试数据和结果</div>
            <button class="test-button" onclick="runTagTest()">运行标签处理测试</button>
            <button class="test-button" onclick="runEdgeCaseTest()">测试边界情况</button>
            <button class="test-button" onclick="generatePreview()">生成表格预览</button>
            
            <div id="testResult" class="result-area">
                点击上方按钮开始测试...
            </div>
        </div>
        
        <div class="section">
            <div class="section-title">4. 实际显示效果预览</div>
            <p>以下是在HCO列表中BU归属字段的显示效果：</p>
            
            <table>
                <thead>
                    <tr>
                        <th>机构ID</th>
                        <th>机构名称</th>
                        <th>机构类型</th>
                        <th>省份</th>
                        <th>城市</th>
                        <th>BU归属</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>12345</td>
                        <td><a href="#" style="color: #1976d2;">北京协和医院</a></td>
                        <td>综合医院</td>
                        <td>北京</td>
                        <td>北京</td>
                        <td>
                            <div class="tag-container">
                                <span class="tag">集团-广阔</span>
                                <span class="tag">华北BU-核心</span>
                                <span class="tag">华北BU-重点</span>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>67890</td>
                        <td><a href="#" style="color: #1976d2;">上海瑞金医院</a></td>
                        <td>综合医院</td>
                        <td>上海</td>
                        <td>上海</td>
                        <td>
                            <div class="tag-container">
                                <span class="tag">华东BU-优质机构</span>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="section">
            <div class="section-title">5. 代码更新状态</div>
            <table>
                <tr>
                    <td><strong>InstitutionTagVo接口定义</strong></td>
                    <td><span class="status-success">✓ 已更新</span></td>
                    <td>添加了bu字段，tagValues改为tagValue</td>
                </tr>
                <tr>
                    <td><strong>getInstitutionTags函数</strong></td>
                    <td><span class="status-success">✓ 已更新</span></td>
                    <td>适配新的数据结构，显示格式为"BU名称-标签值"</td>
                </tr>
                <tr>
                    <td><strong>表格渲染逻辑</strong></td>
                    <td><span class="status-success">✓ 已更新</span></td>
                    <td>使用el-tag组件显示标签</td>
                </tr>
            </table>
        </div>
    </div>

    <script src="test-tag-processing.js"></script>
    <script>
        function runTagTest() {
            const resultDiv = document.getElementById('testResult');
            resultDiv.innerHTML = '<h4>标签处理测试结果：</h4>';
            
            // 重定向console.log到页面
            const originalLog = console.log;
            const logs = [];
            console.log = function(...args) {
                logs.push(args.join(' '));
                originalLog.apply(console, arguments);
            };
            
            // 运行测试
            window.tagTest.testTagProcessing();
            
            // 恢复console.log
            console.log = originalLog;
            
            // 显示结果
            resultDiv.innerHTML += '<pre>' + logs.join('\n') + '</pre>';
        }
        
        function runEdgeCaseTest() {
            const resultDiv = document.getElementById('testResult');
            resultDiv.innerHTML = '<h4>边界情况测试结果：</h4>';
            
            const originalLog = console.log;
            const logs = [];
            console.log = function(...args) {
                logs.push(args.join(' '));
                originalLog.apply(console, arguments);
            };
            
            window.tagTest.testEdgeCases();
            
            console.log = originalLog;
            resultDiv.innerHTML += '<pre>' + logs.join('\n') + '</pre>';
        }
        
        function generatePreview() {
            const resultDiv = document.getElementById('testResult');
            resultDiv.innerHTML = '<h4>表格预览：</h4>';
            resultDiv.innerHTML += window.tagTest.generateHTMLTable();
        }
        
        // 页面加载时显示基本信息
        window.onload = function() {
            const resultDiv = document.getElementById('testResult');
            resultDiv.innerHTML = `
                <h4>测试工具已就绪</h4>
                <p>✅ 标签处理函数已加载</p>
                <p>✅ 测试数据已准备</p>
                <p>✅ 可以开始测试标签显示效果</p>
                <p><strong>点击上方按钮开始测试不同场景</strong></p>
            `;
        };
    </script>
</body>
</html>
