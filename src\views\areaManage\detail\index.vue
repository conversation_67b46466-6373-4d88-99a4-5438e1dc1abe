<template>
  <div class="p-2">
    <div class="card-block" style="margin-bottom: 0;">
      <div class="card-block-top">
        <el-row :gutter="10">
          <el-col :span="1.5" class="title-left">
            <img :src="district" />
            <div class="district-text">{{ state.detail.jurCode }}</div>
            <el-tag class="ml-2" type="info" v-if="state.detail.status==='1'">
              <div class="display-flex">
                <el-icon><RemoveFilled /> </el-icon>
                <span> 停用</span>
              </div>
            </el-tag>
            <el-tag class="ml-2" type="success" v-if="state.detail.status==='0'">
              <div class="display-flex">
                <el-icon><SuccessFilled /></el-icon>
                启用
              </div>
            </el-tag>
          </el-col>
          <div class="top-right-btn">
            <el-row>
              <el-button type="info" plain icon="EditPen" @click="editArea"> 编辑辖区</el-button>
              <el-button type="danger" plain icon="Delete" @click="handleDelete"> 删除辖区</el-button>
              <el-button type="primary" text plain @click="back()">
                <svg-icon icon-class="back" /><span style="padding-left: 8px;">返回</span></el-button
              >
            </el-row>
          </div>
        </el-row>
      </div>
      <el-card shadow="hover">
        <el-form ref="queryFormRef" :inline="false" label-width="68px" class="hover-form">
          <el-row>
            <el-col :span="4">
              <el-form-item label="部门：" prop="deptName">
                <div>{{ state.detail.deptName || '-'}}</div>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="岗位：" prop="postName">
                <div>{{state.detail.postName || '-'}}</div>
              </el-form-item>
            </el-col>
            <el-col :span="3">
              <el-form-item label="辖区类型：" label-width="90px" prop="jurType">
                <div>{{state.detail.jurType || '-'}}</div>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="辖区负责人：" label-width="110px" prop="leaderName">
                <div>{{state.detail.leaderName || '-'}}</div>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="工号：" label-width="80px" prop="leaderCode">
                <div>{{state.detail.leaderCode || '-'}}</div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="产品：" prop="productName">
                <div>{{ state.detail.productName && JSON.parse(state.detail.productName).map((d: ProductVo) => d.productName).join(',') || '-'}}</div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
    </div>
  </div>
  <div class="p-3">
    <div class="card-block" style="width: calc(100vw - 250px)">
      <el-row :gutter="16">
        <el-col :span="20">
          <el-card shadow="hover" class="card-block-content">
            <div class="card-block-top">
              <el-row :gutter="10">
                <el-col :span="1.5" class="title-left">
                  <div class="title-form">辖区下DTP药店</div>
                </el-col>
              </el-row>
              <div>
                <div style="display: flex;justify-content: space-between;width: 100%;">
                  <div>
                    <el-input
                      v-model="state.queryParams.drugName"
                      placeholder="请输入药店名称或药店编码"
                      @keyup.enter="getList('search')"
                      @input="getList('search')"
                      style="width:268px;margin-right:12px;"
                    >
                      <template #suffix>
                        <el-icon><Search /></el-icon>
                      </template>
                    </el-input>
                  </div>
                </div>
              </div>
            </div>
            <Table
              :tableColumn="tableColumn"
              :tableData="state.tableData"
              :isSelection="true"
              v-loading="state.loading"
              :border="false"
              :isNoData="true"
              @handleSelectionChange="handleSelectionChange"
              @handleLink="handleLink"
            >
            </Table>
            <pagination
              v-show="state.total > 10"
              layout="total,prev, pager, sizes"
              :page-sizes="[10, 20, 30, 50, 100]"
              :total="state.total"
              v-model:page="state.queryParams.pageNum"
              v-model:limit="state.queryParams.pageSize"
              @pagination="getList"
            />
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card shadow="hover" class="card-block-content">
            <div class="">
              <el-row :gutter="10">
                <el-col :span="1.5" class="title-left">
                  <div class="title-form">操作记录</div>
                </el-col>
              </el-row>
            </div>
            <div class="access-state-list">
              <el-divider style="margin: 0;" />
              <div v-for="(item, index) in state.operateLogs" :key="index">
                <div class="access-state-list-item">
                  <div class="list-state">
                    <el-tag class="ml-2" type="">{{ item.logType }}</el-tag>
                    <div>
                      {{ item.content }}
                    </div>
                  </div>
                  <div class="list-time">{{ item.logTime }}</div>
                </div>
              </div>
              <pagination
                v-show="state.operateLogTotal > 10"
                layout="prev, pager, next"
                :total="state.operateLogTotal"
                v-model:page="state.operateLogQuery.pageNum"
                v-model:limit="state.operateLogQuery.pageSize"
                :pager-count="3"
                :background="false"
                @pagination="getOperateLogs"
              />
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
  <AddJurisdiction v-model="state.editDialogVisible" :edit-data="state.detail" @success="handleEditSuccess" />
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, getCurrentInstance } from 'vue';
import { useRouter } from 'vue-router';
import district from '@/assets/images/districtManagement/<EMAIL>';
import { getJurisdictionDetail, getJurisdictionDrugList, getJurisdictionOperateLog, updateJurisdiction } from '../api';
import { ElMessage, ElMessageBox } from 'element-plus';
import { RemoveFilled, SuccessFilled } from '@element-plus/icons-vue';
import type { ComponentInternalInstance } from 'vue';
import AddJurisdiction from '../components/addJurisdiction.vue';

const ARER_TYPE = {
  sales: '销售',
  dtp: 'DTP专员'
};


interface ProductVo {
  productName: string;
  productCode: string;
  specCode: string;
  specName: string;
}

interface TableRow {
  drugName: string;
  drugCode: string;
  province: string;
  city: string;
  district: string;
  specCode: string;
  specName: string;
  status: string;
}

interface TableColumn {
  prop: string;
  label: string;
  width?: string;
}

const router = useRouter();
const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const state = reactive({
  loading: false,
  detail: {
    jurCode: '',
    deptName: '',
    postName: '',
    leaderCode: '',
    leaderName: '',
    jurType: '',
    productName: '',
    status: '',
    productVoList: [],
    productCodeList: []
  },
  total: 0,
  queryParams: {
    jurCode: '',
    drugName: '',
    pageNum: 1,
    pageSize: 10
  },
  tableData: [] as TableRow[],
  selectionList: [] as TableRow[],
  editDialogVisible: false,
  operateLogs: [] as any[],
  operateLogTotal: 0,
  operateLogQuery: {
    jurCode: '',
    pageNum: 1,
    pageSize: 10
  }
});

const tableColumn: TableColumn[] = [
  { prop: 'drugName', label: '药店名称' },
  { prop: 'drugCode', label: '药店编码' },
  { prop: 'province', label: '省份', width: '100' },
  { prop: 'city', label: '城市', width: '100' },
  { prop: 'district', label: '区县', width: '100' },
  { prop: 'specCode', label: '规格编码', width: '120' },
  { prop: 'specName', label: '规格名称', width: '180' },
  { prop: 'status', label: '状态', width: '100' }
];

const editArea = () => {
  state.editDialogVisible = true;
};

const back = () => {
  router.go(-1);
};

const handleDelete = async () => {
  try {
    await ElMessageBox.confirm('确认要删除该辖区吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    state.loading = true;
    const res = await updateJurisdiction({
      ...state.detail,
      operateType: 'delete'
    });

    if (res.code === 200) {
      ElMessage.success('删除成功');
      router.go(-1);
    } else {
      ElMessage.error(res.msg || '删除失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to delete jurisdiction:', error);
      ElMessage.error('删除失败');
    }
  } finally {
    state.loading = false;
  }
};

const addIns = () => {
  // TODO: Implement add functionality
};

const handleSelectionChange = (val: TableRow[]) => {
  state.selectionList = val;
};

const handleLink = (row: TableRow) => {
  // TODO: Implement link functionality
};

const getDetail = async () => {
  const res = await getJurisdictionDetail(state.queryParams.jurCode);
  if (res?.code === 200) {
    state.detail = res.data;
    state.detail.productCodeList = res.data.productVoList.map((d:any) => d.productCode)
  }
};

const getList = async (type?: string) => {
  if (type === 'search') {
    state.queryParams.pageNum = 1;
  }
  state.loading = true;
  const res = await getJurisdictionDrugList({
    ...state.queryParams,
    jurCode: state.queryParams.jurCode
  });
  state.loading = false;
  if (res?.code === 200) {
    state.tableData = res.data.rows || [];
    state.total = res.data.total || 0;
  }
};

const adjustment = () => {
  if (!state.selectionList.length) {
    return proxy?.$modal.msgError('请选择机构');
  }
  const flag = state.selectionList.some((item) => item.status === '1');
  if (flag) {
    return proxy?.$modal.msgError('停用的机构不可转移，请重新选择');
  }
  // TODO: Implement adjustment functionality
};

const getOperateLogs = async () => {
  const res = await getJurisdictionOperateLog({
    jurCode: state.queryParams.jurCode,
    pageNum: state.operateLogQuery.pageNum,
    pageSize: state.operateLogQuery.pageSize
  });
  if (res?.code === 200) {
    state.operateLogs = res.data.list || [];
    state.operateLogTotal = res.data.total || 0;
  }
};

const handleEditSuccess = () => {
  state.editDialogVisible = false;
  getDetail();
  getOperateLogs();
};

onMounted(() => {
  state.queryParams.jurCode = router.currentRoute.value.query.jurCode as string;
  state.operateLogQuery.jurCode = state.queryParams.jurCode;
  getDetail();
  getList('');
  getOperateLogs();
});
</script>

<style lang="scss" scoped>
.card-block-top {
  margin-bottom: 19px;

  .title-left {
    display: flex;
    justify-content: center;
    align-items: center;
    img {
      width: 64px;
      height: 64px;
    }
    .district-text {
      font-size: 24px;
      font-weight: 500;
      color: #1d212b;
      margin: 0 12px 0 15px;
    }
  }
  .top-right-btn {
    display: flex;
    align-items: center;
  }
}

.p-3 {
  .card-block {
    background: var(--neutral-color-bg-bg-1, #f3f4f5);
    padding: 0;
    .card-block-content {
      padding: 16px;
    }
  }
}

.query-form-tag {
  ::v-deep {
    .el-tag {
      padding: 0 8px;
      margin: 0;
      border-radius: 2px;
      color: #606266;
      font-size: 14px !important;
      background-color: #fff;
      margin: 2px;
      border: 1px solid var(--neutral-color-3, #e3e4e9);
    }
    .el-select .el-select__tags-text {
      text-align: center;
    }
  }
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.access-state-list {
  padding-top: 16px;
  border-radius: 4px;
  flex: 1;
  .access-state-list-item {
    padding-top: 16px;
    border-bottom: 1px solid var(--neutral-color-line-1, #f4f5f7);
    .list-state {
      display: flex;
      color: var(--neutral-color-2, #4e595e);
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      .tag {
        margin-right: 8px;
      }
    }
  }
  .list-time {
    color: var(--neutral-color-3, #869199);
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    text-align: right;
    margin-top: 8px;
    padding-bottom: 8px;
  }
}

::v-deep {
  .el-tag {
    padding: 0 8px;
    margin: 0;
    border: none !important;
    border-radius: 2px;
    font-size: 14px !important;
    margin-right: 8px;
  }
  .pagination-container {
    display: flex;
    align-items: center;
    margin-top: 8px;
    padding-right: 0 !important;
    float: right;
  }
  .el-pagination {
    width: 100%;
  }
}

.card-block-content {
  height: 100%;
  .el-card__body {
    height: calc(100% - 55px);
    overflow: auto;
  }
}

.access-state-list {
  padding-top: 16px;
  border-radius: 4px;
  flex: 1;
  height: calc(100% - 55px);
  overflow: auto;
  .access-state-list-item {
    padding-top: 16px;
    border-bottom: 1px solid var(--neutral-color-line-1, #f4f5f7);
    .list-state {
      display: flex;
      color: var(--neutral-color-2, #4e595e);
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      .tag {
        margin-right: 8px;
      }
    }
  }
  .list-time {
    color: var(--neutral-color-3, #869199);
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    text-align: right;
    margin-top: 8px;
    padding-bottom: 8px;
  }
}
</style>
