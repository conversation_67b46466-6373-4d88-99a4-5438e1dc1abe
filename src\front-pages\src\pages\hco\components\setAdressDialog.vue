<template>
  <el-drawer destroy-on-close :direction='"rtl"' custom-class="my-ins-dialog" @close="emits('close')" v-model="props.dialogVisible" width="668">
    <template #header>
      <div class="dialog-title">定位校准</div>
    </template>
    <el-divider style="margin: 0;" />
    <Add ref="add" @close="emits('close')" />
  </el-drawer>
</template>

<script setup>
import { provide } from 'vue';

import Add from './add.vue';
const emits = defineEmits(['close']);
const add = ref(null);
const getInfo = ref({})
const props = defineProps({
  dialogVisible: {
    type: Boolean,
    required: false
  }
});
const resetFields = () => {
  add.value.resetFields();
};
    provide('info',getInfo)

const setInfo = (info)=>{
  getInfo.value=info
  console.log(getInfo.value,"----getInfo")

}

defineExpose({
  resetFields,
  setInfo
});
</script>

<style lang="scss">
.my-ins-dialog .el-drawer__headerbtn {
  margin-top: -9px;
}
.my-ins-dialog .el-drawer__header {
  margin-bottom:0;
  padding-top: 12px;
  padding-bottom: 0;
}
.my-ins-dialog .el-drawer__body {
  padding: 12px 0 !important;
}
.dialog-title {
  text-align: center;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: #1d212b;
}
</style>
