<template>
  <div class="approve-text">
    <div style="margin-top: 16px;margin-left: 16px;">
      <el-timeline style="max-width: 600px">
        <el-timeline-item v-for="(item, index) in flowTaskHistoryVo" :key="index">
          <template #default>
            <div class="right w-[100%] pr-[16px] mt-[4px]">
              <div class="text-[#1D212B] text-[16px] flex" style="align-items: center;justify-content: space-between;">
                <div class="flex" style="align-items: center;">
                  <div>{{ item.taskName }}</div>
                  <!--            审批类型 = 或签/会签/抄送 展示-->
                  <div class="mx-[8px] text-[#E5E6EB] text-[12px]" v-if="item.taskType === 2 || item.performType === 2 || item.performType === 3">
                    |
                  </div>
                  <div class="text-[12px] text-[#86909C]" v-if="item.performType === 2 ">会签</div>
                  <div class="text-[12px] text-[#86909C]" v-if="item.performType === 3 ">或签</div>
                  <div class="text-[12px] text-[#86909C]" v-if="item.performType === 2 || item.performType === 3 || item.taskType === 2">
                    {{item?.flowTaskActorHistoryVos?.length}}人
                  </div>
                </div>
                <div
                  v-if="index != 0 && item.taskType !== 2"
                  class="timeLine-task-tag"
                  :style="{ color: task(item.taskState).textColor, borderColor: task(item.taskState).bgcColor }"
                >
                  {{
                    task(item.taskState).text }}
                </div>
              </div>
              <div class="flex gap-[8px] flex-wrap mt-[2px]">
                <div
                  class="flex items-center justify-between gap-[8px] min-w-[100%]"
                  v-for="actor in item?.flowTaskActorHistoryVos || []"
                  :key="actor.id"
                >
                  <div>
                    <div class="flex gap-[2px] items-center rounded-[50px] bg-[#F3F4F5] p-[2px]">
                      <div class="nameAvatar">{{ actor.actorName?.slice(-1) }}</div>
                      <div class="text-[14px]">{{actor.actorName?.length > 4 ? `${actor.actorName.slice(0, 3)}...` : actor.actorName}}</div>
                      <div class="text-[14px] pr-[6px] text-[#86909C]">({{ actor.creator }})</div>
                    </div>
                    <div class="text-[#86909C] text-[12px] mt-[4px]">{{ actor.updateTime }}</div>
                    <div class="text-[#86909C] text-[12px] mt-[4px]">
                      {{ getOpinion(actor) }}
                    </div>
                  </div>
                  <div class="text-[14px] text-[#1D212B] flex items-center gap-[2px]" v-if="actor.opinionStatus == 1">
                    <div class="w-[6px] h-[6px] rounded-[50%] bg-[#00B578]"></div>
                    已通过
                  </div>
                  <div class="text-[14px] text-[#1D212B] flex items-center gap-[2px]" v-else-if="actor.opinionStatus == 2">
                    <div class="w-[6px] h-[6px] rounded-[50%] bg-[#F32F29]"></div>
                    已拒绝
                  </div>
                  <div class="text-[14px] text-[#1D212B]" v-else-if="actor.opinionStatus == 3">已撤销</div>
                </div>
                <!--          <div> {{ item?.flowTaskActorHistoryVos.map(d => d.actorName).join(',') }}</div>-->
              </div>
              <div class="text-[#1D212B] text-[14px] mt-[4px]"></div>
              <!--        <div class="text-[#86909C] text-[12px] mt-[4px]">-->
              <!--          {{ item?.flowTaskActorHistoryVos?.[item?.flowTaskActorHistoryVos?.length - 1]?.updateTime }}-->
              <!--        </div>-->
            </div>
          </template>
          <template #dot>
            <div class="dot">
              <img :src="getNodeIcon(item)" alt="" class="avatar" />
              <!--              <div class="nameAvatar" v-else>{{ item.name.slice(-1) }}</div>-->
              <!--              <img :src="reject" v-if="item.taskState == 7 || item.taskState == 3" alt="" class="dot-bottom" />-->
              <!--              <img :src="pass" v-else alt="" class="dot-bottom" />-->
            </div>
          </template>
        </el-timeline-item>
      </el-timeline>
    </div>
  </div>
</template>
<script setup name="ApproveInfo">
import startNodeIcon from '@/bpm/src/assets/images/approce_status_done.png';
import pendingIcon from '@/bpm/src/assets/images/approve_status_pending.png';
import refuseIcon from '@/bpm/src/assets/images/approce_status_refuse.png';
import cancleIcon from '@/bpm/src/assets/images/approve_status_cancle.png';
import chaosongIcon from '@/bpm/src/assets/images/chaosong.png';
import { ref } from 'vue';
import {taskInstanceActionApi} from "@/bpm/src/api/flow";

const props = defineProps(['instanceId'])

const formApi = ref(null);
const flowTaskHistoryVo = ref([])
const todoTaskActorVo = ref(null)

const getAction = async () => {
  const res = await taskInstanceActionApi({
    instanceId: props.instanceId
  });

  flowTaskHistoryVo.value = res?.data?.flowTaskHistoryVo || [];
  todoTaskActorVo.value = res?.data?.todoTaskActorVo;
};

const getNodeIcon = (item) => {
  console.log("item", item)
  if (item.performType == 0) {
    return startNodeIcon
  }else if (item.performType  == 9) {
    return chaosongIcon
  }else if (item.taskState == 1) {
    // taskState字段（1.审批中 2，完成 3，拒绝 4，撤销审批）
    return pendingIcon
  }else if (item.taskState == 2) {
    return startNodeIcon
  }else if (item.taskState == 3) {
    return refuseIcon
  }else if (item.taskState == 4) {
    return cancleIcon
  }

  return startNodeIcon

}

const task = (taskState) => {
  if (taskState == 2) {
    return {
      text: '审批通过',
      textColor: '#00B578',
      bgcColor: '#A6E5D0'
    }
  }
  if (taskState == 3) {
    return {
      text: '审批拒绝',
      textColor: '#F32F29',
      bgcColor: '#FAACA9'
    }
  }
  if (taskState == 4) {
    return {
      text: '审批撤销',
      textColor: '#4E595E',
      bgcColor: '#C6CAD1'
    }
  }

  return {
    text: '审批中',
    textColor: '#F77234',
    bgcColor: '#FCC59F'
  }
}

const getOpinion = (item) => {
  if(!item.opinion) return ''
  if (item.opinionStatus == 1) {
    return `通过原因：${item.opinion}`
  }
  if (item.opinionStatus == 2) {
    return `拒绝原因：${item.opinion}`
  }
  if (item.opinionStatus == 3) {
    return `撤销原因：${item.opinion}`
  }
}


onMounted(() => {
  getAction()
})
</script>
<style lang="scss" scoped>
.approve-text {
  overflow: hidden;
  overflow-y: scroll;

  .approve-info-content {

    .item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      .label {
        width: 30%;
        color: #86909C;
      }
      .result {
        color: #1D212B;
      }
    }
  }
  .avatar {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    vertical-align: middle;
  }
  .nameAvatar {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background: #92a8f8;
    color: #fff;
    line-height: 28px;
    text-align: center;
    min-width: 28px;

  }
  .dot {
    position: relative;
    .dot-bottom {
      position: absolute;
      width: 10px;
      height: 10px;
      bottom: 0;
      right: 0;

    }

  }
  :deep(.el-timeline-item__dot) {
    left:-7px
  }
  :deep(.el-timeline-item__tail) {
    top: 39%;
    border-left: 2px solid #92a8f8;
    height: 48px;
  }
}

.approve-text::-webkit-scrollbar-track {
  background-color: #fff;
}

.nameAvatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #92a8f8;
  color: #fff;
  line-height: 24px;
  text-align: center;
  min-width: 24px;
  margin-right: 4px;
  font-size: 12px;
}
.approve-detail-line {
  border-radius: 0 0 8px 8px;



}

.timeLine-task-tag {

  padding: 2px 4px;
  font-size: 12px;
  border-radius: 2px;

  border: 1px solid;
  margin-left: 12px;
}
</style>
