<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>查询条件持久化测试</title>
    <style>
      body {
          font-family: Arial, sans-serif;
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
      }
      .test-section {
          margin: 20px 0;
          padding: 15px;
          border: 1px solid #ddd;
          border-radius: 5px;
      }
      .test-section h3 {
          margin-top: 0;
          color: #333;
      }
      button {
          margin: 5px;
          padding: 8px 16px;
          background: #007bff;
          color: white;
          border: none;
          border-radius: 4px;
          cursor: pointer;
      }
      button:hover {
          background: #0056b3;
      }
      .result {
          margin: 10px 0;
          padding: 10px;
          background: #f8f9fa;
          border-radius: 4px;
          white-space: pre-wrap;
      }
      .success {
          background: #d4edda;
          color: #155724;
      }
      .error {
          background: #f8d7da;
          color: #721c24;
      }
    </style>
  </head>
  <body>
    <h1>HCO 查询条件持久化功能测试</h1>

    <div class="test-section">
      <h3>1. 保存查询条件测试</h3>
      <button onclick="testSaveQuery()">保存测试查询条件</button>
      <div id="saveResult" class="result"></div>
    </div>

    <div class="test-section">
      <h3>2. 恢复查询条件测试</h3>
      <button onclick="testRestoreQuery()">恢复查询条件</button>
      <div id="restoreResult" class="result"></div>
    </div>

    <div class="test-section">
      <h3>3. 清除查询条件测试</h3>
      <button onclick="testClearQuery()">清除查询条件</button>
      <div id="clearResult" class="result"></div>
    </div>

    <div class="test-section">
      <h3>4. 数据过期测试</h3>
      <button onclick="testExpiredData()">测试过期数据</button>
      <div id="expiredResult" class="result"></div>
    </div>

    <div class="test-section">
      <h3>5. 分页信息持久化测试</h3>
      <button onclick="testPaginationPersistence()">测试分页持久化</button>
      <div id="paginationResult" class="result"></div>
    </div>

    <div class="test-section">
      <h3>6. 路由跳转清除测试</h3>
      <button onclick="testRouteNavigationClear()">模拟非详情页跳转清除</button>
      <div id="routeResult" class="result"></div>
    </div>

    <div class="test-section">
      <h3>7. localStorage 状态</h3>
      <button onclick="showLocalStorageStatus()">查看 localStorage 状态</button>
      <div id="storageStatus" class="result"></div>
    </div>

    <script>
      // 模拟查询条件持久化功能
      const HCO_QUERY_STORAGE_KEY = 'hco_query_conditions';

      // 模拟保存查询条件
      function saveQueryConditions(basicQuery, advancedFilters, queryParams) {
          try {
              const queryState = {
                  basicQuery,
                  advancedFilters,
                  queryParams,
                  timestamp: Date.now()
              };
              localStorage.setItem(HCO_QUERY_STORAGE_KEY, JSON.stringify(queryState));
              return true;
          } catch (error) {
              console.error('保存查询条件失败:', error);
              return false;
          }
      }

      // 模拟恢复查询条件
      function restoreQueryConditions() {
          try {
              const stored = localStorage.getItem(HCO_QUERY_STORAGE_KEY);
              if (stored) {
                  const queryState = JSON.parse(stored);
                  // 检查数据是否过期（24小时）
                  const isExpired = Date.now() - queryState.timestamp > 24 * 60 * 60 * 1000;
                  if (!isExpired) {
                      return queryState;
                  } else {
                      // 清除过期数据
                      localStorage.removeItem(HCO_QUERY_STORAGE_KEY);
                      return null;
                  }
              }
          } catch (error) {
              console.error('恢复查询条件失败:', error);
              localStorage.removeItem(HCO_QUERY_STORAGE_KEY);
          }
          return null;
      }

      // 模拟清除查询条件
      function clearQueryConditions() {
          try {
              localStorage.removeItem(HCO_QUERY_STORAGE_KEY);
              return true;
          } catch (error) {
              console.error('清除查询条件失败:', error);
              return false;
          }
      }

      // 测试保存查询条件
      function testSaveQuery() {
          const basicQuery = {
              bu: ['BU1', 'BU2'],
              type: ['医院', '诊所'],
              orgName: '北京医院'
          };

          const advancedFilters = [
              { id: 1, left: 'provinceName', op: 'eq', right: '北京' },
              { id: 2, left: 'insType', op: 'contains', right: '三甲' }
          ];

          const queryParams = {
              conditions: [],
              logic: 'and',
              pageSize: 50,  // 测试非默认分页大小
              pageNum: 3     // 测试非第一页
          };

          const success = saveQueryConditions(basicQuery, advancedFilters, queryParams);
          const resultDiv = document.getElementById('saveResult');

          if (success) {
              resultDiv.className = 'result success';
              resultDiv.textContent = '✓ 查询条件保存成功！\n' +
                  JSON.stringify({ basicQuery, advancedFilters, queryParams }, null, 2);
          } else {
              resultDiv.className = 'result error';
              resultDiv.textContent = '✗ 查询条件保存失败！';
          }
      }

      // 测试恢复查询条件
      function testRestoreQuery() {
          const restored = restoreQueryConditions();
          const resultDiv = document.getElementById('restoreResult');

          if (restored) {
              resultDiv.className = 'result success';
              resultDiv.textContent = '✓ 查询条件恢复成功！\n' +
                  JSON.stringify(restored, null, 2);
          } else {
              resultDiv.className = 'result error';
              resultDiv.textContent = '✗ 没有找到保存的查询条件或数据已过期！';
          }
      }

      // 测试清除查询条件
      function testClearQuery() {
          const success = clearQueryConditions();
          const resultDiv = document.getElementById('clearResult');

          if (success) {
              resultDiv.className = 'result success';
              resultDiv.textContent = '✓ 查询条件清除成功！';
          } else {
              resultDiv.className = 'result error';
              resultDiv.textContent = '✗ 查询条件清除失败！';
          }
      }

      // 测试过期数据
      function testExpiredData() {
          // 保存一个过期的数据（时间戳设置为25小时前）
          const expiredTimestamp = Date.now() - (25 * 60 * 60 * 1000);
          const expiredData = {
              basicQuery: { bu: '', type: '', orgName: '过期数据' },
              advancedFilters: [],
              queryParams: { conditions: [], logic: 'and', pageSize: 20, pageNum: 1 },
              timestamp: expiredTimestamp
          };

          localStorage.setItem(HCO_QUERY_STORAGE_KEY, JSON.stringify(expiredData));

          // 尝试恢复过期数据
          const restored = restoreQueryConditions();
          const resultDiv = document.getElementById('expiredResult');

          if (restored === null) {
              resultDiv.className = 'result success';
              resultDiv.textContent = '✓ 过期数据处理正确！过期数据已被自动清除。';
          } else {
              resultDiv.className = 'result error';
              resultDiv.textContent = '✗ 过期数据处理错误！过期数据没有被清除。';
          }
      }

      // 显示 localStorage 状态
      function showLocalStorageStatus() {
          const resultDiv = document.getElementById('storageStatus');
          const stored = localStorage.getItem(HCO_QUERY_STORAGE_KEY);

          if (stored) {
              try {
                  const data = JSON.parse(stored);
                  const age = Math.round((Date.now() - data.timestamp) / 1000 / 60); // 分钟
                  resultDiv.className = 'result';
                  resultDiv.textContent = `localStorage 中存在查询条件数据：\n` +
                      `保存时间：${new Date(data.timestamp).toLocaleString()}\n` +
                      `数据年龄：${age} 分钟\n` +
                      `数据内容：\n${JSON.stringify(data, null, 2)}`;
              } catch (error) {
                  resultDiv.className = 'result error';
                  resultDiv.textContent = '✗ localStorage 中的数据格式错误！';
              }
          } else {
              resultDiv.className = 'result';
              resultDiv.textContent = 'localStorage 中没有查询条件数据。';
          }
      }

      // 测试分页信息持久化
      function testPaginationPersistence() {
          const resultDiv = document.getElementById('paginationResult');

          // 第一步：保存包含分页信息的查询条件
          const basicQuery = {
              bu: ['测试BU'],
              type: ['医院'],
              orgName: '测试医院'
          };

          const advancedFilters = [
              { id: 1, left: 'provinceName', op: 'eq', right: '北京' }
          ];

          const queryParams = {
              conditions: [],
              logic: 'and',
              pageSize: 50,  // 非默认分页大小
              pageNum: 5     // 非第一页
          };

          // 保存查询条件
          const saveSuccess = saveQueryConditions(basicQuery, advancedFilters, queryParams);

          if (!saveSuccess) {
              resultDiv.className = 'result error';
              resultDiv.textContent = '✗ 分页信息保存失败！';
              return;
          }

          // 第二步：恢复查询条件并检查分页信息
          const restored = restoreQueryConditions();

          if (!restored) {
              resultDiv.className = 'result error';
              resultDiv.textContent = '✗ 分页信息恢复失败！没有找到保存的数据。';
              return;
          }

          // 第三步：验证分页信息是否正确恢复
          const restoredPageSize = restored.queryParams.pageSize;
          const restoredPageNum = restored.queryParams.pageNum;

          if (restoredPageSize === 50 && restoredPageNum === 5) {
              resultDiv.className = 'result success';
              resultDiv.textContent = '✓ 分页信息持久化测试成功！\n' +
                  `原始分页信息：第 ${queryParams.pageNum} 页，每页 ${queryParams.pageSize} 条\n` +
                  `恢复分页信息：第 ${restoredPageNum} 页，每页 ${restoredPageSize} 条\n` +
                  '分页信息完全匹配！\n\n' +
                  '✅ 测试场景：\n' +
                  '1. 用户翻到第5页，设置每页50条\n' +
                  '2. 点击机构详情跳转\n' +
                  '3. 返回列表页面\n' +
                  '4. 自动恢复到第5页，每页50条';
          } else {
              resultDiv.className = 'result error';
              resultDiv.textContent = '✗ 分页信息持久化测试失败！\n' +
                  `原始分页信息：第 ${queryParams.pageNum} 页，每页 ${queryParams.pageSize} 条\n` +
                  `恢复分页信息：第 ${restoredPageNum} 页，每页 ${restoredPageSize} 条\n` +
                  '分页信息不匹配！\n\n' +
                  '❌ 问题：分页信息没有正确恢复\n' +
                  '请检查 handleQuery() 函数的 resetPage 参数设置';
          }
      }

      // 测试路由跳转清除功能
      function testRouteNavigationClear() {
          const resultDiv = document.getElementById('routeResult');

          // 第一步：保存一些测试数据
          const basicQuery = {
              bu: ['测试BU'],
              type: ['医院'],
              orgName: '测试医院'
          };

          const advancedFilters = [
              { id: 1, left: 'provinceName', op: 'eq', right: '北京' }
          ];

          const queryParams = {
              conditions: [],
              logic: 'and',
              pageSize: 50,
              pageNum: 3
          };

          // 保存数据
          const saveSuccess = saveQueryConditions(basicQuery, advancedFilters, queryParams);

          if (!saveSuccess) {
              resultDiv.className = 'result error';
              resultDiv.textContent = '✗ 测试数据保存失败！';
              return;
          }

          // 第二步：验证数据已保存
          const beforeClear = restoreQueryConditions();
          if (!beforeClear) {
              resultDiv.className = 'result error';
              resultDiv.textContent = '✗ 测试数据保存后无法读取！';
              return;
          }

          // 第三步：模拟页面卸载清除
          clearQueryConditions();

          // 第四步：验证数据已清除
          const afterClear = restoreQueryConditions();

          if (afterClear === null) {
              resultDiv.className = 'result success';
              resultDiv.textContent = '✓ 路由跳转清除测试成功！\n\n' +
                  '测试流程：\n' +
                  '1. ✅ 保存测试查询条件\n' +
                  '2. ✅ 验证数据已保存\n' +
                  '3. ✅ 模拟非详情页跳转清除\n' +
                  '4. ✅ 验证数据已完全清除\n\n' +
                  '💡 实际使用中：\n' +
                  '- 用户点击左侧菜单跳转到其他页面 → 清除数据\n' +
                  '- 用户点击机构详情跳转 → 保持数据\n' +
                  '- 系统智能区分跳转类型';
          } else {
              resultDiv.className = 'result error';
              resultDiv.textContent = '✗ 路由跳转清除测试失败！\n\n' +
                  '问题：数据没有被正确清除\n' +
                  '请检查 onBeforeRouteLeave 路由守卫中的清除逻辑';
          }
      }

      // 页面加载时显示状态
      window.onload = function() {
          showLocalStorageStatus();
      };
    </script>
  </body>
</html>
