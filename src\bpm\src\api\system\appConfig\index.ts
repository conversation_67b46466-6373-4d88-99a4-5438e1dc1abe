import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { appConfigVO, appConfigForm, appConfigQuery } from '@/api/system/appConfig/types';

/**
 * 查询第三方应用列表
 * @param query
 * @returns {*}
 */

export const listAppConfig = (query?: appConfigQuery): AxiosPromise<appConfigVO[]> => {
  return request({
    url: '/plt/social/app/list',
    method: 'get',
    params: query
  });
};

/**
 * 新增第三方应用
 * @param data
 */
export const addAppConfig = (data: appConfigForm) => {
  return request({
    url: '/plt/social/app',
    method: 'post',
    data: data
  });
};

/**
 * 修改第三方应用
 * @param data
 */
export const updateAppConfig = (data: appConfigForm) => {
  return request({
    url: '/plt/social/app',
    method: 'put',
    data: data
  });
};

/**
 * 删除第三方应用
 * @param id
 */
export const delAppConfig = (id: string | number | Array<string | number>) => {
  return request({
    url: '/plt/social/app/' + id,
    method: 'delete'
  });
};
