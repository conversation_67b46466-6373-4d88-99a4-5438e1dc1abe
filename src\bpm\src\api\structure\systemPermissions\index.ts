import request from '@/utils/request';
import { AxiosPromise } from 'axios';
// 获取部门下权限范围
export const authorityPage = (query: any): AxiosPromise<any> => {
  return request({
    url: '/ped/authority/page',
    method: 'get',
    params: query
  });
};
export const authorityInsert = (data: any): AxiosPromise<any> => {
  return request({
    url: '/ped/authority/insert',
    method: 'post',
    data
  });
};
