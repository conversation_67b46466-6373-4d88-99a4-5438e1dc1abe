import request from '@/utils/request';

/**
 * 问卷管理相关接口
 */

// 问卷对象类型枚举
export enum QuestionnaireTargetEnum {
  DOCTOR = 'doctor',
  INSTITUTION = 'institution',
  EMPLOYEE = 'employee'
}

// 问卷状态枚举
export enum QuestionnaireStatusEnum {
  DRAFT = 'draft', // 待发布
  PUBLISHED = 'published', // 已发布
  CLOSED = 'closed' // 已关闭
}

// 问卷操作记录类型枚举
export enum OperationTypeEnum {
  CREATE = 'create', // 创建
  SAVE = 'save', // 保存（更新）
  PUBLISH = 'publish', // 发布
  COPY = 'copy', // 复制
  CLOSE = 'close', // 关闭
  DELETE = 'delete' // 删除
}

// 问卷列表查询参数
export interface QuestionnaireListParams {
  name?: string; // 问卷名称
  targetType?: QuestionnaireTargetEnum; // 对象类型
  productId?: number | string; // 关联产品ID
  buId?: number | string; // BU ID
  status?: QuestionnaireStatusEnum; // 问卷状态
  pageNum: number; // 页码
  pageSize: number; // 每页条数
}

// 问卷详情
export interface QuestionnaireDetail {
  id: string | number; // 问卷ID
  name: string; // 问卷名称
  targetType: QuestionnaireTargetEnum; // 对象类型
  productId: number | string; // 关联产品ID
  productName: string; // 关联产品名称
  status: QuestionnaireStatusEnum; // 状态
  buId: number | string; // 所属BU ID
  buName: string; // 所属BU名称
  content: any; // 问卷内容(JSON)
  createdBy: string; // 创建人
  createdTime: string; // 创建时间
  lastOperator: string; // 最后操作人
  lastOperateTime: string; // 最后操作时间
  isDeleted: boolean; // 是否已删除
}

// 问卷操作记录
export interface OperationRecord {
  id: string | number; // 记录ID
  questionnaireId: string | number; // 问卷ID
  questionnaireName: string; // 问卷名称
  operationType: OperationTypeEnum; // 操作类型
  operator: string; // 操作人
  operateTime: string; // 操作时间
  remark: string; // 备注
}

// 问卷答卷数据
export interface ResponseData {
  id: string | number; // 答卷ID
  questionnaireId: string | number; // 问卷ID
  respondent: string; // 答卷人
  responseTime: string; // 答卷时间
  answers: any; // 答卷内容(JSON)
}

/**
 * 获取问卷列表
 * @param params 查询参数
 * @returns 问卷列表
 */
export function getQuestionnaireList(params: QuestionnaireListParams) {
  return request({
    url: '/business/questionnaire/list',
    method: 'get',
    params
  });
}

/**
 * 获取问卷详情
 * @param id 问卷ID
 * @returns 问卷详情
 */
export function getQuestionnaireDetail(id: string | number) {
  return request({
    url: `/business/questionnaire/${id}`,
    method: 'get'
  });
}

/**
 * 创建问卷
 * @param data 问卷数据
 * @returns 创建结果
 */
export function createQuestionnaire(data: any) {
  return request({
    url: '/business/questionnaire',
    method: 'post',
    data
  });
}

/**
 * 更新问卷
 * @param id 问卷ID
 * @param data 问卷数据
 * @returns 更新结果
 */
export function updateQuestionnaire(id: string | number, data: any) {
  return request({
    url: `/business/questionnaire/${id}`,
    method: 'put',
    data
  });
}

/**
 * 复制问卷
 * @param id 问卷ID
 * @returns 复制结果
 */
export function copyQuestionnaire(id: string | number) {
  return request({
    url: `/business/questionnaire/${id}/copy`,
    method: 'post'
  });
}

/**
 * 发布问卷
 * @param id 问卷ID
 * @returns 发布结果
 */
export function publishQuestionnaire(id: string | number) {
  return request({
    url: `/business/questionnaire/${id}/publish`,
    method: 'put'
  });
}

/**
 * 关闭问卷
 * @param id 问卷ID
 * @returns 关闭结果
 */
export function closeQuestionnaire(id: string | number) {
  return request({
    url: `/business/questionnaire/${id}/close`,
    method: 'put'
  });
}

/**
 * 删除问卷
 * @param id 问卷ID
 * @returns 删除结果
 */
export function deleteQuestionnaire(id: string | number) {
  return request({
    url: `/business/questionnaire/${id}`,
    method: 'delete'
  });
}

/**
 * 获取问卷操作记录
 * @param id 问卷ID
 * @returns 操作记录列表
 */
export function getOperationRecords(id: string | number) {
  return request({
    url: `/business/questionnaire/${id}/operations`,
    method: 'get'
  });
}

/**
 * 获取问卷答卷数据列表
 * @param id 问卷ID
 * @param params 查询参数
 * @returns 答卷数据列表
 */
export function getResponseDataList(id: string | number, params: any) {
  return request({
    url: `/business/questionnaire/${id}/responses`,
    method: 'get',
    params
  });
}

/**
 * 导出问卷答卷数据
 * @param id 问卷ID
 * @returns 导出文件流
 */
export function exportResponseData(id: string | number) {
  return request({
    url: `/business/questionnaire/${id}/responses/export`,
    method: 'get',
    responseType: 'blob'
  });
}

/**
 * 获取产品列表
 * @returns 产品列表
 */
export function getProductList() {
  return request({
    url: '/business/product/list',
    method: 'get'
  });
}

/**
 * 获取BU列表
 * @returns BU列表
 */
export function getBuList() {
  return request({
    url: '/business/bu/list',
    method: 'get'
  });
}
