import request from '@/utils/request';
import { AxiosPromise } from 'axios';
// 获取部门下权限范围
export const getpitListApi = (query: any): AxiosPromise<any> => {
  return request({
    url: '/hr/node_group/list',
    method: 'get',
    params: query
  });
};
export const delApi = (query: any, url: string): AxiosPromise<any> => {
  return request({
    url,
    method: 'get',
    params: query
  });
};

//导入接口
export const importApi = (data: any, id: stying) => {
  return request({
    url: `hr/node_group/import?opType=${id}`,
    method: 'post',
    data: data
  });
};
