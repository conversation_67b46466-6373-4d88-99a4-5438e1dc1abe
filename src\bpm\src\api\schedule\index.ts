import request from '@/utils/request';
import { AxiosPromise } from 'axios';

export const calendarEventList = (data: any) => {
  return request({
    url: '/cal/calendar/event/list',
    method: 'post',
    data: data
  });
};

export const calendarGetDot = (query: any) => {
  return request({
    url: `/cal/calendar/get/dot`,
    method: 'get',
    params: query
  });
};

export const calendarDeptUserList = (query: any) => {
  return request({
    url: `/plt/dept/user/list`,
    method: 'get',
    params: query
  });
};
export const crmVisitList = (query: any) => {
  return request({
    url: `/cal/crm/visit/list`,
    method: 'get',
    params: query
  });
};
export const crmAssistList = (query: any) => {
  return request({
    url: `/cal/crm/assist/list`,
    method: 'get',
    params: query
  });
};
export const dataInsightList = (query: any) => {
  return request({
    url: `/cal/sale/list`,
    method: 'get',
    params: query
  });
};

export const superviseList = (query: any) => {
  return request({
    url: `/cal/task/list`,
    method: 'get',
    params: query
  });
};

// 会议辅导平台
export const meetingList = (query: any) => {
  return request({
    url: `/cal/meeting/list`,
    method: 'get',
    params: query
  });
};
