{"version": 3, "sources": ["../../node_modules/@form-create/utils/lib/modify.js", "../../node_modules/@form-create/utils/lib/deepextend.js", "../../node_modules/@form-create/utils/lib/console.js", "../../node_modules/@form-create/utils/lib/json.js"], "sourcesContent": ["\nexport function $set(target, field, value) {\n    target[field] = value;\n}\n\nexport function $del(target, field) {\n    delete target[field];\n}\n", "import {$set} from './modify';\nimport is from './type';\n\nexport default function deepExtend(origin, target = {}, mode) {\n    let isArr = false;\n    for (let key in target) {\n        if (Object.prototype.hasOwnProperty.call(target, key)) {\n            let clone = target[key];\n            if ((isArr = Array.isArray(clone)) || is.Object(clone)) {\n                let nst = origin[key] === undefined;\n                if (isArr) {\n                    isArr = false;\n                    nst && $set(origin, key, []);\n                } else if (clone._clone && mode !== undefined) {\n                    if (mode) {\n                        clone = clone.getRule();\n                        nst && $set(origin, key, {});\n                    } else {\n                        $set(origin, key, clone._clone());\n                        continue;\n                    }\n                } else {\n                    nst && $set(origin, key, {});\n                }\n                origin[key] = deepExtend(origin[key], clone, mode);\n            } else {\n                $set(origin, key, clone);\n                if (!is.Undef(clone)) {\n                    if (!is.Undef(clone.__json)) {\n                        origin[key].__json = clone.__json;\n                    }\n                    if (!is.Undef(clone.__origin)) {\n                        origin[key].__origin = clone.__origin;\n                    }\n                }\n            }\n        }\n    }\n    return (mode !== undefined && Array.isArray(origin)) ? origin.filter(v => !v || !v.__ctrl) : origin\n}\n\nexport function deepCopy(value) {\n    return deepExtend({}, {value}).value;\n}\n\nexport function deepExtendArgs(origin, ...lst) {\n    lst.forEach(target => {\n        origin = deepExtend(origin, target);\n    });\n    return origin;\n}\n", "export function format(type, msg, rule) {\n    return (`[form-create ${type}]: ${msg}` + (rule ? ('\\n\\nrule: ' + JSON.stringify(rule.getRule ? rule.getRule() : rule)) : ''))\n}\n\nexport function tip(msg, rule) {\n    console.warn(format('tip', msg, rule));\n}\n\nexport function err(msg, rule) {\n    console.error(format('err', msg, rule));\n}\n\nexport function logError(e) {\n    err(e.toString());\n    console.error(e);\n}\n", "import deepExtend from './deepextend';\nimport {err} from './console';\nimport is from './type';\n\nconst PREFIX = '[[FORM-CREATE-PREFIX-';\nconst SUFFIX = '-FORM-CREATE-SUFFIX]]';\n\nconst $T = '$FN:';\nconst $TX = '$FNX:';\nconst $ON = '$GLOBAL:';\nconst FUNCTION = 'function';\n\nexport function toJson(obj, space) {\n    return JSON.stringify(deepExtend(Array.isArray(obj) ? [] : {}, obj, true), function (key, val) {\n        if (val && val._isVue === true)\n            return undefined;\n\n        if (typeof val !== FUNCTION) {\n            return val;\n        }\n        if (val.__json) {\n            return val.__json;\n        }\n        if (val.__origin)\n            val = val.__origin;\n\n        if (val.__emit)\n            return undefined;\n        return PREFIX + val + SUFFIX;\n    }, space);\n}\n\nfunction makeFn(fn) {\n    return (new Function('return ' + fn))();\n}\n\nexport function parseFn(fn, mode) {\n    if (fn && is.String(fn) && fn.length > 4) {\n        let v = fn.trim();\n        let flag = false;\n        try {\n            if (v.indexOf(SUFFIX) > 0 && v.indexOf(PREFIX) === 0) {\n                v = v.replace(SUFFIX, '').replace(PREFIX, '');\n                flag = true;\n            } else if (v.indexOf($T) === 0) {\n                v = v.replace($T, '');\n                flag = true;\n            } else if (v.indexOf($ON) === 0) {\n                const name = v.replace($ON, '');\n                v = function (...args) {\n                    const callback = args[0].api.getGlobalEvent(name);\n                    if (callback) {\n                        return callback.call(this, ...args);\n                    }\n                    return undefined;\n                }\n                v.__json = fn;\n                v.__inject = true;\n                return v;\n            } else if (v.indexOf($TX) === 0) {\n                v = makeFn('function($inject){' + v.replace($TX, '') + '}');\n                v.__json = fn;\n                v.__inject = true;\n                return v;\n            } else if (!mode && v.indexOf(FUNCTION) === 0 && v !== FUNCTION) {\n                flag = true;\n            }\n            if (!flag) return fn;\n            const val = makeFn((v.indexOf(FUNCTION) === -1 && v.indexOf('(') !== 0) ? (FUNCTION + ' ' + v) : v);\n            val.__json = fn;\n            return val;\n        } catch (e) {\n            err(`解析失败:${v}\\n\\nerr: ${e}`);\n            return undefined;\n        }\n    }\n    return fn;\n}\n\nexport function parseJson(json, mode) {\n    return JSON.parse(json, function (k, v) {\n        if (is.Undef(v) || !v.indexOf) return v;\n        return parseFn(v, mode);\n    });\n}\n"], "mappings": ";;;;;;AACO,SAAS,KAAK,QAAQ,OAAO,OAAO;AACvC,SAAO,KAAK,IAAI;AACpB;;;ACAe,SAAR,WAA4B,QAAQ,SAAS,CAAC,GAAG,MAAM;AAC1D,MAAI,QAAQ;AACZ,WAAS,OAAO,QAAQ;AACpB,QAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AACnD,UAAI,QAAQ,OAAO,GAAG;AACtB,WAAK,QAAQ,MAAM,QAAQ,KAAK,MAAM,aAAG,OAAO,KAAK,GAAG;AACpD,YAAI,MAAM,OAAO,GAAG,MAAM;AAC1B,YAAI,OAAO;AACP,kBAAQ;AACR,iBAAO,KAAK,QAAQ,KAAK,CAAC,CAAC;AAAA,QAC/B,WAAW,MAAM,UAAU,SAAS,QAAW;AAC3C,cAAI,MAAM;AACN,oBAAQ,MAAM,QAAQ;AACtB,mBAAO,KAAK,QAAQ,KAAK,CAAC,CAAC;AAAA,UAC/B,OAAO;AACH,iBAAK,QAAQ,KAAK,MAAM,OAAO,CAAC;AAChC;AAAA,UACJ;AAAA,QACJ,OAAO;AACH,iBAAO,KAAK,QAAQ,KAAK,CAAC,CAAC;AAAA,QAC/B;AACA,eAAO,GAAG,IAAI,WAAW,OAAO,GAAG,GAAG,OAAO,IAAI;AAAA,MACrD,OAAO;AACH,aAAK,QAAQ,KAAK,KAAK;AACvB,YAAI,CAAC,aAAG,MAAM,KAAK,GAAG;AAClB,cAAI,CAAC,aAAG,MAAM,MAAM,MAAM,GAAG;AACzB,mBAAO,GAAG,EAAE,SAAS,MAAM;AAAA,UAC/B;AACA,cAAI,CAAC,aAAG,MAAM,MAAM,QAAQ,GAAG;AAC3B,mBAAO,GAAG,EAAE,WAAW,MAAM;AAAA,UACjC;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,SAAQ,SAAS,UAAa,MAAM,QAAQ,MAAM,IAAK,OAAO,OAAO,OAAK,CAAC,KAAK,CAAC,EAAE,MAAM,IAAI;AACjG;;;ACvCO,SAAS,OAAO,MAAM,KAAK,MAAM;AACpC,SAAQ,gBAAgB,UAAU,SAAS,OAAQ,eAAe,KAAK,UAAU,KAAK,UAAU,KAAK,QAAQ,IAAI,IAAI,IAAK;AAC9H;AAMO,SAAS,IAAI,KAAK,MAAM;AAC3B,UAAQ,MAAM,OAAO,OAAO,KAAK,IAAI,CAAC;AAC1C;;;ACNA,IAAM,SAAS;AACf,IAAM,SAAS;AAEf,IAAM,KAAK;AACX,IAAM,MAAM;AACZ,IAAM,MAAM;AACZ,IAAM,WAAW;AAEV,SAAS,OAAO,KAAK,OAAO;AAC/B,SAAO,KAAK,UAAU,WAAW,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,KAAK,IAAI,GAAG,SAAU,KAAK,KAAK;AAC3F,QAAI,OAAO,IAAI,WAAW;AACtB,aAAO;AAEX,QAAI,OAAO,QAAQ,UAAU;AACzB,aAAO;AAAA,IACX;AACA,QAAI,IAAI,QAAQ;AACZ,aAAO,IAAI;AAAA,IACf;AACA,QAAI,IAAI;AACJ,YAAM,IAAI;AAEd,QAAI,IAAI;AACJ,aAAO;AACX,WAAO,SAAS,MAAM;AAAA,EAC1B,GAAG,KAAK;AACZ;AAEA,SAAS,OAAO,IAAI;AAChB,SAAQ,IAAI,SAAS,YAAY,EAAE,EAAG;AAC1C;AAEO,SAAS,QAAQ,IAAI,MAAM;AAC9B,MAAI,MAAM,aAAG,OAAO,EAAE,KAAK,GAAG,SAAS,GAAG;AACtC,QAAI,IAAI,GAAG,KAAK;AAChB,QAAI,OAAO;AACX,QAAI;AACA,UAAI,EAAE,QAAQ,MAAM,IAAI,KAAK,EAAE,QAAQ,MAAM,MAAM,GAAG;AAClD,YAAI,EAAE,QAAQ,QAAQ,EAAE,EAAE,QAAQ,QAAQ,EAAE;AAC5C,eAAO;AAAA,MACX,WAAW,EAAE,QAAQ,EAAE,MAAM,GAAG;AAC5B,YAAI,EAAE,QAAQ,IAAI,EAAE;AACpB,eAAO;AAAA,MACX,WAAW,EAAE,QAAQ,GAAG,MAAM,GAAG;AAC7B,cAAM,OAAO,EAAE,QAAQ,KAAK,EAAE;AAC9B,YAAI,YAAa,MAAM;AACnB,gBAAM,WAAW,KAAK,CAAC,EAAE,IAAI,eAAe,IAAI;AAChD,cAAI,UAAU;AACV,mBAAO,SAAS,KAAK,MAAM,GAAG,IAAI;AAAA,UACtC;AACA,iBAAO;AAAA,QACX;AACA,UAAE,SAAS;AACX,UAAE,WAAW;AACb,eAAO;AAAA,MACX,WAAW,EAAE,QAAQ,GAAG,MAAM,GAAG;AAC7B,YAAI,OAAO,uBAAuB,EAAE,QAAQ,KAAK,EAAE,IAAI,GAAG;AAC1D,UAAE,SAAS;AACX,UAAE,WAAW;AACb,eAAO;AAAA,MACX,WAAW,CAAC,QAAQ,EAAE,QAAQ,QAAQ,MAAM,KAAK,MAAM,UAAU;AAC7D,eAAO;AAAA,MACX;AACA,UAAI,CAAC;AAAM,eAAO;AAClB,YAAM,MAAM,OAAQ,EAAE,QAAQ,QAAQ,MAAM,MAAM,EAAE,QAAQ,GAAG,MAAM,IAAM,WAAW,MAAM,IAAK,CAAC;AAClG,UAAI,SAAS;AACb,aAAO;AAAA,IACX,SAAS,GAAP;AACE,UAAI,QAAQ;AAAA;AAAA,OAAa,GAAG;AAC5B,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AAEO,SAAS,UAAU,MAAM,MAAM;AAClC,SAAO,KAAK,MAAM,MAAM,SAAU,GAAG,GAAG;AACpC,QAAI,aAAG,MAAM,CAAC,KAAK,CAAC,EAAE;AAAS,aAAO;AACtC,WAAO,QAAQ,GAAG,IAAI;AAAA,EAC1B,CAAC;AACL;", "names": []}