import request from '@/utils/request';
import { AxiosPromise } from 'axios';
// 获取讲者列表
export const getSpeakerList = (query: any): AxiosPromise<any> => {
  return request({
    url: '/mdm/speaker/list',
    method: 'post',
    params: query,
    data: {} // post请求必须加data
  });
};

// 获取讲者审批列表
export const getSpeakerApprovalList = (query: any): AxiosPromise<any> => {
  return request({
    url: '/mdm/bpm/list',
    method: 'post',
    params: query,
    data: {} // post请求必须加data
  });
};

// 获取字典
export const setDictList = (type: string): AxiosPromise<any> => {
  return request({
    url: `/plt/dict/data/list?dictType=${type}`,
    method: 'get'
  });
};
