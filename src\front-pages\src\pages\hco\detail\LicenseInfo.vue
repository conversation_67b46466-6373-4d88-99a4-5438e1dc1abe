<template>
  <div class="license-info">
    <el-row :gutter="24">
      <!-- 医疗机构执业许可证 -->
      <el-col :span="12">
        <div class="license-section">
          <h3 class="section-title">医疗机构执业许可证</h3>
          <div class="image-list">
            <div v-if="medicalLicenses.length" class="image-grid">
              <div
                v-for="(item, index) in medicalLicenses"
                :key="item.ossId || index"
                class="image-item"
              >
                <el-image
                  :src="item.url"
                  :preview-src-list="medicalLicenses.map(img => img.url)"
                  :initial-index="index"
                  fit="cover"
                  class="license-image"
                  :loading="item.loading"
                >
                  <template #error>
                    <div class="image-error">
                      <el-icon><Picture /></el-icon>
                      <span>加载失败</span>
                    </div>
                  </template>
                  <template #placeholder>
                    <div class="image-placeholder">
                      <el-icon><Loading /></el-icon>
                    </div>
                  </template>
                </el-image>
                <div class="image-info" v-if="item.name">
                  <span class="image-name" :title="item.name">{{ item.name }}</span>
                </div>
              </div>
            </div>
            <div v-else class="empty-placeholder">
              <el-empty description="暂无医疗机构执业许可证" />
            </div>
          </div>
        </div>
      </el-col>

      <!-- 营业执照 -->
      <!-- <el-col :span="12">
        <div class="license-section">
          <h3 class="section-title">营业执照</h3>
          <div class="image-list">
            <div v-if="businessLicenses.length" class="image-grid">
              <div
                v-for="(item, index) in businessLicenses"
                :key="item.ossId || index"
                class="image-item"
              >
                <el-image
                  :src="item.url"
                  :preview-src-list="businessLicenses.map(img => img.url)"
                  :initial-index="index"
                  fit="cover"
                  class="license-image"
                  :loading="item.loading"
                >
                  <template #error>
                    <div class="image-error">
                      <el-icon><Picture /></el-icon>
                      <span>加载失败</span>
                    </div>
                  </template>
                  <template #placeholder>
                    <div class="image-placeholder">
                      <el-icon><Loading /></el-icon>
                    </div>
                  </template>
                </el-image>
                <div class="image-info" v-if="item.name">
                  <span class="image-name" :title="item.name">{{ item.name }}</span>
                </div>
              </div>
            </div>
            <div v-else class="empty-placeholder">
              <el-empty description="暂无营业执照" />
            </div>
          </div>
        </div>
      </el-col> -->
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { Picture, Loading } from '@element-plus/icons-vue';
import { useRoute } from 'vue-router';
import { ElMessage } from 'element-plus';
import { queryDetail } from '../hco';
import { listByIds } from '@/api/system/oss';

const route = useRoute();

// 证照数据
const medicalLicenses = ref([]);
const businessLicenses = ref([]);

// 通过 ossId 获取文件详情
const getFileDetailByOssId = async (ossId) => {
  try {
    const response = await listByIds(ossId);
    if (response.data && response.data.length > 0) {
      const fileInfo = response.data[0];
      return {
        ossId: fileInfo.ossId,
        url: fileInfo.url,
        name: fileInfo.originalName || fileInfo.fileName || '未知文件',
        loading: false
      };
    }
  } catch (error) {
    console.error(`获取文件详情失败 (ossId: ${ossId}):`, error);
  }
  return null;
};

// 解析 fileList 字符串并获取文件详情
const parseFileList = async (fileListStr) => {
  try {
    const fileList = JSON.parse(fileListStr);
    const licenseImages = [];

    for (const file of fileList) {
      if (file.ossId) {
        // 先添加一个加载状态的项目
        const loadingItem = {
          ossId: file.ossId,
          url: file.url || '',
          name: file.name || '加载中...',
          loading: true
        };
        licenseImages.push(loadingItem);

        // 异步获取详细信息
        const fileDetail = await getFileDetailByOssId(file.ossId);
        if (fileDetail) {
          // 更新对应的项目
          const index = licenseImages.findIndex(item => item.ossId === file.ossId);
          if (index !== -1) {
            licenseImages[index] = fileDetail;
          }
        } else {
          // 如果获取详情失败，使用原始数据
          const index = licenseImages.findIndex(item => item.ossId === file.ossId);
          if (index !== -1) {
            licenseImages[index] = {
              ossId: file.ossId,
              url: file.url || '',
              name: file.name || '文件加载失败',
              loading: false
            };
          }
        }
      }
    }

    return licenseImages;
  } catch (error) {
    console.error('解析 fileList 失败:', error);
    return [];
  }
};

// 获取证照数据的方法
const getLicenseInfo = async () => {
  try {
    const insCode = route.query.insCode || route.query.insDsSpecCode;
    if (!insCode) {
      console.warn('缺少机构编码');
      return;
    }

    // 获取机构详情
    const response = await queryDetail(insCode);
    if (response.code === 200 && response.data) {
      const institutionData = response.data;

      // 从 drugstoreFeature.extendInfo 获取 fileList
      const extendInfo = institutionData.drugstoreFeature?.extendInfo ? JSON.parse(institutionData.drugstoreFeature?.extendInfo) : null;
      if (extendInfo && extendInfo.fileList) {
        const allLicenseImages = await parseFileList(extendInfo.fileList);

        // 根据文件名或其他标识进行分类
        medicalLicenses.value = allLicenseImages.filter(item =>
          item.name.includes('医疗') ||
          item.name.includes('执业许可证') ||
          item.name.includes('medical') ||
          item.name.includes('license')
        );

        businessLicenses.value = allLicenseImages.filter(item =>
          item.name.includes('营业执照') ||
          item.name.includes('business') ||
          item.name.includes('营业')
        );

        // 如果没有明确分类，将所有证照显示在医疗许可证中
        if (medicalLicenses.value.length === 0 && businessLicenses.value.length === 0) {
          medicalLicenses.value = allLicenseImages;
        }
      } else {
        console.log('未找到 fileList 数据');
        medicalLicenses.value = [];
        businessLicenses.value = [];
      }
    } else {
      ElMessage.error(response.msg || '获取机构详情失败');
    }
  } catch (error) {
    console.error('获取证照信息失败:', error);
    ElMessage.error('获取证照信息失败');
  }
};

onMounted(() => {
  getLicenseInfo();
});
</script>

<style lang="scss" scoped>
.license-info {
  .license-section {
    background-color: #fff;
    border-radius: 4px;
    padding: 16px;
    height: 100%;

    .section-title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      margin-bottom: 16px;
    }

    .image-list {
      min-height: 200px;

      .image-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 16px;
      }

      .image-item {
        position: relative;
        border-radius: 4px;
        overflow: hidden;
        border: 1px solid #eee;
        transition: all 0.3s;

        &:hover {
          transform: scale(1.02);
          box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }

        .license-image {
          width: 100%;
          height: 200px;
          border-radius: 4px;
          cursor: pointer;
        }

        .image-info {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
          padding: 8px;
          color: white;

          .image-name {
            font-size: 12px;
            display: block;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }

      .image-error,
      .image-placeholder {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        color: #909399;
        background: #f5f7fa;

        .el-icon {
          font-size: 24px;
          margin-bottom: 8px;
        }
      }
    }

    .empty-placeholder {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;
    }
  }
}
</style>
